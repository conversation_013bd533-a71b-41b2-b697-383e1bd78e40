"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Settings, 
  Save, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Info,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  RefreshCw,
  Globe,
  Download,
  Wand2
} from "lucide-react";
import { toast } from "sonner";

interface AnalyticsConfig {
  provider: 'google' | 'plausible' | 'umami';
  api_key: string;
  website_id: string;
  base_url?: string;
  domain: string;
  isActive: boolean;
  lastTested?: string;
  testStatus?: 'success' | 'error' | 'pending';
}

interface AnalyticsConfigDialogProps {
  project_id: string;
  projectDomain: string;
  configs: AnalyticsConfig[];
  onConfigsUpdate: (configs: AnalyticsConfig[]) => void;
}

export function AnalyticsConfigDialog({ 
  project_id, 
  projectDomain, 
  configs, 
  onConfigsUpdate 
}: AnalyticsConfigDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
  const [editingConfigs, setEditingConfigs] = useState<AnalyticsConfig[]>([]);
  const [fetchingWebsites, setFetchingWebsites] = useState<Record<string, boolean>>({});
  const [availableWebsites, setAvailableWebsites] = useState<Record<string, Array<{id: string, name: string, domain?: string}>>>({});
  const [fetchingConfigs, setFetchingConfigs] = useState(false);
  const [availableConfigs, setAvailableConfigs] = useState<Array<{
    id: string;
    configName: string;
    api_key: string;
    website_id: string;
    base_url: string;
    domain: string;
    projectId: string;
    projectIds?: string[];
    created_at: string;
  }>>([]);

  // Load saved configurations from database
  const loadSavedConfigs = async () => {
    try {
      const response = await fetch(`/api/projects/${project_id}/analytics-config`);
      if (response.ok) {
        const data = await response.json();
        console.log('Loaded analytics configs:', data);
        
        if (data.configs && data.configs.length > 0) {
          console.log('Found saved configs:', data.configs);
          // Create base configs with defaults
          const baseConfigs = [
            {
              provider: 'google' as const,
              api_key: '',
              website_id: '',
              domain: projectDomain,
              isActive: false
            },
            {
              provider: 'plausible' as const,
              api_key: '',
              website_id: '',
              domain: projectDomain,
              isActive: false
            },
            {
              provider: 'umami' as const,
              api_key: '',
              website_id: '',
              base_url: '',
              domain: projectDomain,
              isActive: false
            }
          ];

          // Update with saved data
          const updatedConfigs = baseConfigs.map(baseConfig => {
            const savedConfig = data.configs.find((c: any) => c.provider === baseConfig.provider);
            
            if (savedConfig) {
              return {
                ...baseConfig,
                api_key: savedConfig.config_data?.api_key || '', // Load actual API key for editing
                website_id: savedConfig.website_id || '',
                base_url: savedConfig.config_data?.base_url || baseConfig.base_url || '',
                isActive: savedConfig.isActive || false, // Use actual active status from DB
                testStatus: 'success' as const,
                lastTested: savedConfig.updated_at
              };
            }
            
            return baseConfig;
          });
          
          console.log('Updated configs to be set:', updatedConfigs);
          setEditingConfigs(updatedConfigs);
        } else {
          // If no saved configs, set default empty configs
          const defaultConfigs = [
            {
              provider: 'google' as const,
              api_key: '',
              website_id: '',
              domain: projectDomain,
              isActive: false
            },
            {
              provider: 'plausible' as const,
              api_key: '',
              website_id: '',
              domain: projectDomain,
              isActive: false
            },
            {
              provider: 'umami' as const,
              api_key: '',
              website_id: '',
              base_url: '',
              domain: projectDomain,
              isActive: false
            }
          ];
          setEditingConfigs(defaultConfigs);
        }
      } else {
        // If request failed, set default empty configs
        const defaultConfigs = [
          {
            provider: 'google' as const,
            api_key: '',
            website_id: '',
            domain: projectDomain,
            isActive: false
          },
          {
            provider: 'plausible' as const,
            api_key: '',
            website_id: '',
            domain: projectDomain,
            isActive: false
          },
          {
            provider: 'umami' as const,
            api_key: '',
            website_id: '',
            base_url: '',
            domain: projectDomain,
            isActive: false
          }
        ];
        setEditingConfigs(defaultConfigs);
      }
    } catch (error) {
      console.error('Failed to load saved analytics configs:', error);
      // Set default empty configs on error
      const defaultConfigs = [
        {
          provider: 'google' as const,
          api_key: '',
          website_id: '',
          domain: projectDomain,
          isActive: false
        },
        {
          provider: 'plausible' as const,
          api_key: '',
          website_id: '',
          domain: projectDomain,
          isActive: false
        },
        {
          provider: 'umami' as const,
          api_key: '',
          website_id: '',
          base_url: '',
          domain: projectDomain,
          isActive: false
        }
      ];
      setEditingConfigs(defaultConfigs);
    }
  };

  // Load configs when dialog opens and reset when closed
  React.useEffect(() => {
    if (open) {
      loadSavedConfigs();
    } else {
      // Reset state when dialog closes
      setTestingProvider(null);
      setShowApiKeys({});
      setEditingConfigs([]);
      setFetchingWebsites({});
      setAvailableWebsites({});
      setFetchingConfigs(false);
      setAvailableConfigs([]);
    }
  }, [open, projectDomain]);

  const providerInfo = {
    plausible: {
      name: "Plausible Analytics",
      icon: "📈",
      description: "轻量级、隐私友好的网站分析",
      fields: {
        api_key: "API Token",
        website_id: "网站域名",
        base_url: "实例URL (自托管时填写)"
      },
      helpUrl: "https://plausible.io/docs/stats-api"
    },
    google: {
      name: "Google Analytics(❌)",
      icon: "📊",
      description: "Google Analytics 4 (GA4) 数据API",
      fields: {
        api_key: "服务账号密钥 (JSON)",
        website_id: "Property ID",
        base_url: "API端点 (可选)"
      },
      helpUrl: "https://developers.google.com/analytics/devguides/reporting/data/v1"
    },
    umami: {
      name: "Umami Analytics(❌)",
      icon: "🔍",
      description: "开源的网站分析平台",
      fields: {
        api_key: "API Token",
        website_id: "Website ID",
        base_url: "Umami实例URL"
      },
      helpUrl: "https://umami.is/docs/api"
    }
  };

  const handleConfigChange = (provider: string, field: string, value: string | boolean) => {
    setEditingConfigs(prev => 
      prev.map(config => 
        config.provider === provider 
          ? { ...config, [field]: value }
          : config
      )
    );
  };

  const handleTestConnection = async (provider: string) => {
    setTestingProvider(provider);
    const config = editingConfigs.find(c => c.provider === provider);
    
    if (!config) return;

    try {
      // Call project-specific API to test connection
      const response = await fetch(`/api/projects/${project_id}/analytics-config/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: config.provider,
          api_key: config.api_key,
          website_id: config.website_id,
          base_url: config.base_url,
          domain: config.domain
        })
      });

      const result = await response.json();
      
      const updatedConfigs = editingConfigs.map(c => 
        c.provider === provider 
          ? { 
              ...c, 
              testStatus: (result.success ? 'success' : 'error') as 'success' | 'error' | 'pending',
              lastTested: new Date().toISOString()
            }
          : c
      );
      
      setEditingConfigs(updatedConfigs);
      
      if (result.success) {
        toast.success(`${providerInfo[provider as keyof typeof providerInfo].name} 连接测试成功`);
        // Show additional info if available
        if (result.data) {
          console.log('Connection test data:', result.data);
        }
      } else {
        toast.error(`${providerInfo[provider as keyof typeof providerInfo].name} 连接测试失败: ${result.error}`);
      }
    } catch (error: any) {
      const updatedConfigs = editingConfigs.map(c => 
        c.provider === provider 
          ? { 
              ...c, 
              testStatus: 'error' as 'success' | 'error' | 'pending',
              lastTested: new Date().toISOString()
            }
          : c
      );
      
      setEditingConfigs(updatedConfigs);
      toast.error(`连接测试失败: ${error.message || '网络错误'}`);
    } finally {
      setTestingProvider(null);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Save each active configuration to the database
      const savePromises = editingConfigs
        .filter(config => config.isActive && config.api_key && config.website_id)
        .map(async (config) => {
          const response = await fetch(`/api/projects/${project_id}/analytics-config`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              provider: config.provider,
              configName: `${config.provider.charAt(0).toUpperCase() + config.provider.slice(1)} Analytics - ${projectDomain}`,
              configData: {
                api_key: config.api_key,
                website_id: config.website_id,
                base_url: config.base_url,
                domain: config.domain
              },
              testConnection: false // Don't test again since we may have already tested
            })
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(`${config.provider}: ${error.error || 'Save failed'}`);
          }

          return response.json();
        });

      // Wait for all saves to complete
      await Promise.all(savePromises);
      
      // Call the parent callback to update the UI
      onConfigsUpdate(editingConfigs);
      setOpen(false);
      toast.success("配置已保存到数据库");
    } catch (error: any) {
      console.error("Save error:", error);
      toast.error(`保存失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const toggleApiKeyVisibility = (provider: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("已复制到剪贴板");
  };

  const fetchUserPlausibleConfigs = async () => {
    setFetchingConfigs(true);
    
    try {
      const response = await fetch(`/api/user-configs/analytics/plausible`);
      const result = await response.json();

      if (result.success && result.configs) {
        setAvailableConfigs(result.configs);
        toast.success(`成功获取到 ${result.configs.length} 个已保存的 Plausible 配置`);
      } else {
        toast.error(`获取配置失败: ${result.error || '未知错误'}`);
      }
    } catch (error: any) {
      console.error('Error fetching user Plausible configs:', error);
      toast.error(`获取配置失败: ${error.message || '网络错误'}`);
    } finally {
      setFetchingConfigs(false);
    }
  };

  const fillConfigFromSaved = (configData: typeof availableConfigs[0]) => {
    handleConfigChange('plausible', 'api_key', configData.api_key);
    handleConfigChange('plausible', 'website_id', projectDomain);
    handleConfigChange('plausible', 'base_url', configData.base_url);
    // Keep current project domain instead of saved domain
    handleConfigChange('plausible', 'domain', projectDomain);
    
    // Clear available configs after selection
    setAvailableConfigs([]);
    
    toast.success(`已自动填充配置: ${configData.configName}`);
  };

  const fetchWebsitesList = async (provider: string) => {
    const config = editingConfigs.find(c => c.provider === provider);
    if (!config || !config.api_key) {
      toast.error("请先输入API Token");
      return;
    }

    // For Plausible, base_url is optional
    // For Umami, base_url is required
    if (provider === 'umami' && !config.base_url) {
      toast.error("请先输入Umami实例URL");
      return;
    }

    setFetchingWebsites(prev => ({ ...prev, [provider]: true }));

    try {
      const response = await fetch(`/api/projects/${project_id}/analytics-config/websites`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: provider,
          api_key: config.api_key,
          base_url: config.base_url
        })
      });

      const result = await response.json();

      if (result.success && result.websites) {
        setAvailableWebsites(prev => ({
          ...prev,
          [provider]: result.websites
        }));
        toast.success(`成功获取到 ${result.websites.length} 个网站`);
      } else {
        // 特殊处理不同平台的错误情况
        if (provider === 'plausible' && config.base_url) {
          toast.error(`获取网站列表失败: ${result.error || '未知错误'}。注意：Plausible 自部署版本可能不支持网站列表API，请手动输入网站域名。`);
        } else if (provider === 'umami') {
          // 检查是否是 404 错误
          if (result.error && (result.error.includes('404') || result.error.includes('端点不存在'))) {
            toast.error(`Umami API 端点不可用: ${result.error}。\n请检查：1) Umami 版本是否支持 API  2) 实例 URL 是否正确  3) API 功能是否已启用`);
          } else if (result.error && result.error.includes('认证失败')) {
            toast.error(`Umami 认证失败: ${result.error}。\n请尝试：1) 检查 API Token  2) 使用用户名:密码格式  3) 确认账户权限`);
          } else {
            toast.error(`获取 Umami 网站列表失败: ${result.error || '未知错误'}`);
          }
        } else {
          toast.error(`获取网站列表失败: ${result.error || '未知错误'}`);
        }
      }
    } catch (error: any) {
      console.error('Error fetching websites:', error);
      // 特殊处理不同平台的网络错误情况
      if (provider === 'plausible' && config.base_url) {
        toast.error(`获取网站列表失败: ${error.message || '网络错误'}。注意：Plausible 自部署版本可能不支持网站列表API，请手动输入网站域名。`);
      } else if (provider === 'umami') {
        toast.error(`获取 Umami 网站列表失败: ${error.message || '网络错误'}。\n请检查网络连接和 Umami 实例状态。`);
      } else {
        toast.error(`获取网站列表失败: ${error.message || '网络错误'}`);
      }
    } finally {
      setFetchingWebsites(prev => ({ ...prev, [provider]: false }));
    }
  };

  const getConfigForProvider = (provider: string): AnalyticsConfig => {
    return editingConfigs.find(c => c.provider === provider) || {
      provider: provider as 'google' | 'plausible' | 'umami',
      api_key: '',
      website_id: '',
      base_url: '',
      domain: projectDomain,
      isActive: false,
      lastTested: undefined,
      testStatus: undefined
    };
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          配置分析平台
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            分析平台配置
            <Badge variant="outline">{projectDomain}</Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="google" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            {Object.entries(providerInfo).map(([key, info]) => (
              <TabsTrigger 
                key={key} 
                value={key} 
                className="flex items-center gap-2"
                disabled={key === 'umami' || key === "google"}
              >
                <span>{info.icon}</span>
                {info.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(providerInfo).map(([providerKey, info]) => {
            const config = getConfigForProvider(providerKey);
            const isVisible = showApiKeys[providerKey] || false;
            
            return (
              <TabsContent key={providerKey} value={providerKey} className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span>{info.icon}</span>
                        {info.name}
                        {config.isActive && <Badge className="bg-green-100 text-green-800">已启用</Badge>}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(info.helpUrl, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4" />
                        文档
                      </Button>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">{info.description}</p>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Auto-fill for Plausible */}
                    {providerKey === 'plausible' && (
                      <div className="space-y-2 pb-4 border-b border-muted">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">快速配置</Label>
                          <div className="flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={fetchUserPlausibleConfigs}
                              disabled={fetchingConfigs}
                              className="flex items-center gap-2"
                            >
                              {fetchingConfigs ? (
                                <RefreshCw className="w-4 h-4 animate-spin" />
                              ) : (
                                <Download className="w-4 h-4" />
                              )}
                              {fetchingConfigs ? "Fetching..." : "Fetch saved config"}
                            </Button>
                          </div>
                        </div>
                        
                        {/* Available Configs Selection */}
                        {availableConfigs.length > 0 && (
                          <div className="space-y-2">
                            <Label className="text-xs text-muted-foreground">
                              选择一个配置自动填充（域名将自动设为当前项目域名 {projectDomain}）：
                            </Label>
                            <div className="grid gap-2">
                              {availableConfigs.map((savedConfig) => (
                                <div
                                  key={savedConfig.id}
                                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                                >
                                  <div className="flex-1">
                                    <div className="font-medium text-sm">{savedConfig.configName}</div>
                                    <div className="text-xs text-muted-foreground">
                                      域名: {savedConfig.domain} • 网站ID: {savedConfig.website_id}
                                      {savedConfig.base_url && savedConfig.base_url !== 'https://plausible.io' && ` • 自定义URL`}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {savedConfig.projectIds && savedConfig.projectIds.length > 1 ? (
                                        `使用于 ${savedConfig.projectIds.length} 个项目`
                                      ) : savedConfig.projectId ? (
                                        `项目: ${savedConfig.projectId}`
                                      ) : (
                                        '全局配置'
                                      )} • 创建时间: {new Date(savedConfig.created_at).toLocaleString('zh-CN')}
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => fillConfigFromSaved(savedConfig)}
                                    className="flex items-center gap-1"
                                  >
                                    <Wand2 className="w-3 h-3" />
                                    使用
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {availableConfigs.length === 0 && !fetchingConfigs && (
                          <div className="text-xs text-muted-foreground bg-blue-50 p-3 rounded border border-blue-200">
                            <Info className="w-3 h-3 inline mr-1" />
                            点击上方按钮获取已保存的 Plausible 配置。如果您在其他项目中已配置过 Plausible，可以在此处快速复用相同的 API Token 和设置。
                          </div>
                        )}
                      </div>
                    )}

                    {/* API Key */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        {info.fields.api_key}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            toggleApiKeyVisibility(providerKey);
                          }}
                        >
                          {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </Button>
                      </Label>
                      <div className="flex gap-2">
                        {providerKey === 'google' ? (
                          <Textarea
                            value={config.api_key}
                            onChange={(e) => handleConfigChange(providerKey, 'api_key', e.target.value)}
                            placeholder="粘贴服务账号JSON密钥..."
                            className="font-mono text-sm"
                            rows={4}
                          />
                        ) : (
                          <Input
                            type={isVisible ? "text" : "password"}
                            value={config.api_key}
                            onChange={(e) => handleConfigChange(providerKey, 'api_key', e.target.value)}
                            placeholder="输入API Token..."
                            className="flex-1"
                            autoComplete="off"
                          />
                        )}
                        {config.api_key && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(config.api_key)}
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Base URL (for Plausible self-hosted and Umami) - Move this before Website ID */}
                    {(providerKey === 'plausible' || providerKey === 'umami') && (
                      <div className="space-y-2">
                        <Label>{info.fields.base_url}</Label>
                        <Input
                          value={config.base_url || ''}
                          onChange={(e) => handleConfigChange(providerKey, 'base_url', e.target.value)}
                          placeholder={
                            providerKey === 'plausible' 
                              ? "留空使用 plausible.io，自托管时填写：https://your-plausible.com"
                              : "例如: https://analytics.example.com"
                          }
                        />
                      </div>
                    )}

                    {/* Fetch Websites Button (for Plausible and Umami) */}
                    {(providerKey === 'plausible' || providerKey === 'umami') && (
                      <div className="space-y-2 pt-2 pb-2 border-b border-muted">
                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => fetchWebsitesList(providerKey)}
                            disabled={!config.api_key || fetchingWebsites[providerKey] || (providerKey === 'umami' && !config.base_url)}
                            className="flex items-center gap-2"
                          >
                            {fetchingWebsites[providerKey] ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : (
                              <Globe className="w-4 h-4" />
                            )}
                            {fetchingWebsites[providerKey] ? "获取中..." : "获取网站列表"}
                          </Button>
                          {availableWebsites[providerKey] && availableWebsites[providerKey].length > 0 && (
                            <span className="text-sm text-muted-foreground">
                              已获取 {availableWebsites[providerKey].length} 个网站
                            </span>
                          )}
                        </div>
                        {/* Plausible 自部署版本提示 */}
                        {providerKey === 'plausible' && config.base_url && (
                          <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                            <Info className="w-3 h-3 inline mr-1" />
                            注意：Plausible 自部署版本可能不支持网站列表API，如果获取失败请手动输入网站域名
                          </div>
                        )}
                        
                        {/* Umami 认证方法提示 */}
                        {providerKey === 'umami' && (
                          <div className="text-xs text-blue-600 bg-blue-50 p-3 rounded border border-blue-200 space-y-2">
                            <div className="font-medium flex items-center gap-1">
                              <Info className="w-3 h-3" />
                              Umami 认证方法说明：
                            </div>
                            <div className="space-y-1 text-xs">
                              <div><strong>官方 Umami Cloud：</strong> 在 API Key 字段输入生成的 API Token</div>
                              <div><strong>自部署实例（有 API Token）：</strong> 输入管理员生成的 API Token</div>
                              <div><strong>自部署实例（无 API Token）：</strong> 在 API Key 字段输入 <code className="bg-blue-100 px-1 rounded">用户名:密码</code> 格式</div>
                            </div>
                            <div className="text-xs text-blue-500 mt-2">
                              注意：部分自部署的 Umami 版本可能不支持 API 功能，请联系管理员确认
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Website ID */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        {info.fields.website_id}
                        {(providerKey === 'plausible' || providerKey === 'umami') && availableWebsites[providerKey] && availableWebsites[providerKey].length > 0 && (
                          <span className="text-xs text-muted-foreground">从下拉列表选择或手动输入</span>
                        )}
                      </Label>
                      {(providerKey === 'plausible' || providerKey === 'umami') && availableWebsites[providerKey] && availableWebsites[providerKey].length > 0 ? (
                        <Select
                          value={config.website_id}
                          onValueChange={(value) => handleConfigChange(providerKey, 'website_id', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择网站..." />
                          </SelectTrigger>
                          <SelectContent>
                            {availableWebsites[providerKey].map((website) => (
                              <SelectItem key={website.id} value={website.id}>
                                <div className="flex flex-col">
                                  <span>{website.name}</span>
                                  {website.domain && website.domain !== website.name && (
                                    <span className="text-xs text-muted-foreground">{website.domain}</span>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          value={config.website_id}
                          onChange={(e) => handleConfigChange(providerKey, 'website_id', e.target.value)}
                          placeholder={
                            providerKey === 'google' ? "例如: 123456789" :
                            providerKey === 'plausible' ? "例如: example.com" :
                            "例如: abc123-def456-ghi789"
                          }
                        />
                      )}
                    </div>

                    {/* Test Connection */}
                    <div className="flex items-center gap-4 pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestConnection(providerKey)}
                        disabled={!config.api_key || !config.website_id || testingProvider === providerKey}
                      >
                        <TestTube className="w-4 h-4 mr-2" />
                        {testingProvider === providerKey ? "测试中..." : "测试连接"}
                      </Button>
                      
                      {config.testStatus && (
                        <div className="flex items-center gap-2">
                          {config.testStatus === 'success' ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                          <span className="text-sm">
                            {config.testStatus === 'success' ? '连接成功' : '连接失败'}
                          </span>
                          {config.lastTested && (
                            <span className="text-xs text-muted-foreground">
                              {new Date(config.lastTested).toLocaleString('zh-CN')}
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Status Toggle */}
                    <div className="flex items-center gap-2 pt-2">
                      <input
                        type="checkbox"
                        id={`active-${providerKey}`}
                        checked={config.isActive}
                        onChange={(e) => handleConfigChange(providerKey, 'isActive', e.target.checked)}
                        className="w-4 h-4"
                      />
                      <Label htmlFor={`active-${providerKey}`}>
                        启用此分析平台
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                {/* Setup Instructions */}
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <strong>设置说明：</strong>
                    {providerKey === 'google' && (
                      <div className="mt-2 space-y-1">
                        <p>1. 在 Google Cloud Console 中创建服务账号</p>
                        <p>2. 启用 Google Analytics Data API</p>
                        <p>3. 下载服务账号JSON密钥文件</p>
                        <p>4. 在GA4中为服务账号邮箱添加查看权限</p>
                      </div>
                    )}
                    {providerKey === 'plausible' && (
                      <div className="mt-2 space-y-1">
                        <p>1. 登录 Plausible 账号</p>
                        <p>2. 在设置中生成 API Token</p>
                        <p>3. 确保网站已添加到 Plausible</p>
                      </div>
                    )}
                    {providerKey === 'umami' && (
                      <div className="mt-2 space-y-2">
                        <div className="font-medium">官方 Umami Cloud：</div>
                        <div className="space-y-1 ml-2">
                          <p>1. 登录 Umami Cloud 账号</p>
                          <p>2. 在设置中生成 API Token</p>
                          <p>3. 复制网站的 Website ID</p>
                        </div>
                        
                        <div className="font-medium">自部署 Umami：</div>
                        <div className="space-y-1 ml-2">
                          <p>1. 如果管理员提供了 API Token，直接使用</p>
                          <p>2. 如果没有 API Token，使用登录凭据：</p>
                          <p className="ml-4 text-sm text-muted-foreground">在 API Key 字段输入 "用户名:密码"</p>
                          <p>3. 确认 Umami 版本支持 API 功能</p>
                        </div>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              </TabsContent>
            );
          })}
        </Tabs>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setOpen(false)}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="w-4 h-4 mr-2" />
            {loading ? "保存中..." : "保存配置"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 