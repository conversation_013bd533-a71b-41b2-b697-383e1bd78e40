"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Plus, Globe, FolderOpen, Menu, X, Archive, ArchiveRestore, MoreHorizontal, Trash2, Tags, Edit2, Trash, Tag } from "lucide-react";
import { ProjectDeleteConfirmationDialog } from "./project-delete-confirmation-dialog";
import { Project, Link, ProjectCategoryStats } from "@/types/links";
import { getCategoryConfig } from "@/lib/project-categories";
import { Sidebar } from "./sidebar";
import { OverviewView } from "./overview-view";
import { AllLinksView } from "./all-links-view";
import { AnalyticsView } from "./analytics-view";
import { IntegrationsView } from "./integrations-view";
import { ProjectDetailView } from "./project-detail-view";
import { ProjectForm } from "./project-form";
import { LinkForm } from "./link-form";
import { ImportDialog } from "./import-dialog";
import DomainManagement from "./domain-management";
import { ImportPanel } from "./admin-import/ImportPanel";
import { AdminPanel } from "@/components/admin/admin-panel";
import { useTierStatus } from "@/lib/hooks/useTierStatus";
import { toast } from "sonner";
import NextLink from "next/link";

interface LinkDashboardProps {
  className?: string;
  showAdminPanel?: boolean;
}

export default function LinkDashboard({ className, showAdminPanel }: LinkDashboardProps) {
  const t = useTranslations("links");
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [links, setLinks] = useState<Link[]>([]);
  const [filteredLinks, setFilteredLinks] = useState<Link[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("overview");
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showLinkForm, setShowLinkForm] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingLink, setEditingLink] = useState<Link | null>(null);
  const [viewingProject, setViewingProject] = useState<Project | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [userCategories, setUserCategories] = useState<ProjectCategoryStats[]>([]);
  const [creatingCategoryForProject, setCreatingCategoryForProject] = useState<string | null>(null);
  const [newCategoryName, setNewCategoryName] = useState("");
  const { tierInfo, isPaidUser, canMakeDrQuery, canMakeTrafficUpdate } = useTierStatus();

  useEffect(() => {
    fetchProjects();
    fetchAllLinks();
    fetchUserCategories();
  }, []);

  useEffect(() => {
    // Since link_resources are decoupled from projects, show all links
    // Project filtering is no longer applicable in the new architecture
    setFilteredLinks(links);
  }, [links]);

  const fetchProjects = async () => {
    try {
      const response = await fetch("/api/projects");
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast.error("Failed to fetch projects");
    }
  };

  const fetchAllLinks = async () => {
    try {
      const response = await fetch("/api/links");
      if (response.ok) {
        const data = await response.json();
        setLinks(data.links || []);
      }
    } catch (error) {
      console.error("Error fetching links:", error);
      toast.error("Failed to fetch links");
    } finally {
      setLoading(false);
    }
  };

  const fetchUserCategories = async () => {
    try {
      const response = await fetch("/api/projects/categories");
      if (response.ok) {
        const data = await response.json();
        setUserCategories(data.categories || []);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const calculateStats = () => {
    const targetLinks = filteredLinks;
    const totalLinks = targetLinks.length;
    const indexedLinks = targetLinks.filter(link => link.is_indexed).length;
    
    // Calculate average DR score from links that have a DR score
    const linksWithDR = targetLinks.filter(link => link.dr_score && link.dr_score > 0);
    const avgDrScore = linksWithDR.length > 0 
      ? Math.round(linksWithDR.reduce((sum, link) => sum + (link.dr_score || 0), 0) / linksWithDR.length)
      : 0;
      
    const monthlyTraffic = targetLinks.reduce((sum, link) => sum + link.traffic, 0);

    return {
      totalLinks,
      indexedLinks,
      avgDrScore,
      monthlyTraffic
    };
  };

  const handleProjectSubmit = async (projectData: any) => {
    try {
      const url = editingProject ? `/api/projects/${editingProject.id}` : "/api/projects";
      const method = editingProject ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(projectData),
      });

      if (response.ok) {
        const data = await response.json();
        if (editingProject) {
          setProjects(projects.map(p => p.id === editingProject.id ? data.project : p));
          toast.success("Project updated successfully");
        } else {
          setProjects([...projects, data.project]);
          toast.success("Project created successfully");
        }
        setShowProjectForm(false);
        setEditingProject(null);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to save project");
      }
    } catch (error) {
      console.error("Error saving project:", error);
      toast.error("Failed to save project");
    }
  };

  const handleLinkSubmit = async (linkData: any) => {
    try {
      const url = editingLink ? `/api/links/${editingLink.id}` : "/api/links";
      const method = editingLink ? "PUT" : "POST";

      // ProjectId is optional - links can exist without projects
      if (!linkData.project_id && !editingLink) {
        linkData.project_id = null;
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(linkData),
      });

      if (response.ok) {
        const data = await response.json();
        if (editingLink) {
          setLinks(links.map(l => l.id === editingLink.id ? data.link : l));
          toast.success("Link updated successfully");
        } else {
          setLinks([...links, data.link]);
          toast.success("Link created successfully");
        }
        setShowLinkForm(false);
        setEditingLink(null);
        fetchProjects(); // Refresh project stats
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to save link");
      }
    } catch (error) {
      console.error("Error saving link:", error);
      toast.error("Failed to save link");
    }
  };

  const handleDeleteLink = async (link: Link) => {
    try {
      const response = await fetch(`/api/links/${link.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setLinks(links.filter(l => l.id !== link.id));
        toast.success("Link deleted successfully");
        fetchProjects(); // Refresh project stats
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete link");
      }
    } catch (error) {
      console.error("Error deleting link:", error);
      toast.error("Failed to delete link");
    }
  };

  // Batch update handlers for DR and traffic data
  const handleBatchUpdateDR = async () => {
    try {
      const response = await fetch("/api/cron-worker/trigger/dr", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`DR update triggered successfully: ${result.message || 'Processing started'}`);
        
        // Refresh all links data after successful update
        await fetchAllLinks();
        toast.success("Links data refreshed");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to trigger DR update");
      }
    } catch (error) {
      console.error("Error triggering DR update:", error);
      toast.error("Failed to trigger DR update");
    }
  };

  const handleBatchUpdateTraffic = async () => {
    try {
      const response = await fetch("/api/cron-worker/trigger/traffic", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`Traffic update triggered successfully: ${result.message || 'Processing started'}`);
        
        // Refresh all links data after successful update
        await fetchAllLinks();
        toast.success("Traffic data refreshed");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to trigger traffic update");
      }
    } catch (error) {
      console.error("Error triggering traffic update:", error);
      toast.error("Failed to trigger traffic update");
    }
  };

  const handleBatchDiscoverLinks = async () => {
    try {
      const response = await fetch("/api/cron-worker/trigger/links", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`External links discovery triggered successfully: ${result.message || 'Processing started'}`);
        
        // Refresh projects and links data after successful discovery
        await fetchProjects();
        await fetchAllLinks();
        toast.success("Data refreshed with newly discovered links");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to trigger external links discovery");
      }
    } catch (error) {
      console.error("Error triggering external links discovery:", error);
      toast.error("Failed to trigger external links discovery");
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        // Remove the project from the projects list
        setProjects(projects.filter(p => p.id !== projectId));
        
        // If we're currently viewing this project, go back to overview
        if (viewingProject?.id === projectId) {
          setViewingProject(null);
          setActiveSection("overview");
        }
        
        // If this was the selected project, clear the selection
        if (selectedProject?.id === projectId) {
          setSelectedProject(null);
        }
        
        toast.success("Project deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete project");
      }
    } catch (error) {
      console.error("Error deleting project:", error);
      toast.error("Failed to delete project");
    }
  };

  const handleArchiveProject = async (projectId: string, archived: boolean) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/archive`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_archived: archived })
      });

      if (response.ok) {
        const data = await response.json();
        // Update the project in the projects list
        setProjects(projects.map(p => 
          p.id === projectId 
            ? { ...p, is_archived: archived, archived_at: archived ? new Date().toISOString() : undefined }
            : p
        ));
        
        toast.success(archived ? 'Project archived successfully' : 'Project restored successfully');
      } else {
        const error = await response.json();
        toast.error(`Failed to ${archived ? 'archive' : 'restore'} project: ${error.error}`);
      }
    } catch (error) {
      console.error('Error archiving project:', error);
      toast.error(`Failed to ${archived ? 'archive' : 'restore'} project`);
    }
  };

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    if (section === "import-csv") {
      setShowImportDialog(true);
    }
    // If switching to a different section while viewing a project, exit project view
    // Also exit project view when clicking overview to return to project list
    if (viewingProject) {
      setViewingProject(null);
    }
    // Close mobile menu when section changes
    setIsMobileMenuOpen(false);
  };

  const handleAddLinkClick = () => {
    setShowLinkForm(true);
  };

  // 创建新分类并分配项目
  const handleCreateCategoryAndAssign = async (projectId: string) => {
    if (!newCategoryName.trim()) return;

    try {
      const categoryName = newCategoryName.trim();
      
      // 直接将项目分配到新分类（这会自动创建分类）
      await handleAssignProjectToCategory(projectId, categoryName);
      
      // 清理状态
      setNewCategoryName("");
      setCreatingCategoryForProject(null);
      
      toast.success(`分类 "${categoryName}" 创建成功并已分配项目`);
    } catch (error) {
      console.error("Error creating category and assigning project:", error);
      toast.error("创建分类失败");
    }
  };



  // 将项目分配到分类
  const handleAssignProjectToCategory = async (projectId: string, categoryName: string | null) => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      const response = await fetch(`/api/projects/${projectId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          ...project, 
          category: categoryName 
        }),
      });

      if (response.ok) {
        // 更新本地项目列表
        setProjects(projects.map(p => 
          p.id === projectId 
            ? { ...p, category: categoryName }
            : p
        ));
        
        // 刷新分类统计
        await fetchUserCategories();
        
        const categoryText = categoryName || "未分类";
        toast.success(`项目已分配到 "${categoryText}" 分类`);
      } else {
        toast.error("分配分类失败");
      }
    } catch (error) {
      console.error("Error assigning project to category:", error);
      toast.error("分配分类失败");
    }
  };

  const stats = calculateStats();

  const renderMainContent = () => {
    // If viewing a specific project, show project detail view only when activeSection is not specified
    // This allows sidebar navigation to work even when viewing a project
    if (viewingProject && activeSection === "overview") {
      return (
        <ProjectDetailView
          project={viewingProject}
          onBack={() => setViewingProject(null)}
          onEditLink={(link) => {
            setEditingLink(link);
            setShowLinkForm(true);
          }}
          onDeleteLink={handleDeleteLink}
          onAddLink={handleAddLinkClick}
          onProjectUpdate={(updatedProject) => {
            setViewingProject(updatedProject);
            setProjects(projects.map(p => p.id === updatedProject.id ? updatedProject : p));
          }}
          onSidebarNavigation={(section) => {
            setActiveSection(section);
            // Don't close project view, just switch sections
          }}
        />
      );
    }

    switch (activeSection) {
      case "overview":
        return (
          <OverviewView
            projects={projects}
            selectedProject={selectedProject}
            onProjectChange={(project) => {
              if (project) {
                setViewingProject(project);
                setActiveSection("overview");
              } else {
                setSelectedProject(project);
              }
            }}
            onImportClick={() => handleSectionChange("import-csv")}
            onAddLinkClick={handleAddLinkClick}
            onArchiveProject={handleArchiveProject}
            onDeleteProject={handleDeleteProject}
            stats={stats}
          />
        );

      case "all-links":
        return (
          <AllLinksView
            links={filteredLinks}
            loading={loading}
            onEditLink={(link) => {
              setEditingLink(link);
              setShowLinkForm(true);
            }}
            onDeleteLink={handleDeleteLink}
            onAddLink={handleAddLinkClick}
            onImportComplete={() => {
              fetchAllLinks();
              fetchProjects();
            }}
            onBatchUpdateDR={canMakeDrQuery ? handleBatchUpdateDR : undefined}
            onBatchUpdateTraffic={canMakeTrafficUpdate ? handleBatchUpdateTraffic : undefined}
          />
        );

      case "analytics":
        return <AnalyticsView />;

      case "integrations":
        return <IntegrationsView projects={projects} />;

      case "domain-management":
        return <DomainManagement />;

      case "batch-import":
        return <ImportPanel onBack={() => setActiveSection("overview")} />;

      case "admin-panel":
        return <NextLink href="/admin">Admin Panel</NextLink>
      
      case "projects":
        const activeProjects = projects.filter(p => !p.is_archived);
        const archivedProjects = projects.filter(p => p.is_archived);
        const hasActiveProjects = activeProjects.length > 0;
        const hasArchivedProjects = archivedProjects.length > 0;

        // 按分类分组活跃项目
        const groupedActiveProjects = activeProjects.reduce((groups, project) => {
          const category = project.category || '未分类';
          if (!groups[category]) {
            groups[category] = [];
          }
          groups[category].push(project);
          return groups;
        }, {} as Record<string, Project[]>);

        // 按分类分组已归档项目
        const groupedArchivedProjects = archivedProjects.reduce((groups, project) => {
          const category = project.category || '未分类';
          if (!groups[category]) {
            groups[category] = [];
          }
          groups[category].push(project);
          return groups;
        }, {} as Record<string, Project[]>);

        const handleProjectArchive = async (projectId: string, archived: boolean, e: React.MouseEvent) => {
          e.preventDefault();
          e.stopPropagation();
          await handleArchiveProject(projectId, archived);
        };

        const handleProjectDelete = async (projectId: string) => {
          await handleDeleteProject(projectId);
        };

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground">
                  {activeProjects.length} active project{activeProjects.length !== 1 ? 's' : ''}
                  {hasArchivedProjects && ` • ${archivedProjects.length} archived`}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button onClick={() => setShowProjectForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              </div>
            </div>
            
            {!hasActiveProjects ? (
              <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                  <FolderOpen className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No active projects</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first project to organize your links
                </p>
                <Button onClick={() => setShowProjectForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Project
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Active Projects - Grouped by Category */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Active Projects</h3>
                  {Object.entries(groupedActiveProjects)
                    .sort(([a], [b]) => {
                      // 将"未分类"排在最后
                      if (a === '未分类') return 1;
                      if (b === '未分类') return -1;
                      return a.localeCompare(b);
                    })
                    .map(([category, categoryProjects]) => {
                      const categoryConfig = getCategoryConfig(category === '未分类' ? null : category);
                      const IconComponent = categoryConfig.icon;
                      
                      return (
                        <div key={category} className="mb-6">
                          {/* Category Header */}
                          <div className="flex items-center gap-2 mb-3">
                            <div className={`p-1.5 rounded-md ${categoryConfig.bgColor}`}>
                              <IconComponent className={`h-4 w-4 ${categoryConfig.color}`} />
                            </div>
                            <h4 className="font-medium text-foreground">{category}</h4>
                            <Badge variant="outline" className="text-xs">
                              {categoryProjects.length}
                            </Badge>
                          </div>
                          
                          {/* Projects Grid */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {categoryProjects.map((project) => (
                              <Card
                                key={project.id}
                                className="cursor-pointer hover:shadow-lg hover:border-primary/50 transition-all group relative"
                                onClick={() => {
                                  setViewingProject(project);
                                  setActiveSection("overview");
                                }}
                              >
                                <CardContent className="p-4">
                                  <div className="flex items-center justify-between mb-2">
                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                                        {project.name}
                                      </h4>
                                      {project.category && (
                                        <div className="mt-1">
                                          <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                                            {(() => {
                                              const categoryConfig = getCategoryConfig(project.category);
                                              const IconComponent = categoryConfig.icon;
                                              return (
                                                <>
                                                  <IconComponent className="h-3 w-3 mr-1" />
                                                  {project.category}
                                                </>
                                              );
                                            })()}
                                          </Badge>
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Badge variant="outline" className="text-xs">
                                        {project.total_links} links
                                      </Badge>
                                      <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                            onClick={(e) => {
                                              e.preventDefault();
                                              e.stopPropagation();
                                            }}
                                          >
                                            <MoreHorizontal className="h-4 w-4" />
                                          </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                          <DropdownMenuSub>
                                            <DropdownMenuSubTrigger>
                                              <Tag className="h-4 w-4 mr-2" />
                                              分配分类
                                            </DropdownMenuSubTrigger>
                                            <DropdownMenuSubContent>
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.preventDefault();
                                                  e.stopPropagation();
                                                  handleAssignProjectToCategory(project.id, null);
                                                }}
                                              >
                                                <div className="flex items-center gap-2">
                                                  <div className="w-4 h-4 rounded border border-dashed border-gray-400 flex items-center justify-center">
                                                    <X className="h-2 w-2 text-gray-400" />
                                                  </div>
                                                  未分类
                                                  {!project.category && (
                                                    <span className="text-xs text-green-600">✓</span>
                                                  )}
                                                </div>
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              {userCategories.map((category) => {
                                                const categoryConfig = getCategoryConfig(category.category);
                                                const IconComponent = categoryConfig.icon;
                                                const isSelected = project.category === category.category;
                                                
                                                return (
                                                  <DropdownMenuItem
                                                    key={category.category}
                                                    onClick={(e) => {
                                                      e.preventDefault();
                                                      e.stopPropagation();
                                                      handleAssignProjectToCategory(project.id, category.category);
                                                    }}
                                                  >
                                                    <div className="flex items-center gap-2">
                                                      <div className={`p-1 rounded ${categoryConfig.bgColor}`}>
                                                        <IconComponent className={`h-2 w-2 ${categoryConfig.color}`} />
                                                      </div>
                                                      <span>{category.category}</span>
                                                      <span className="text-xs text-muted-foreground">({category.project_count})</span>
                                                      {isSelected && (
                                                        <span className="text-xs text-green-600">✓</span>
                                                      )}
                                                    </div>
                                                  </DropdownMenuItem>
                                                );
                                              })}
                                              
                                              {/* 创建新分类选项 */}
                                              <DropdownMenuSeparator />
                                              {creatingCategoryForProject === project.id ? (
                                                <div className="p-2 space-y-2">
                                                  <Input
                                                    placeholder="输入新分类名称"
                                                    value={newCategoryName}
                                                    onChange={(e) => setNewCategoryName(e.target.value)}
                                                    onKeyDown={(e) => {
                                                      if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        handleCreateCategoryAndAssign(project.id);
                                                      } else if (e.key === 'Escape') {
                                                        setCreatingCategoryForProject(null);
                                                        setNewCategoryName("");
                                                      }
                                                    }}
                                                    autoFocus
                                                    className="h-8 text-sm"
                                                  />
                                                  <div className="flex gap-1">
                                                    <Button
                                                      size="sm"
                                                      onClick={() => handleCreateCategoryAndAssign(project.id)}
                                                      disabled={!newCategoryName.trim()}
                                                      className="h-6 text-xs"
                                                    >
                                                      创建
                                                    </Button>
                                                    <Button
                                                      size="sm"
                                                      variant="outline"
                                                      onClick={() => {
                                                        setCreatingCategoryForProject(null);
                                                        setNewCategoryName("");
                                                      }}
                                                      className="h-6 text-xs"
                                                    >
                                                      取消
                                                    </Button>
                                                  </div>
                                                </div>
                                              ) : (
                                                <DropdownMenuItem
                                                  onClick={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    setCreatingCategoryForProject(project.id);
                                                    setNewCategoryName("");
                                                  }}
                                                >
                                                  <div className="flex items-center gap-2">
                                                    <div className="w-4 h-4 rounded border border-dashed border-primary flex items-center justify-center">
                                                      <Plus className="h-2 w-2 text-primary" />
                                                    </div>
                                                    <span className="text-primary">创建新分类...</span>
                                                  </div>
                                                </DropdownMenuItem>
                                              )}
                                            </DropdownMenuSubContent>
                                          </DropdownMenuSub>
                                          <DropdownMenuSeparator />
                                          <DropdownMenuItem
                                            onClick={(e) => handleProjectArchive(project.id, true, e)}
                                          >
                                            <Archive className="h-4 w-4 mr-2" />
                                            Archive Project
                                          </DropdownMenuItem>
                                          <ProjectDeleteConfirmationDialog
                                            project={project}
                                            onDelete={handleProjectDelete}
                                            trigger={
                                              <div className="flex items-center w-full px-2 py-1.5 text-sm text-destructive hover:text-destructive cursor-pointer rounded-sm hover:bg-accent">
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete Project
                                              </div>
                                            }
                                          />
                                        </DropdownMenuContent>
                                      </DropdownMenu>
                                    </div>
                                  </div>
                                  <p className="text-xs text-muted-foreground mb-3 truncate">
                                    {project.domain}
                                  </p>
                                  <div className="flex justify-between text-xs">
                                    <span className="text-muted-foreground">
                                      {project.indexed_links} indexed
                                    </span>
                                    <span className="text-primary font-medium group-hover:underline">
                                      View Details →
                                    </span>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                </div>

                {/* Archived Projects Section - Grouped by Category */}
                {hasArchivedProjects && (
                  <div className="mt-8 pt-6 border-t border-border/50">
                    <div className="flex items-center gap-2 mb-4">
                      <Archive className="h-4 w-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold text-muted-foreground">Archived Projects</h3>
                      <span className="text-sm text-muted-foreground">({archivedProjects.length})</span>
                    </div>
                    {Object.entries(groupedArchivedProjects)
                      .sort(([a], [b]) => {
                        // 将"未分类"排在最后
                        if (a === '未分类') return 1;
                        if (b === '未分类') return -1;
                        return a.localeCompare(b);
                      })
                      .map(([category, categoryProjects]) => {
                        const categoryConfig = getCategoryConfig(category === '未分类' ? null : category);
                        const IconComponent = categoryConfig.icon;
                        
                        return (
                          <div key={`archived-${category}`} className="mb-6">
                            {/* Category Header */}
                            <div className="flex items-center gap-2 mb-3">
                              <div className={`p-1.5 rounded-md ${categoryConfig.bgColor} opacity-60`}>
                                <IconComponent className={`h-4 w-4 ${categoryConfig.color}`} />
                              </div>
                              <h4 className="font-medium text-muted-foreground">{category}</h4>
                              <Badge variant="outline" className="text-xs opacity-75">
                                {categoryProjects.length}
                              </Badge>
                            </div>
                            
                            {/* Projects Grid */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                              {categoryProjects.map((project) => (
                                <Card
                                  key={project.id}
                                  className="cursor-pointer hover:shadow-lg hover:border-primary/50 transition-all group relative opacity-75"
                                  onClick={() => setViewingProject(project)}
                                >
                                  <CardContent className="p-4">
                                    <div className="flex items-center justify-between mb-2">
                                      <div className="flex-1 min-w-0">
                                        <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                                          {project.name}
                                        </h4>
                                        {project.category && (
                                          <div className="mt-1">
                                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5 opacity-75">
                                              {(() => {
                                                const categoryConfig = getCategoryConfig(project.category);
                                                const IconComponent = categoryConfig.icon;
                                                return (
                                                  <>
                                                    <IconComponent className="h-3 w-3 mr-1" />
                                                    {project.category}
                                                  </>
                                                );
                                              })()}
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="text-xs">
                                          {project.total_links} links
                                        </Badge>
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                              onClick={(e) => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                              }}
                                            >
                                              <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                            <DropdownMenuSub>
                                              <DropdownMenuSubTrigger>
                                                <Tag className="h-4 w-4 mr-2" />
                                                分配分类
                                              </DropdownMenuSubTrigger>
                                              <DropdownMenuSubContent>
                                                <DropdownMenuItem
                                                  onClick={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    handleAssignProjectToCategory(project.id, null);
                                                  }}
                                                >
                                                  <div className="flex items-center gap-2">
                                                    <div className="w-4 h-4 rounded border border-dashed border-gray-400 flex items-center justify-center">
                                                      <X className="h-2 w-2 text-gray-400" />
                                                    </div>
                                                    未分类
                                                    {!project.category && (
                                                      <span className="text-xs text-green-600">✓</span>
                                                    )}
                                                  </div>
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                {userCategories.map((category) => {
                                                  const categoryConfig = getCategoryConfig(category.category);
                                                  const IconComponent = categoryConfig.icon;
                                                  const isSelected = project.category === category.category;
                                                  
                                                  return (
                                                    <DropdownMenuItem
                                                      key={category.category}
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        handleAssignProjectToCategory(project.id, category.category);
                                                      }}
                                                    >
                                                      <div className="flex items-center gap-2">
                                                        <div className={`p-1 rounded ${categoryConfig.bgColor}`}>
                                                          <IconComponent className={`h-2 w-2 ${categoryConfig.color}`} />
                                                        </div>
                                                        <span>{category.category}</span>
                                                        <span className="text-xs text-muted-foreground">({category.project_count})</span>
                                                        {isSelected && (
                                                          <span className="text-xs text-green-600">✓</span>
                                                        )}
                                                      </div>
                                                    </DropdownMenuItem>
                                                  );
                                                })}
                                                
                                                {/* 创建新分类选项 */}
                                                <DropdownMenuSeparator />
                                                {creatingCategoryForProject === project.id ? (
                                                  <div className="p-2 space-y-2">
                                                    <Input
                                                      placeholder="输入新分类名称"
                                                      value={newCategoryName}
                                                      onChange={(e) => setNewCategoryName(e.target.value)}
                                                      onKeyDown={(e) => {
                                                        if (e.key === 'Enter') {
                                                          e.preventDefault();
                                                          handleCreateCategoryAndAssign(project.id);
                                                        } else if (e.key === 'Escape') {
                                                          setCreatingCategoryForProject(null);
                                                          setNewCategoryName("");
                                                        }
                                                      }}
                                                      autoFocus
                                                      className="h-8 text-sm"
                                                    />
                                                    <div className="flex gap-1">
                                                      <Button
                                                        size="sm"
                                                        onClick={() => handleCreateCategoryAndAssign(project.id)}
                                                        disabled={!newCategoryName.trim()}
                                                        className="h-6 text-xs"
                                                      >
                                                        创建
                                                      </Button>
                                                      <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={() => {
                                                          setCreatingCategoryForProject(null);
                                                          setNewCategoryName("");
                                                        }}
                                                        className="h-6 text-xs"
                                                      >
                                                        取消
                                                      </Button>
                                                    </div>
                                                  </div>
                                                ) : (
                                                  <DropdownMenuItem
                                                    onClick={(e) => {
                                                      e.preventDefault();
                                                      e.stopPropagation();
                                                      setCreatingCategoryForProject(project.id);
                                                      setNewCategoryName("");
                                                    }}
                                                  >
                                                    <div className="flex items-center gap-2">
                                                      <div className="w-4 h-4 rounded border border-dashed border-primary flex items-center justify-center">
                                                        <Plus className="h-2 w-2 text-primary" />
                                                      </div>
                                                      <span className="text-primary">创建新分类...</span>
                                                    </div>
                                                  </DropdownMenuItem>
                                                )}
                                              </DropdownMenuSubContent>
                                            </DropdownMenuSub>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                              onClick={(e) => handleProjectArchive(project.id, false, e)}
                                            >
                                              <ArchiveRestore className="h-4 w-4 mr-2" />
                                              Restore Project
                                            </DropdownMenuItem>
                                            <ProjectDeleteConfirmationDialog
                                              project={project}
                                              onDelete={handleProjectDelete}
                                              trigger={
                                                <div className="flex items-center w-full px-2 py-1.5 text-sm text-destructive hover:text-destructive cursor-pointer rounded-sm hover:bg-accent">
                                                  <Trash2 className="h-4 w-4 mr-2" />
                                                  Delete Project
                                                </div>
                                              }
                                            />
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                    </div>
                                    <p className="text-xs text-muted-foreground mb-3 truncate">
                                      {project.domain}
                                    </p>
                                    <div className="flex justify-between text-xs">
                                      <span className="text-muted-foreground">
                                        {project.indexed_links} indexed
                                      </span>
                                      <span className="text-muted-foreground/70">
                                        Archived {project.archived_at ? new Date(project.archived_at).toLocaleDateString() : ''}
                                      </span>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            )}
          </div>
        );
      
      default:
        return (
          <OverviewView
            projects={projects}
            selectedProject={selectedProject}
            onProjectChange={setSelectedProject}
            onImportClick={() => handleSectionChange("import-csv")}
            onAddLinkClick={handleAddLinkClick}
            onArchiveProject={handleArchiveProject}
            onDeleteProject={handleDeleteProject}
            stats={stats}
          />
        );
    }
  };

  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Sidebar - positioned outside container for proper overlay */}
      <div className={`
        fixed lg:hidden inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <Sidebar
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
          projectsCount={projects.length}
          linksCount={links.length}
          onClose={() => setIsMobileMenuOpen(false)}
        />
      </div>

      {/* Main Layout Container - Optimized for better space utilization */}
      <div className="mx-auto">
        <div className="flex min-h-screen">
          {/* Desktop Sidebar - More compact */}
          <div className="hidden lg:block relative w-52 xl:w-56">
            <Sidebar
              activeSection={activeSection}
              onSectionChange={handleSectionChange}
              projectsCount={projects.length}
              linksCount={links.length}
              onClose={() => setIsMobileMenuOpen(false)}
            />
          </div>

          {/* Main Content - Optimized layout */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Mobile Header with Menu Button */}
            <div className="lg:hidden flex items-center justify-between border-b border-border bg-background px-4 py-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(true)}
                className="flex items-center gap-2"
              >
                <Menu className="h-5 w-5" />
                <span>Menu</span>
              </Button>
              <h1 className="text-base font-semibold">
                {activeSection === "overview" ? "Overview" : 
                 activeSection === "all-links" ? "Links" :
                 activeSection === "projects" ? "Projects" :
                 activeSection === "domain-management" ? "Domains" :
                 activeSection === "admin-panel" ? "Admin" :
                 "Dashboard"}
              </h1>
              <div className="w-16" /> {/* Spacer for centering */}
            </div>

            {/* Desktop Page Header - More compact */}
            {!viewingProject && (
              <div className="hidden lg:block border-b border-border bg-background px-4 xl:px-5 py-2 xl:py-2.5">
                <h1 className="text-lg xl:text-xl font-bold text-foreground">
                  {activeSection === "overview" ? "Link Management Center" : 
                   activeSection === "all-links" ? "Link Resources" :
                   activeSection === "projects" ? "Projects" :
                   activeSection === "domain-management" ? "Domain Management" :
                   activeSection === "admin-panel" ? "Admin Panel" :
                   "Link Management Center"}
                </h1>
                <p className="text-xs xl:text-sm text-muted-foreground mt-0.5">
                  {activeSection === "overview" ? "Monitor and optimize your external links" :
                   activeSection === "all-links" ? `Manage all your backlinks in one place (${filteredLinks.length} total)` :
                   activeSection === "projects" ? "Organize your links by projects" :
                   activeSection === "domain-management" ? "Manage your domains, track expiry dates, and monitor WHOIS information" :
                   activeSection === "admin-panel" ? "Administrative tools and sync controls" :
                   "Monitor and optimize your external links"}
                </p>
              </div>
            )}

            {/* Main Content Area - Reduced padding for better space utilization */}
            <main className="flex-1 p-3 sm:p-4 lg:p-4 xl:p-5 overflow-y-auto">
              <div className="max-w-none">
                {renderMainContent()}
              </div>
            </main>
          </div>
        </div>
      </div>

      {/* Dialogs */}
      {showProjectForm && (
        <ProjectForm
          project={editingProject}
          onSubmit={handleProjectSubmit}
          onCancel={() => {
            setShowProjectForm(false);
            setEditingProject(null);
          }}
        />
      )}

      {showLinkForm && (
        <LinkForm
          link={editingLink}
          projects={projects}
          selectedProject={selectedProject}
          onSubmit={handleLinkSubmit}
          onCancel={() => {
            setShowLinkForm(false);
            setEditingLink(null);
          }}
        />
      )}

      {showImportDialog && (
        <ImportDialog
          project_id={selectedProject?.id || (projects.length > 0 ? projects[0].id : null)}
          onSuccess={() => {
            setShowImportDialog(false);
            fetchAllLinks();
            fetchProjects();
          }}
          onCancel={() => setShowImportDialog(false)}
        />
      )}
    </div>
  );
} 