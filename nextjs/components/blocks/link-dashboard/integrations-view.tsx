"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  Plus, 
  ExternalLink, 
  Edit, 
  Trash2,
  AlertCircle,
  CheckCircle,
  Search,
  Globe,
  Link2,
  Zap,
  RefreshCw
} from "lucide-react";
import { IntegrationForm } from "./integration-form";
import { GoogleConsoleConfig } from "./google-console-config";
import { Project } from "@/types/links";
import { Integration } from "@/types/integrations";
import { toast } from "sonner";

interface IntegrationsViewProps {
  projects: Project[];
}

interface IntegrationCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  integrations: Integration[];
  availableProviders: Array<{
    id: string;
    name: string;
    description: string;
    status: 'available' | 'coming_soon' | 'beta';
    icon: string;
  }>;
}

export function IntegrationsView({ projects }: IntegrationsViewProps) {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingIntegration, setEditingIntegration] = useState<Integration | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    fetchIntegrations();
  }, []);

  const fetchIntegrations = async () => {
    try {
      const response = await fetch("/api/integrations");
      if (response.ok) {
        const data = await response.json();
        setIntegrations(data.integrations || []);
      }
    } catch (error) {
      console.error("Error fetching integrations:", error);
      toast.error("Failed to fetch integrations");
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshIntegrations = async () => {
    setRefreshing(true);
    try {
      await fetchIntegrations();
      toast.success("Integrations refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh integrations");
    } finally {
      setRefreshing(false);
    }
  };

  const handleSubmitIntegration = async (integrationData: any) => {
    try {
      const url = editingIntegration 
        ? `/api/integrations/${editingIntegration.id}` 
        : "/api/integrations";
      const method = editingIntegration ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(integrationData),
      });

      if (response.ok) {
        const data = await response.json();
        if (editingIntegration) {
          setIntegrations(integrations.map(i => 
            i.id === editingIntegration.id ? data.integration : i
          ));
          toast.success("Integration updated successfully");
        } else {
          setIntegrations([...integrations, data.integration]);
          toast.success("Integration created successfully");
        }
        setShowForm(false);
        setEditingIntegration(null);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to save integration");
      }
    } catch (error) {
      console.error("Error saving integration:", error);
      toast.error("Failed to save integration");
    }
  };

  const handleDeleteIntegration = async (integration: Integration) => {
    if (!confirm("Are you sure you want to delete this integration?")) return;

    try {
      const response = await fetch(`/api/integrations/${integration.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setIntegrations(integrations.filter(i => i.id !== integration.id));
        toast.success("Integration deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete integration");
      }
    } catch (error) {
      console.error("Error deleting integration:", error);
      toast.error("Failed to delete integration");
    }
  };

  const getProviderInfo = (provider: string) => {
    const providers = {
      // Analytics
      // umami: {
      //   name: "Umami Analytics",
      //   description: "Self-hosted web analytics",
      //   icon: "📊",
      //   color: "bg-blue-100 text-blue-800",
      //   category: "analytics"
      // },
      plausible: {
        name: "Plausible Analytics", 
        description: "Privacy-focused analytics",
        icon: "📈",
        color: "bg-green-100 text-green-800",
        category: "analytics"
      },
      google_analytics: {
        name: "Google Analytics",
        description: "Google's web analytics platform",
        icon: "🔍",
        color: "bg-red-100 text-red-800",
        category: "analytics"
      },
      // Search Console
      google_search_console: {
        name: "Google Search Console",
        description: "Search performance and indexing data",
        icon: "🔍",
        color: "bg-blue-100 text-blue-800",
        category: "search"
      },
      bing_webmaster: {
        name: "Bing Webmaster Tools",
        description: "Bing search performance tools",
        icon: "🔍",
        color: "bg-orange-100 text-orange-800",
        category: "search"
      },
      // Backlink Detection
      ahrefs: {
        name: "Ahrefs",
        description: "Backlink analysis and SEO tools",
        icon: "🔗",
        color: "bg-purple-100 text-purple-800",
        category: "backlinks"
      },
      semrush: {
        name: "SEMrush",
        description: "SEO and marketing analytics",
        icon: "📊",
        color: "bg-orange-100 text-orange-800",
        category: "backlinks"
      },
      moz: {
        name: "Moz",
        description: "SEO tools and link analysis",
        icon: "🔍",
        color: "bg-blue-100 text-blue-800",
        category: "backlinks"
      }
    };
    return providers[provider as keyof typeof providers] || {
      name: provider,
      description: "Integration provider",
      icon: "⚡",
      color: "bg-gray-100 text-gray-800",
      category: "other"
    };
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: "default" as const, label: "Active", icon: CheckCircle },
      inactive: { variant: "secondary" as const, label: "Inactive", icon: AlertCircle },
      error: { variant: "destructive" as const, label: "Error", icon: AlertCircle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.inactive;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getIntegrationCategories = (): IntegrationCategory[] => [
    {
      id: "search",
      name: "Search Console",
      description: "Search engine webmaster tools and indexing data",
      icon: <Search className="h-5 w-5" />,
      color: "text-blue-600",
      integrations: integrations.filter(i => getProviderInfo(i.provider).category === "search"),
      availableProviders: [
        {
          id: "google_search_console",
          name: "Google Search Console",
          description: "Monitor search performance and indexing",
          status: "available",
          icon: "🔍"
        },
        {
          id: "bing_webmaster",
          name: "Bing Webmaster Tools",
          description: "Bing search performance and indexing",
          status: "coming_soon",
          icon: "🔍"
        }
      ]
    },
    {
      id: "analytics",
      name: "Analytics Platforms",
      description: "Web analytics and traffic measurement tools",
      icon: <BarChart3 className="h-5 w-5" />,
      color: "text-green-600",
      integrations: integrations.filter(i => getProviderInfo(i.provider).category === "analytics"),
      availableProviders: [
        // {
        //   id: "umami",
        //   name: "Umami Analytics",
        //   description: "Self-hosted web analytics",
        //   status: "available",
        //   icon: "📊"
        // },
        {
          id: "plausible",
          name: "Plausible Analytics",
          description: "Privacy-focused analytics",
          status: "available",
          icon: "📈"
        },
        // {
        //   id: "google_analytics",
        //   name: "Google Analytics",
        //   description: "Google's web analytics platform",
        //   status: "coming_soon",
        //   icon: "🔍"
        // }
      ]
    },
    {
      id: "backlinks",
      name: "Backlink Detection",
      description: "Tools for monitoring and analyzing backlinks",
      icon: <Link2 className="h-5 w-5" />,
      color: "text-purple-600",
      integrations: integrations.filter(i => getProviderInfo(i.provider).category === "backlinks"),
      availableProviders: [
        {
          id: "ahrefs",
          name: "Ahrefs",
          description: "Comprehensive backlink analysis",
          status: "beta",
          icon: "🔗"
        },
        {
          id: "semrush",
          name: "SEMrush",
          description: "SEO and backlink monitoring",
          status: "coming_soon",
          icon: "📊"
        },
        {
          id: "moz",
          name: "Moz",
          description: "Link analysis and SEO metrics",
          status: "coming_soon",
          icon: "🔍"
        }
      ]
    }
  ];

  const renderIntegrationCard = (integration: Integration) => {
    const providerInfo = getProviderInfo(integration.provider);
    const project = projects.find(p => p.id === integration.project_id);
    
    return (
      <div key={integration.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">{providerInfo.icon}</span>
              <div>
                <h4 className="font-semibold">{integration.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {providerInfo.name} • {project?.name || 'Unknown Project'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>ID: {integration.website_id}</span>
              {integration.base_url && (
                <span>URL: {integration.base_url}</span>
              )}
              {getStatusBadge(integration.status)}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setEditingIntegration(integration);
                setShowForm(true);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteIntegration(integration)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderProviderCard = (provider: any) => {
    const statusConfig = {
      available: { variant: "default" as const, label: "Available", color: "text-green-600 border-green-600" },
      coming_soon: { variant: "secondary" as const, label: "Coming Soon", color: "text-blue-600 border-blue-600" },
      beta: { variant: "outline" as const, label: "Beta", color: "text-purple-600 border-purple-600" }
    };
    
    const config = statusConfig[provider.status as keyof typeof statusConfig];
    
    return (
      <Card key={provider.id} className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl">{provider.icon}</span>
              <div>
                <h4 className="font-semibold">{provider.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {provider.description}
                </p>
              </div>
            </div>
            <Badge variant={config.variant} className={config.color}>
              {config.label}
            </Badge>
          </div>
          <div className="mt-4 flex justify-end">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setSelectedCategory(provider.id);
                setShowForm(true);
              }}
              disabled={provider.status === "coming_soon"}
            >
              <Plus className="h-4 w-4 mr-2" />
              {provider.status === "available" ? "Add Integration" : "Coming Soon"}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderCategoryContent = (category: IntegrationCategory) => (
    <div className="space-y-6">
      {/* Category Description */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">{category.name}</h3>
        <p className="text-muted-foreground">{category.description}</p>
      </div>

      {/* Existing Integrations */}
      {category.integrations.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium">Active Integrations ({category.integrations.length})</h4>
          <div className="space-y-3">
            {category.integrations.map(renderIntegrationCard)}
          </div>
        </div>
      )}

      {/* Available Providers */}
      <div className="space-y-4">
        <h4 className="font-medium">Available Integrations</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {category.availableProviders.map(renderProviderCard)}
        </div>
      </div>
    </div>
  );

  if (projects.length === 0) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need to create at least one project before adding integrations.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const categories = getIntegrationCategories();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Integrations</h2>
          <p className="text-muted-foreground">
            Connect external services to enhance your link tracking capabilities
          </p>
        </div>
        <Button 
          onClick={handleRefreshIntegrations}
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{integrations.length}</p>
                <p className="text-sm text-muted-foreground">Total Integrations</p>
              </div>
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{integrations.filter(i => i.status === 'active').length}</p>
                <p className="text-sm text-muted-foreground">Active</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{categories.reduce((sum, cat) => sum + cat.availableProviders.filter(p => p.status === 'available').length, 0)}</p>
                <p className="text-sm text-muted-foreground">Available</p>
              </div>
              <Globe className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{categories.length}</p>
                <p className="text-sm text-muted-foreground">Categories</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Tabs */}
      <Tabs defaultValue="search" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {categories.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
              {category.icon}
              <span className="hidden sm:inline">{category.name}</span>
            </TabsTrigger>
          ))}
        </TabsList>
        
        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id} className="mt-6">
            {renderCategoryContent(category)}
          </TabsContent>
        ))}
      </Tabs>

      {/* Integration Form Dialog */}
      {showForm && (
        <IntegrationForm
          integration={editingIntegration || undefined}
          projects={projects}
          onSubmit={handleSubmitIntegration}
          onCancel={() => {
            setShowForm(false);
            setEditingIntegration(null);
            setSelectedCategory(null);
          }}
        />
      )}
    </div>
  );
} 