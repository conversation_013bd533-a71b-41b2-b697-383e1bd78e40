# Enhanced Backlinks Table - 批量操作功能

## 概述

为 `enhanced-backlinks-table.tsx` 组件新增了批量删除和批量状态修改功能，提升用户操作效率。

## 新增功能

### 1. 复选框选择
- **全选复选框**：位于表格头部，支持全选/取消全选
- **单选复选框**：每行都有独立的复选框
- **半选状态**：当部分项目被选中时，全选复选框显示半选状态
- **移动端支持**：在移动端卡片视图中也提供复选框

### 2. 批量操作工具栏
- **动态显示**：仅在有选中项目时显示
- **选中计数**：显示当前选中的项目数量
- **取消选择**：一键清除所有选择
- **批量状态更新**：支持批量修改为新发现/已提交/已收录/已归档
- **批量删除**：支持批量删除选中的外链

### 3. 加载状态管理
- **操作禁用**：批量操作进行时禁用相关按钮和复选框
- **加载指示器**：显示操作进度
- **错误处理**：操作失败时显示错误提示

## 接口变更

### 新增 Props

```typescript
interface EnhancedBacklinksTableProps {
  // ... 原有属性
  onBatchDeleteLinks?: (linkIds: string[]) => Promise<void>;
  onBatchUpdateLinkStatus?: (linkIds: string[], status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => Promise<void>;
}
```

### 使用示例

```typescript
<EnhancedBacklinksTable
  discoveredLinks={links}
  onAddToManagedLinks={handleAddToManagedLinks}
  onScanForLinks={handleScanForLinks}
  isScanning={false}
  onDeleteLink={handleDeleteLink}
  onUpdateLinkStatus={handleUpdateLinkStatus}
  // 新增的批量操作回调
  onBatchDeleteLinks={handleBatchDeleteLinks}
  onBatchUpdateLinkStatus={handleBatchUpdateLinkStatus}
/>
```

## 实现细节

### 状态管理
- `selectedLinks: Set<string>` - 存储选中的链接ID
- `isBatchProcessing: boolean` - 批量操作进行状态

### 核心函数
- `handleSelectAll()` - 全选/取消全选
- `handleSelectLink(linkId)` - 单个项目选择
- `handleBatchDelete()` - 批量删除
- `handleBatchUpdateStatus(status)` - 批量状态更新

### 兼容性处理
- 如果未提供批量操作回调函数，会自动降级为逐个操作
- 保持向后兼容，不影响现有功能

## 用户体验优化

1. **视觉反馈**：选中状态有明确的视觉指示
2. **操作确认**：批量操作有明确的成功/失败提示
3. **响应式设计**：在不同屏幕尺寸下都有良好的体验
4. **性能优化**：使用 Set 数据结构优化选择状态管理

## 测试建议

1. 测试全选/取消全选功能
2. 测试单个项目选择/取消选择
3. 测试批量删除功能
4. 测试批量状态更新功能
5. 测试在操作进行中的禁用状态
6. 测试移动端和桌面端的兼容性
7. 测试错误处理和用户反馈

## 注意事项

- 批量操作是可选的，如果不提供相应的回调函数，组件会自动降级处理
- 建议在实际项目中实现真实的API调用来替换示例中的模拟操作
- 考虑添加操作确认对话框，特别是对于批量删除操作
