/**
 * 使用示例：Enhanced Backlinks Table 批量操作功能
 * 
 * 这个文件展示了如何使用新增的批量删除和批量状态更新功能
 */

"use client";

import React, { useState } from 'react';
import EnhancedBacklinksTable from './enhanced-backlinks-table';
import { toast } from "sonner";

// 示例数据类型
interface DiscoveredLinkExtended {
  id: string;
  url: string;
  title: string;
  domain: string;
  anchorText: string;
  link_type: 'dofollow' | 'nofollow';
  discoveredAt: string;
  sourceUrl: string;
  isActive: boolean;
  status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED';
  dr_score?: number;
  traffic?: number;
  isInManagedList: boolean;
  is_indexed?: boolean;
  referral_traffic?: number;
  analytics_source?: 'plausible' | 'google-analytics' | 'umami' | 'manual';
  traffic_period?: string;
  last_traffic_update?: string;
  traffic_contribution_percentage?: number;
}

// 示例数据
const sampleLinks: DiscoveredLinkExtended[] = [
  {
    id: '1',
    url: 'https://example.com/page1',
    title: '示例页面1',
    domain: 'example.com',
    anchorText: '示例锚文本1',
    link_type: 'dofollow',
    discoveredAt: '2024-01-15T10:00:00Z',
    sourceUrl: 'https://source.com/page1',
    isActive: true,
    status: 'NEW',
    dr_score: 65,
    traffic: 1000,
    isInManagedList: false,
    is_indexed: true,
    referral_traffic: 50,
    analytics_source: 'plausible',
    traffic_contribution_percentage: 5.0
  },
  {
    id: '2',
    url: 'https://example2.com/page2',
    title: '示例页面2',
    domain: 'example2.com',
    anchorText: '示例锚文本2',
    link_type: 'nofollow',
    discoveredAt: '2024-01-16T10:00:00Z',
    sourceUrl: 'https://source2.com/page2',
    isActive: true,
    status: 'SUBMITTED',
    dr_score: 45,
    traffic: 500,
    isInManagedList: true,
    is_indexed: false
  }
];

export default function EnhancedBacklinksTableExample() {
  const [links, setLinks] = useState<DiscoveredLinkExtended[]>(sampleLinks);

  // 单个链接操作
  const handleAddToManagedLinks = async (linkId: string) => {
    console.log('添加到管理列表:', linkId);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setLinks(prev => prev.map(link => 
      link.id === linkId ? { ...link, isInManagedList: true } : link
    ));
  };

  const handleDeleteLink = async (linkId: string) => {
    console.log('删除链接:', linkId);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setLinks(prev => prev.filter(link => link.id !== linkId));
  };

  const handleUpdateLinkStatus = async (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
    console.log('更新链接状态:', linkId, status);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setLinks(prev => prev.map(link => 
      link.id === linkId ? { ...link, status } : link
    ));
  };

  // 批量操作
  const handleBatchDeleteLinks = async (linkIds: string[]) => {
    console.log('批量删除链接:', linkIds);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
  };

  const handleBatchUpdateLinkStatus = async (linkIds: string[], status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
    console.log('批量更新链接状态:', linkIds, status);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setLinks(prev => prev.map(link => 
      linkIds.includes(link.id) ? { ...link, status } : link
    ));
  };

  const handleScanForLinks = () => {
    console.log('扫描外链');
    toast.info('开始扫描外链...');
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">外链发现表格 - 批量操作示例</h1>
      
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">新功能说明：</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ 支持单选和全选复选框</li>
          <li>✅ 批量删除选中的外链</li>
          <li>✅ 批量更新选中外链的状态（新发现/已提交/已收录/已归档）</li>
          <li>✅ 批量操作工具栏（仅在有选中项时显示）</li>
          <li>✅ 支持移动端和桌面端视图</li>
          <li>✅ 操作过程中的加载状态和禁用状态</li>
        </ul>
      </div>

      <EnhancedBacklinksTable
        discoveredLinks={links}
        onAddToManagedLinks={handleAddToManagedLinks}
        onScanForLinks={handleScanForLinks}
        isScanning={false}
        onDeleteLink={handleDeleteLink}
        onUpdateLinkStatus={handleUpdateLinkStatus}
        onBatchDeleteLinks={handleBatchDeleteLinks}
        onBatchUpdateLinkStatus={handleBatchUpdateLinkStatus}
      />
    </div>
  );
}
