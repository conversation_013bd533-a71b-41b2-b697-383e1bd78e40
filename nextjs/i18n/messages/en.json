{"user": {"sign_in": "Sign In", "sign_out": "Sign Out", "my_orders": "My Orders", "no_auth_or_email": "No authentication or email found"}, "task_succeeded": "Task completed successfully", "file": "File", "status": {"online": "Online", "view_pending": "View Pending", "created": "Created", "no_pending": "No pending items", "error": "Error", "sent": "<PERSON><PERSON>", "title": "Status", "failed": "Failed", "active": "Active", "description": "Description", "inactive": "Inactive", "pending": "Pending", "offline": "Offline"}, "link_type": {}, "user_id": "User ID", "historyLimit": "History Limit", "withStats": "With Statistics", "action": "Action", "table": {"created_at": "Created At", "id": "ID", "actions": "Actions", "name": "Name", "updated_at": "Updated At", "event_type": "Event Type", "status": "Status", "sent_at": "<PERSON><PERSON>", "tools_count": "Tools Count", "type": "Type", "recipient": "Recipient", "subject": "Subject", "item_uuid": "Item UUID"}, "no_image_result": "No image result", "no_logs": "No logs available", "no_templates": "No templates available", "category": "Category", "result_image": "Result Image", "cancel_button": "Cancel", "completed_at": "Completed At", "error_reason": "Error Reason", "my_tasks": "My Tasks", "T": "T", "update_error": "Update Error", "saved_success": "Saved Successfully", "create_error": "Create Error", "details": {}, "selected_items": "Selected Items", "no_json_result": "No JSON result", "searchTerm": "Search Term", "task_status": "Task Status", "type": "Type", "empty": {"no_tools": "No tools available", "no_parameters": "No parameters available", "no_tools_to_translate": "No tools to translate"}, "prev_page": "Previous Page", "error_loading": "Error Loading", "provider": "Provider", "content": "Content", "search": "Search", "task_processing": "Task Processing", "projectId": "Project ID", "create_success": "Created Successfully", "div": "Division", "my_tasks_description": "My Tasks Description", "print_result": "Print Result", "slug": "Slug", "a": "Link", "brief": "Brief", "test": "Test", "sort": "Sort", "timeRange": "Time Range", "query": "Query", "author_avatar_url": "Author <PERSON><PERSON> URL", "saved": "Saved", "tab_json": "JSON Tab", "saved_error": "Save Error", "url": "URL", "update_success": "Updated Successfully", "tip": "Tip", "host": "Host", "task_view": "Task View", "processinfo": "Process Information", "isPaid": "<PERSON>", "q": "Question", "is_recommended": "Is Recommended", "checked_at": "Checked At", "page": "Page", "keywords": "Keywords", "video_urls": "Video URLs", "uuid": "UUID", "tab_image": "Image Tab", "sessionId": "Session ID", "no_tasks": "No tasks available", "goHome": "Go Home", "task_date": "Task Date", "current": "Current", "image_urls": "Image URLs", "create_button": "Create", "tab_text": "Text Tab", "processing_time": "Processing Time", "task_credit_cost": "Task Credit Cost", "cover_url": "Cover URL", "save": "Save", "Authorization": "Authorization", "signin_type": "Sign In Type", "invite_code": "Invite Code", "lang": "Language", "view_task_result": "View Task Result", "refresh_button": "Refresh", "back_to_submissions": "Back to Submissions", "results_title": "Results", "saving": "Saving...", "USD": "USD", "offset": "Offset", "website_url": "Website URL", "task_product": "Task Product", "tagName": "Tag Name", "task_pending": "Task Pending", "stdio": "Standard I/O", "sse": "Server-Sent Events", "includeHistory": "Include History", "is_official": "Is Official", "task_result": "Task Result", "title": "Title", "limit": "Limit", "process_button": "Process", "next_page": "Next Page", "author_name": "Author Name", "locale": "Locale", "update_button": "Update", "description": "Description", "field": "Field", "api_key": "API Key", "ai_summary": "AI Summary", "domain": {"management": {"title": "Domain Management", "description": "Manage domain expiration dates and avoid missing renewals", "addDomain": "Add Domain", "editDomain": "Edit Domain", "deleteDomain": "Delete Domain", "refreshWhois": "Refresh WHOIS", "bulkImport": "Bulk Import", "associateProject": "Associate Project", "removeAssociation": "Remove Association", "searchPlaceholder": "Search domains...", "noDomains": "No domains found", "loading": "Loading...", "confirmDelete": "Are you sure you want to delete this domain?", "confirmDeleteMessage": "This action cannot be undone.", "domainAdded": "Domain added successfully", "domainUpdated": "Domain updated successfully", "domainDeleted": "Domain deleted successfully", "whoisRefreshed": "WHOIS data refreshed successfully", "errorAddingDomain": "Error adding domain", "errorUpdatingDomain": "Error updating domain", "errorDeletingDomain": "Error deleting domain", "errorRefreshingWhois": "Error refreshing WHOIS data", "expirationTracking": "Track expiration dates to avoid missing renewals"}, "status": {"active": "Active", "expired": "Expired", "expiring": "Expiring Soon", "unknown": "Unknown"}, "stats": {"total": "Total Domains", "active": "Active", "expiring": "Expiring Soon", "expired": "Expired", "projects": "Associated Projects"}, "form": {"domain": "Domain", "addDescription": "Enter domain details. WHOIS data will be fetched automatically.", "registrar": "Registrar", "dnsProvider": "DNS Provider", "createdDate": "Created Date", "expiryDate": "Expiry Date", "registrationPrice": "Registration Price", "renewalPrice": "Renewal Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "autoRenew": "Auto Renew", "monitorExpiry": "Monitor Expiry", "alertDaysBefore": "<PERSON><PERSON> Before", "notes": "Notes", "tags": "Tags", "isFavorite": "Favorite", "nameServers": "Name Servers", "required": "Required", "optional": "Optional", "enterDomain": "Enter domain name", "enterRegistrar": "Enter registrar name", "enterDnsProvider": "Enter DNS provider", "enterNotes": "Enter notes", "enterTags": "Enter tags (comma separated)", "selectCurrency": "Select currency", "selectProject": "Select project", "daysBeforeExpiry": "Days before expiry"}, "filter": {"all": "All", "status": "Status", "registrar": "Registrar", "dnsProvider": "DNS Provider", "project": "Project", "expiring": "Expiring Soon", "favorites": "Favorites"}, "sort": {"domain": "Domain", "expiryDate": "Expiry Date", "createdDate": "Created Date", "registrar": "Registrar", "ascending": "Ascending", "descending": "Descending"}, "table": {"domain": "Domain", "registrar": "Registrar", "dnsProvider": "DNS Provider", "status": "Status", "expiryDate": "Expiry Date", "createdDate": "Created Date", "projects": "Projects", "actions": "Actions", "daysUntilExpiry": "Days Until Expiry", "whoisLastUpdated": "WHOIS Updated", "expires": "Expires", "renewalPrice": "Renewal Price", "never": "Never", "view": "View", "edit": "Edit", "delete": "Delete", "refresh": "Refresh", "associate": "Associate", "favorite": "Favorite", "unfavorite": "Unfavorite"}, "whois": {"data": "WHOIS Data", "lastUpdated": "Last Updated", "cacheExpires": "<PERSON><PERSON> Expires", "refreshing": "Refreshing...", "noData": "No WHOIS data available", "expired": "WHOIS data expired", "clickToRefresh": "Click to refresh WHOIS data"}, "project": {"association": "Project Association", "associatedProjects": "Associated Projects", "noAssociatedProjects": "No associated projects", "selectProject": "Select a project to associate", "isPrimary": "Primary domain for project", "create_project": "Create Project", "edit_project": "Edit Project", "associationAdded": "Project association added", "associationRemoved": "Project association removed", "errorAddingAssociation": "Error adding project association", "errorRemovingAssociation": "Error removing project association"}, "monitoring": {"title": "Domain Expiration Monitoring", "expiryMonitoring": "Expiry Monitoring", "alertSettings": "<PERSON><PERSON>", "lastAlertSent": "Last <PERSON><PERSON>", "noAlerts": "No alerts sent yet", "alertsEnabled": "<PERSON><PERSON><PERSON>", "alertsDisabled": "<PERSON><PERSON><PERSON> Disabled", "preventMissedRenewals": "Prevent missed renewals with expiration alerts"}, "import": {"title": "Bulk Import Domains", "description": "Import multiple domains at once", "format": "Format: domain.com,registrar,dns-provider", "example": "Example: example.com,Go<PERSON><PERSON>dy,Cloudflare", "paste": "Paste domains (one per line)", "import": "Import Domains", "importing": "Importing...", "success": "Successfully imported {count} domains", "error": "Error importing domains", "invalidFormat": "Invalid format on line {line}", "duplicateDomain": "Duplicate domain: {domain}"}}, "task_failed_status": "Task Failed", "no_text_result": "No text result", "invitation": {"invites_count": "<PERSON><PERSON><PERSON>", "earn_credits": "<PERSON><PERSON><PERSON>", "share_title": "Share Invitation", "credits_per_invite": "Credits per Invite", "copy": "Copy", "share_text": "Share this invitation link with your friends!", "copy_success": "Copied to clipboard!", "how_it_works": "How It Works", "your_code": "Your Code", "copying": "Copying...", "share_success": "Shared successfully!", "share_description": "Invite friends and earn credits", "share_code": "Share Code", "title": "Invitation System", "share_invite_link": "Share Invite Link", "copy_failed": "Co<PERSON> failed", "share": "Share", "share_failed": "Share failed", "successful_invites": "Successful Invites"}, "editor": {"result_image": "Result Image", "copied_json": "JSON copied to clipboard!", "results_title": "Results", "copy": "Copy", "usage_processing_time": "Processing Time", "my_tasks": "My Tasks", "usage_supported_formats": "Supported Formats", "upload_description": "Upload your file for processing", "drop_files": "Drop files here to upload", "tab_text": "Text", "no_json_result": "No JSON result", "no_text_result": "No text result", "processing_file": "Processing file...", "processing": "Processing...", "file_selected": "File selected", "error_polling": "Error polling for results", "unexpected_error": "Unexpected error occurred", "usage_max_file_size": "Maximum file size", "task_complete": "Task completed", "no_image_result": "No image result", "usage_tips_title": "Usage Tips", "error_no_file": "No file selected", "process_file": "Process File", "error": "Error", "tab_json": "JSON", "usage_sign_in": "Sign in to use this feature", "error_file_size": "File size exceeds limit", "usage_credit_cost": "Credit cost", "error_file_type": "Unsupported file type", "task_timeout": "Task timeout", "error_create_task": "Error creating task", "upload_title": "Upload File", "copied_text": "Text copied to clipboard!", "upload_process_prompt": "Upload and process your file", "file_type": "File type", "remove_file": "Remove file", "results_description": "Processing results", "tab_image": "Image", "max_file_size": "Maximum file size", "unknown_error": "Unknown error", "task_failed": "Task failed"}, "linkResources": {"title": "External Link Resource Library", "description": "Manage your collection of external link resources and track their effectiveness for your projects", "loading": "Loading...", "noResults": "No resources found", "resultsCount": "{count} resources found", "paid": "Paid", "free": "Free", "common": {"home": "Home"}, "seo": {"title": "External Link Resource Management", "description": "Your database of verified external link platforms with performance tracking", "keywords": "external link resources, link library, project management, SEO tools, link building", "categoryDescription": "Browse {category} backlink resources in your library", "paidDescription": "Premium backlink resources in your collection", "freeDescription": "Free backlink resources in your collection", "page": "Page", "additionalTitle": "How Your Personal Resource Library Works", "howItWorks": "How It Works", "howItWorksDesc": "Your systematic approach to managing backlink resources", "step1": "Build your personal library of verified submission platforms", "step2": "Track performance metrics and success rates", "step3": "Update resource status based on project results", "step4": "Maintain quality through regular review and updates", "benefits": "Benefits", "benefitsDesc": "Why maintain your personal resource library", "benefit1": "Curated collection tailored to your projects", "benefit2": "Track success rates and ROI for each resource", "benefit3": "Avoid wasting time on low-quality platforms", "benefit4": "Build institutional knowledge over time"}, "stats": {"totalResources": "Total Resources in Library", "categories": "Categories Tracked", "highAuthority": "Average Success Rate"}, "features": {"title": "Personal Library Features", "highQuality": "Quality Tracking", "highQualityDesc": "Monitor performance and success rates", "verified": "Personal Verification", "verifiedDesc": "Track your own experience with each platform", "qualityAssured": "Continuous Updates", "qualityAssuredDesc": "Keep your library current and effective", "updated": "Performance Monitoring", "updatedDesc": "Track ROI and effectiveness over time"}, "filters": {"searchPlaceholder": "Search your resources...", "categoryPlaceholder": "Select category", "allCategories": "All Categories", "pricingPlaceholder": "Select pricing", "allPricing": "All Pricing", "free": "Free Only", "paid": "Paid Only", "sortPlaceholder": "Sort by", "sortByDRDesc": "Domain Rating (High to Low)", "sortByDRAsc": "Domain Rating (Low to High)", "sortByTrafficDesc": "Traffic (High to Low)", "sortByTrafficAsc": "Traffic (Low to High)", "sortBySuccessRateDesc": "Success Rate (High to Low)", "sortByNewest": "Recently Added"}, "categories": {"directory": "Directory", "blog": "Blog", "news": "News", "resource-page": "Resource Page", "guest-post": "Guest Post", "forum": "Forum", "social-media": "Social Media", "press-release": "Press Release", "startup-directory": "Startup Directory", "tool-directory": "Tool Directory"}, "details": {"submissionMethod": "Submission Method", "responseTime": "Response Time", "successRate": "Personal Success Rate", "priceRange": "Price Range", "requirements": "Requirements", "lastUsed": "Last Used", "personalNotes": "Personal Notes"}, "actions": {"submitHere": "Submit Here", "visitWebsite": "Visit Website", "contact": "Contact", "updateStatus": "Update Status", "addNotes": "Add Notes"}, "pagination": {"page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "faq": {"title": "Frequently Asked Questions", "q1": "What is a personal backlink resource library?", "a1": "A curated collection of backlink platforms that you've personally tested and verified for your side projects.", "q2": "How do I track resource performance?", "a2": "Record success rates, response times, and personal notes for each platform you use.", "q3": "Can I import existing resources?", "a3": "Yes, you can bulk import your existing backlink resources and add performance data.", "q4": "How often should I update my library?", "a4": "Review and update your library weekly based on new submissions and results.", "q5": "What metrics should I track?", "a5": "Track success rates, response times, costs, and personal effectiveness notes.", "q6": "How does this help my side projects?", "a6": "Maintain a quality resource library to efficiently build backlinks for future projects."}}, "links": {"traffic": "Traffic", "title": "Links", "add": "Add Link", "saving": "Saving...", "cancel": "Cancel", "update": "Update", "a": "Link", "dr_score": "Domain Rating", "add_link": "Add Link", "project": {"create_project": "Create a new project to organize your links", "name": "Project name", "domain": "example.com (will be normalized automatically)", "domain_note": "Domain will be automatically normalized (www prefix removed, lowercased)", "description": "Optional description of your project", "category": "Project Category", "select_category": "Select or create category", "category_help": "Select a category for the project for easy organization and management", "analytics_platform": "Analytics Platform (Optional)", "analytics_note": "You can configure analytics integration later from the Integrations tab", "actions": {"cancel": "Cancel", "create": "Create"}}}, "SubmitPage": {"heading": "Add Resource to Library", "home": "Home", "submit": "Add Resource", "title": "Add Resource", "description": "Add a new backlink resource to your personal library"}, "SearchPage": {"search": "Search", "title": "Search Resources", "description": "Search your personal backlink resource library", "home": "Home"}, "metadata": {"title": "MyBackLinks - External Link Resource Management & Project Analytics", "description": "Manage your external link resource library, track project DR values and backlinks, monitor domain expiration, and integrate with analytics platforms", "keywords": "external link library, project analytics, domain management, DR tracking, SEMrush integration, plausible analytics"}, "NotFound": {"title": "Page Not Found", "description": "The page you're looking for doesn't exist or has been moved.", "goHome": "Go Home"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to access your personal backlink library, side project tracking, and domain management tools", "cancel_title": "Cancel", "google_sign_in": "Google", "github_sign_in": "GitHub"}, "integrations": "Integrations", "projects": {"title": "Projects", "description": "Track DR values, backlinks, and analytics for your projects", "addProject": "Add Project", "editProject": "Edit Project", "deleteProject": "Delete Project", "viewAnalytics": "View Analytics", "projectUrl": "Project URL", "projectName": "Project Name", "projectDescription": "Project Description", "noProjects": "No projects found", "createFirst": "Create your first project to start tracking", "drTracking": {"title": "Domain Rating Tracking", "currentDR": "Current DR", "drHistory": "DR History", "lastUpdated": "Last Updated", "uploadSemrush": "Upload SEMrush File", "manualUpdate": "Manual Update", "limitedQueries": "Limited manual queries available"}, "backlinks": {"title": "Backlink Tracking", "totalBacklinks": "Total Backlinks", "newBacklinks": "New Backlinks", "lostBacklinks": "Lost Backlinks", "referringDomains": "Referring Domains", "backlinkSources": "Backlink Sources", "qualityScore": "Quality Score", "noDeadLinkTracking": "Note: Dead link tracking not available"}, "analytics": {"title": "Project Analytics", "traffic": "Traffic Overview", "backlinks": "Backlinks", "domains": "Associated Domains", "lastUpdated": "Last Updated (Weekly)", "plausibleData": "Plausible Analytics", "googleAnalyticsData": "Google Analytics", "googleConsoleData": "Google Search Console", "trafficSources": "Traffic Sources", "topPages": "Top Pages", "keywords": "Keywords", "noData": "No analytics data available", "weeklyUpdates": "Data updated weekly"}, "resourceLibrary": {"title": "Resource Library Updates", "description": "Update your backlink resource library based on this project's results", "addSuccessful": "Add Successful Resources", "markUnsuccessful": "<PERSON>successful Resources", "updateNotes": "Update Resource Notes", "trackPerformance": "Track Performance"}, "limits": {"freeProjects": "Free users can track up to 5 projects", "freeUpdateFrequency": "Analytics and DR data updated weekly", "freeManualQueries": "10 manual DR queries per month", "premiumProjects": "Premium users can track up to 1000 projects", "premiumUpdateFrequency": "Analytics and DR data updated weekly", "premiumManualQueries": "100 manual DR queries per month", "upgradePrompt": "Upgrade to Premium for more projects and manual queries", "noRealTimeTracking": "Real-time tracking not available", "noApiAccess": "API access not provided", "noDataExport": "Data export not available"}}, "analytics": {"title": "Analytics", "traffic": {"title": "Traffic Analytics", "description": "Track your website traffic from integrated analytics platforms", "plausible": "Plausible Analytics", "googleAnalytics": "Google Analytics", "googleConsole": "Google Search Console", "visitors": "Visitors", "pageViews": "Page Views", "sessions": "Sessions", "bounceRate": "Bounce Rate", "avgSessionDuration": "Average Session Duration", "topReferrers": "Top Referrers", "topCountries": "Top Countries", "searchQueries": "Search Queries", "clicks": "<PERSON>licks", "impressions": "Impressions", "ctr": "Click-through Rate", "avgPosition": "Average Position", "weeklyUpdates": "Data updated weekly", "notRealTime": "Not real-time data"}, "backlinks": {"title": "Backlink Analytics", "description": "Monitor your backlink profile and growth (via SEMrush import or manual queries)", "totalBacklinks": "Total Backlinks", "newBacklinks": "New Backlinks", "lostBacklinks": "Lost Backlinks", "referringDomains": "Referring Domains", "domainRating": "Domain Rating", "urlRating": "URL Rating", "anchorText": "Anchor Text", "linkType": "Link Type", "status": "Status", "firstSeen": "First Seen", "lastSeen": "Last Seen", "semrushImport": "SEMrush File Import", "manualQuery": "Manual Query", "queryLimits": "Query limits apply", "noDeadLinkTracking": "Dead link tracking not available"}, "integration": {"title": "Analytics Integration", "description": "Connect your analytics providers to track data", "plausible": {"title": "Plausible Analytics", "description": "Connect your Plausible Analytics account for traffic data", "apiKey": "API Key", "siteId": "Site ID", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect", "disconnect": "Disconnect"}, "googleAnalytics": {"title": "Google Analytics", "description": "Connect your Google Analytics account for traffic insights", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect with Google", "disconnect": "Disconnect", "selectProperty": "Select Property"}, "googleConsole": {"title": "Google Search Console", "description": "Connect your Google Search Console account for search data", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect with Google", "disconnect": "Disconnect", "selectProperty": "Select Property"}, "limitations": {"title": "Integration Limitations", "weeklyUpdates": "Data updated weekly, not real-time", "noApiAccess": "API access not provided", "noDataExport": "Data export not available"}}}, "pricing": {"title": "Pricing", "free": {"title": "Free Plan", "price": "Free", "features": {"0": "Up to 5 projects", "1": "External link resource library", "2": "Basic DR tracking (10 manual queries/month)", "3": "Domain expiration monitoring", "4": "Analytics integration (Plausible, Google)", "5": "Weekly data updates"}, "limitations": {"0": "Limited to 5 projects", "1": "10 manual DR queries per month", "2": "Weekly data updates only", "3": "No API access", "4": "No data export", "5": "No dead link tracking"}}, "premium": {"title": "Premium Plan", "price": "$29/month", "features": {"0": "Up to 1000 projects", "1": "Advanced resource library management", "2": "Enhanced DR tracking (100 manual queries/month)", "3": "Advanced domain monitoring with alerts", "4": "Priority analytics integration", "5": "Weekly data updates", "6": "Priority support", "7": "Advanced reporting"}, "upgrade": "Upgrade to Premium", "benefits": {"0": "More manual DR queries", "1": "Track up to 1000 projects", "2": "Advanced domain alerts", "3": "Priority customer support"}, "limitations": {"0": "Still weekly updates (not real-time)", "1": "No API access", "2": "No data export", "3": "No dead link tracking"}}}, "limitations": {"title": "Platform Limitations", "description": "Understanding what MyBackLinks does and doesn't support", "noDeadLinkTracking": {"title": "No Dead Link Tracking", "description": "Automatic dead link detection and monitoring is not available"}, "noApiAccess": {"title": "No API Access", "description": "API endpoints for external integrations are not provided"}, "noDataExport": {"title": "No Data Export", "description": "Bulk data export functionality is not available"}, "weeklyUpdates": {"title": "Weekly Data Updates", "description": "Data is updated approximately once per week, not in real-time"}, "manualQueries": {"title": "Limited Manual Queries", "description": "Manual DR and backlink queries are limited based on your plan"}}, "features": {"title": "Core Features", "description": "What MyBackLinks helps you accomplish", "personalLibrary": {"title": "Personal Backlink Resource Library", "description": "Maintain and organize your collection of backlink submission platforms"}, "projectTracking": {"title": "Side Project Tracking", "description": "Track DR values and backlinks for your side projects via SEMrush import or manual queries"}, "resourceMaintenance": {"title": "Resource Library Maintenance", "description": "Update your resource library based on project results and performance"}, "domainManagement": {"title": "Domain Expiration Management", "description": "Monitor domain expiration dates and avoid missing renewals"}, "analyticsIntegration": {"title": "Analytics Integration", "description": "Connect with Plausible, Google Analytics, and Google Search Console for traffic insights"}}, "subscription": {"title": "Subscription Management", "description": "Manage your MyBackLinks subscription and view usage statistics", "current_plan": "Current Plan", "usage_summary": "Usage Summary", "upgrade_plan": "Upgrade Plan", "manage_billing": "Manage Billing", "features": "Features", "limits": "Limits", "projects": "Projects", "domains": "Domains", "link_resources": "Link Resources", "dr_queries": "DR Queries", "traffic_updates": "Traffic Updates", "free_tier": "Free Tier", "professional_tier": "Professional Tier", "unlimited": "Unlimited", "per_month": "per month", "upgrade_now": "Upgrade Now", "contact_support": "Contact Support"}, "blog": {"title": "Blog", "description": "Latest insights and updates from our team", "read_more_text": "Read More"}, "blocks": {"hero": {"title": "Build Your Ultimate External Link Resource Library", "description": "Track website traffic, monitor domain ratings, and manage your external link portfolio for better project promotion and growth.", "announcement": {"title": "New Analytics Dashboard Available", "url": "/dashboard"}, "buttons": {"primary": "Start Building Your Library", "secondary": "View Live Demo"}, "tip": "Free plan includes up to 5 projects and basic analytics integration"}, "feature1": {"title": "Real-Time Traffic & DR Analytics", "description": "Monitor your side projects' performance with comprehensive analytics integration and domain rating tracking to optimize your growth strategy.", "features": {"traffic_monitoring": {"title": "Traffic Analytics Integration", "description": "Connect with Google Analytics, Plausible, and Google Search Console for unified traffic insights across all your projects."}, "dr_tracking": {"title": "Domain Rating Monitoring", "description": "Track DR changes over time via SEMrush integration or manual queries to measure your SEO progress effectively."}, "backlink_analysis": {"title": "Backlink Portfolio Management", "description": "Monitor new and lost backlinks, analyze referring domains, and track the quality of your link building efforts."}, "performance_insights": {"title": "Growth Performance Insights", "description": "Get actionable insights on traffic trends, keyword rankings, and backlink opportunities to accelerate project growth."}}}, "feature2": {"title": "Smart External Link Resource Management", "description": "Build and maintain your personal database of high-quality backlink opportunities with performance tracking and success rate monitoring.", "features": {"resource_library": {"title": "Curated Resource Database", "description": "Organize external link opportunities by category, track success rates, and build your personalized submission workflow."}, "performance_tracking": {"title": "Success Rate Analytics", "description": "Monitor response times, approval rates, and ROI for each resource to optimize your outreach strategy over time."}, "quality_management": {"title": "Quality Assurance System", "description": "Rate and review platforms based on your experience, maintaining a high-quality resource library for future campaigns."}}}, "feature": {"title": "Complete Domain Portfolio Management", "description": "Never miss a domain renewal again with automated expiration monitoring and comprehensive domain management tools.", "features": {"expiration_monitoring": {"title": "Expiration Alerts", "description": "Get timely notifications before domain expiration to prevent service interruptions and protect your brand."}, "whois_tracking": {"title": "WHOIS Data Management", "description": "Automatically track registrar information, DNS settings, and renewal costs for all your domains in one place."}, "project_association": {"title": "Project Integration", "description": "Link domains to specific projects for holistic tracking of your digital asset portfolio and performance metrics."}}}, "pricing": {"title": "Choose Your Growth Plan", "description": "Start free and scale with your projects. No hidden fees, cancel anytime.", "plans": {"free": {"name": "Starter", "price": "Free", "description": "Perfect for testing and small projects", "features": ["Up to 5 projects tracking", "External link resource library", "Basic DR tracking (10 queries/month)", "Domain expiration monitoring", "Analytics integration", "Weekly data updates"], "button": "Get Started Free", "tip": "No credit card required"}, "premium": {"name": "Professional", "price": "$29", "unit": "/month", "description": "Ideal for serious side project builders", "features": ["Up to 1000 projects tracking", "Advanced resource management", "Enhanced DR tracking (100 queries/month)", "Advanced domain monitoring", "Priority analytics integration", "Weekly data updates", "Priority support", "Advanced reporting"], "button": "Upgrade to Pro", "popular": true}}}, "testimonials": {"title": "Loved by Side Project Builders", "description": "See how creators are growing their projects with our platform"}, "faq": {"title": "Frequently Asked Questions", "description": "Everything you need to know about building your external link resource library", "questions": {"what_is_platform": {"question": "What is MyBackLinks and how does it help my side projects?", "answer": "MyBackLinks helps you build and maintain a personal library of external link opportunities while tracking your projects' performance metrics like traffic, DR, and backlinks."}, "how_track_dr": {"question": "How do you track domain rating and backlinks?", "answer": "We integrate with SEMrush for data import and provide manual query options. You can upload SEMrush reports or use our limited manual queries based on your plan."}, "analytics_integration": {"question": "Which analytics platforms do you support?", "answer": "We integrate with Google Analytics, Google Search Console, and Plausible Analytics to provide unified traffic insights for your projects."}, "resource_library": {"question": "How does the external link resource library work?", "answer": "You can organize and track external link opportunities by category, monitor success rates, and build your personalized outreach workflow for better results."}, "data_updates": {"question": "How often is data updated?", "answer": "Analytics and DR data are updated approximately once per week. We don't provide real-time tracking to keep costs manageable and focus on trends."}, "plan_limits": {"question": "What are the differences between free and premium plans?", "answer": "Free users can track up to 5 projects with 10 manual DR queries per month. Premium users get up to 1000 projects with 100 manual queries and priority support."}}}, "cta": {"title": "Ready to Grow Your Side Project Empire?", "description": "Join thousands of creators building sustainable external link libraries and tracking their project success.", "button": {"primary": "Start Building for Free", "secondary": "View Live Demo"}}}, "faq": {"helpful_information": "Helpful information", "still_have_questions": "Still have questions?", "contact_support_description": "We're here to help you build your external link empire", "contact_support": "Contact Support"}}