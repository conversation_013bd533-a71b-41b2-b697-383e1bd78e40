#!/usr/bin/env node

/**
 * Test script for error scenarios and edge cases
 * This tests how the API handles various failure conditions
 */

console.log('🧪 Testing Error Scenarios and Edge Cases');
console.log('==========================================\n');

const testCases = [
  {
    name: 'Request Body Double-Parse Bug Test',
    description: 'Test the critical bug we identified where request body is parsed twice',
    body: {
      text: 'Test text for double parse bug',
      prompt: 'Optimize this'
    },
    // We'll simulate this by sending a request that might trigger the error path
    simulateError: true
  },
  {
    name: 'Very Large Text Input',
    description: 'Test with extremely large text that might cause memory issues',
    body: {
      text: 'A'.repeat(50000) + ' This is a very large text input that might cause issues.',
      prompt: 'Optimize this massive text'
    }
  },
  {
    name: 'Malformed Authorization Header',
    description: 'Test various malformed authorization headers',
    body: {
      text: 'Test text',
      prompt: 'Optimize this'
    },
    headers: {
      'Authorization': 'NotBearer invalid-format'
    }
  },
  {
    name: 'Empty Authorization Header',
    description: 'Test empty authorization header',
    body: {
      text: 'Test text',
      prompt: 'Optimize this'
    },
    headers: {
      'Authorization': ''
    }
  },
  {
    name: 'Invalid Tone Value',
    description: 'Test with invalid tone enum value',
    body: {
      text: 'Test text',
      prompt: 'Optimize this',
      tone: 'invalid-tone-value'
    }
  },
  {
    name: 'Negative Max Length',
    description: 'Test with negative max_length value',
    body: {
      text: 'Test text that should not be truncated',
      prompt: 'Optimize this',
      max_length: -10
    }
  },
  {
    name: 'Zero Max Length',
    description: 'Test with zero max_length value',
    body: {
      text: 'Test text',
      prompt: 'Optimize this',
      max_length: 0
    }
  },
  {
    name: 'Unicode and Emoji Heavy Text',
    description: 'Test with lots of unicode characters and emojis',
    body: {
      text: '🚀🎉🔥💯 Unicode test: café, naïve, résumé, 中文, العربية, русский 🌟✨🎯',
      prompt: 'Clean up this international text'
    }
  },
  {
    name: 'SQL Injection Attempt',
    description: 'Test potential SQL injection in text field',
    body: {
      text: "'; DROP TABLE users; SELECT * FROM passwords; --",
      prompt: 'Clean this malicious input'
    }
  },
  {
    name: 'XSS Attempt',
    description: 'Test potential XSS in text field',
    body: {
      text: '<script>alert("XSS")</script><img src="x" onerror="alert(1)">',
      prompt: 'Clean this potentially dangerous input'
    }
  },
  {
    name: 'Extremely Long Prompt',
    description: 'Test with very long prompt',
    body: {
      text: 'Short text',
      prompt: 'A'.repeat(10000) + ' Make this better with this extremely long prompt that might cause issues'
    }
  },
  {
    name: 'Null Bytes and Control Characters',
    description: 'Test with null bytes and control characters',
    body: {
      text: 'Text with\x00null\x01bytes\x02and\x03control\x04characters',
      prompt: 'Clean this up'
    }
  }
];

async function runErrorTests() {
  console.log('🚀 Starting error scenario tests...\n');
  
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`📝 Description: ${testCase.description}`);
    console.log('─'.repeat(70));
    
    try {
      const url = 'http://localhost:3001/api/extension/optimize';
      const headers = {
        'Content-Type': 'application/json',
        ...testCase.headers
      };
      
      console.log(`📤 Request: POST ${url}`);
      if (testCase.headers) {
        console.log(`📋 Headers:`, testCase.headers);
      }
      console.log(`📄 Body length: ${JSON.stringify(testCase.body).length} characters`);
      
      const startTime = Date.now();
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(testCase.body)
      });
      const endTime = Date.now();
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        responseData = responseText;
      }
      
      console.log(`📥 Response Status: ${response.status}`);
      console.log(`⏱️  Response Time: ${endTime - startTime}ms`);
      
      if (response.status >= 200 && response.status < 300) {
        console.log('✅ Request completed successfully');
        if (responseData.success) {
          console.log(`📊 Optimization Details:`);
          console.log(`   - Source: ${responseData.source}`);
          console.log(`   - Tier: ${responseData.tier}`);
          console.log(`   - Original Length: ${responseData.original_length}`);
          console.log(`   - Optimized Length: ${responseData.optimized_length}`);
          console.log(`   - Confidence: ${responseData.confidence}%`);
          
          // Check for potential security issues
          if (responseData.optimized_text.includes('<script>') || 
              responseData.optimized_text.includes('DROP TABLE')) {
            console.log('🚨 SECURITY WARNING: Potentially dangerous content in response!');
          }
        }
      } else if (response.status >= 400 && response.status < 500) {
        console.log('✅ Client error (expected for invalid inputs)');
        console.log(`📄 Error: ${responseData.message || responseData}`);
      } else {
        console.log('⚠️  Server error');
        console.log(`📄 Error: ${JSON.stringify(responseData, null, 2)}`);
      }
      
      // Check response time for performance issues
      if (endTime - startTime > 10000) {
        console.log('⚠️  WARNING: Response took longer than 10 seconds');
      }
      
    } catch (error) {
      console.log(`💥 Request failed: ${error.message}`);
      
      if (error.name === 'AbortError') {
        console.log('⚠️  Request timed out');
      } else if (error.code === 'ECONNREFUSED') {
        console.log('🚨 Server not running!');
        break;
      }
    }
    
    // Wait between tests to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 Error scenario tests completed!');
  console.log('\n📝 Summary:');
  console.log('   - Check for any unexpected server errors');
  console.log('   - Verify that malicious inputs are handled safely');
  console.log('   - Look for performance issues with large inputs');
  console.log('   - Ensure proper validation error messages');
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  process.exit(1);
}

runErrorTests().catch(console.error);
