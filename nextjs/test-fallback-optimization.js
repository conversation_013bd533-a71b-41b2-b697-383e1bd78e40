#!/usr/bin/env node

/**
 * Test script specifically for fallback optimization functionality
 * This tests the performBasicOptimization function when AI APIs are unavailable
 */

console.log('🧪 Testing Fallback Optimization');
console.log('=================================\n');

const testCases = [
  {
    name: 'Basic Text Cleanup',
    body: {
      text: '  this is  a test   text  ',
      prompt: 'Clean this up'
    }
  },
  {
    name: 'Professional Tone Enhancement',
    body: {
      text: 'This is awesome stuff guys, okay?',
      prompt: 'Make this professional',
      tone: 'professional'
    }
  },
  {
    name: 'Length Constraint Test',
    body: {
      text: 'This is a very long text that exceeds the maximum length limit and should be truncated properly with ellipsis.',
      prompt: 'Shorten this',
      max_length: 50
    }
  },
  {
    name: 'SEO Optimization Test',
    body: {
      text: 'This is basic content.',
      prompt: 'Optimize for SEO',
      context: 'Website content'
    }
  },
  {
    name: 'Empty Text Edge Case',
    body: {
      text: '',
      prompt: 'Optimize this'
    }
  },
  {
    name: 'Special Characters Test',
    body: {
      text: 'Text with émojis 🚀 and spëcial chars: @#$%^&*()',
      prompt: 'Clean this up',
      tone: 'professional'
    }
  }
];

async function testFallbackOptimization() {
  console.log('🔧 Testing with AI API disabled (fallback mode)...\n');
  
  // Temporarily disable AI API by setting invalid keys
  const originalEnv = {
    AI_API_KEY: process.env.AI_API_KEY,
    FREE_AI_API_KEY: process.env.FREE_AI_API_KEY
  };
  
  // Set invalid API keys to force fallback
  process.env.AI_API_KEY = '';
  process.env.FREE_AI_API_KEY = '';
  
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log('─'.repeat(50));
    
    try {
      const url = 'http://localhost:3001/api/extension/optimize';
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCase.body)
      });
      
      const data = await response.json();
      
      console.log(`📤 Input: "${testCase.body.text}"`);
      console.log(`📥 Status: ${response.status}`);
      
      if (response.status === 200 && data.success) {
        console.log(`✅ Fallback optimization successful`);
        console.log(`📄 Output: "${data.optimized_text}"`);
        console.log(`📊 Details:`);
        console.log(`   - Source: ${data.source}`);
        console.log(`   - Original Length: ${data.original_length}`);
        console.log(`   - Optimized Length: ${data.optimized_length}`);
        console.log(`   - Improvements: ${data.improvements?.join(', ') || 'None'}`);
        console.log(`   - Confidence: ${data.confidence}%`);
        
        // Verify it's using fallback
        if (data.source !== 'fallback') {
          console.log(`⚠️  Warning: Expected fallback source, got ${data.source}`);
        }
      } else if (response.status === 400) {
        console.log(`✅ Validation error (expected): ${data.message}`);
      } else {
        console.log(`❌ Unexpected response: ${JSON.stringify(data, null, 2)}`);
      }
      
    } catch (error) {
      console.log(`💥 Error: ${error.message}`);
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Restore original environment
  process.env.AI_API_KEY = originalEnv.AI_API_KEY;
  process.env.FREE_AI_API_KEY = originalEnv.FREE_AI_API_KEY;
  
  console.log('\n🎉 Fallback optimization tests completed!');
  console.log('\n📝 Analysis:');
  console.log('   - All responses should have source: "fallback"');
  console.log('   - Text should be cleaned up with basic improvements');
  console.log('   - Professional tone should replace casual words');
  console.log('   - Length constraints should be respected');
  console.log('   - Empty text should be handled gracefully');
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  process.exit(1);
}

testFallbackOptimization().catch(console.error);
