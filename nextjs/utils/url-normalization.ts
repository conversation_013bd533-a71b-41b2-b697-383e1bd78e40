/**
 * URL normalization utilities for consistent link handling
 * Handles cases where www.example.com and example.com should be treated as the same domain
 */

export interface NormalizedUrl {
  normalized: string;
  domain: string;
  originalUrl: string;
}

/**
 * Normalizes a URL by:
 * - Converting to lowercase
 * - Removing www. prefix
 * - Ensuring https protocol
 * - Removing trailing slash
 * - Removing fragment identifier (#)
 * - Sorting query parameters
 */
export function normalizeUrl(url: string): NormalizedUrl {
  // Check if url is null, undefined, or not a string
  if (!url || typeof url !== 'string') {
    return {
      normalized: '',
      domain: '',
      originalUrl: url || ''
    };
  }

  try {
    // Handle URLs without protocol
    let fullUrl = url.trim();
    if (!fullUrl.match(/^https?:\/\//)) {
      fullUrl = `https://${fullUrl}`;
    }

    const urlObj = new URL(fullUrl);
    
    // Normalize hostname (remove www prefix and convert to lowercase)
    let hostname = urlObj.hostname.toLowerCase();
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    // Always use https
    urlObj.protocol = 'https:';
    urlObj.hostname = hostname;
    
    // Remove trailing slash from pathname unless it's just "/"
    if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
      urlObj.pathname = urlObj.pathname.slice(0, -1);
    }
    
    // Remove fragment identifier
    urlObj.hash = '';
    
    // Sort query parameters for consistent ordering
    const searchParams = new URLSearchParams(urlObj.search);
    const sortedParams = new URLSearchParams();
    Array.from(searchParams.keys())
      .sort()
      .forEach(key => {
        searchParams.getAll(key).forEach(value => {
          sortedParams.append(key, value);
        });
      });
    urlObj.search = sortedParams.toString();
    
    return {
      normalized: urlObj.toString(),
      domain: hostname,
      originalUrl: url
    };
  } catch (error) {
    // If URL parsing fails, return original URL with basic cleanup
    const cleanUrl = url.trim().toLowerCase();
    return {
      normalized: cleanUrl,
      domain: extractDomainFromUrl(cleanUrl),
      originalUrl: url
    };
  }
}

/**
 * Extracts domain from URL with www normalization
 */
export function extractDomainFromUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }
  
  try {
    const normalized = normalizeUrl(url);
    return normalized.domain;
  } catch {
    return '';
  }
}

/**
 * Checks if two URLs are considered the same after normalization
 */
export function areUrlsEquivalent(url1: string, url2: string): boolean {
  if (!url1 || !url2 || typeof url1 !== 'string' || typeof url2 !== 'string') {
    return false;
  }
  
  try {
    const normalized1 = normalizeUrl(url1);
    const normalized2 = normalizeUrl(url2);
    return normalized1.normalized === normalized2.normalized;
  } catch {
    return url1.trim().toLowerCase() === url2.trim().toLowerCase();
  }
}

/**
 * Checks if two domains are the same after www normalization
 */
export function areDomainsEquivalent(domain1: string, domain2: string): boolean {
  if (!domain1 || !domain2 || typeof domain1 !== 'string' || typeof domain2 !== 'string') {
    return false;
  }
  
  const normalize = (domain: string) => {
    let normalized = domain.toLowerCase().trim();
    if (normalized.startsWith('www.')) {
      normalized = normalized.substring(4);
    }
    return normalized;
  };
  
  return normalize(domain1) === normalize(domain2);
}

/**
 * Validates domain format - allows both top-level domains and subdomains
 * Examples: example.com, sub.example.com, news.facts.dev
 */
export function validateDomainFormat(domain: string): boolean {
  if (!domain || typeof domain !== 'string') {
    return false;
  }

  // Remove protocol and www if present
  const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];

  // Must have at least 2 parts (domain.tld)
  const parts = cleanDomain.split('.');
  if (parts.length < 2) {
    return false;
  }

  // Validate each part
  for (const part of parts) {
    if (!part || part.length === 0) {
      return false;
    }
    // Each part should contain only valid characters
    if (!/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/.test(part)) {
      return false;
    }
  }

  // The last part (TLD) should be at least 2 characters and contain only letters
  const tld = parts[parts.length - 1];
  if (tld.length < 2 || !/^[a-zA-Z]{2,}$/.test(tld)) {
    return false;
  }

  return true;
}

/**
 * Extracts and validates domain from URL (supports both top-level domains and subdomains)
 * Returns empty string if not a valid domain
 */
export function extractTopLevelDomain(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }

  const cleanDomain = url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];

  if (validateDomainFormat(cleanDomain)) {
    return cleanDomain.toLowerCase();
  }

  return '';
}

/**
 * Gets the canonical domain (without www prefix)
 * Validates that it's a valid domain (supports both top-level domains and subdomains)
 */
export function getCanonicalDomain(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }
  
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    let hostname = urlObj.hostname.toLowerCase();
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    // Validate it's a valid domain
    if (validateDomainFormat(hostname)) {
      return hostname;
    }
    
    return '';
  } catch {
    const cleanDomain = url.toLowerCase().replace(/^www\./, '');
    return validateDomainFormat(cleanDomain) ? cleanDomain : '';
  }
}

/**
 * Batch normalize URLs - useful for processing multiple URLs
 */
export function batchNormalizeUrls(urls: string[]): NormalizedUrl[] {
  if (!Array.isArray(urls)) {
    return [];
  }
  
  return urls
    .filter(url => url && typeof url === 'string')
    .map(url => normalizeUrl(url));
}

/**
 * Find duplicate URLs in an array after normalization
 */
export function findDuplicateUrls(urls: string[]): { url: string; duplicates: string[] }[] {
  if (!Array.isArray(urls)) {
    return [];
  }
  
  const normalizedMap = new Map<string, string[]>();
  
  urls
    .filter(url => url && typeof url === 'string')
    .forEach(url => {
      const normalized = normalizeUrl(url).normalized;
      if (!normalizedMap.has(normalized)) {
        normalizedMap.set(normalized, []);
      }
      normalizedMap.get(normalized)!.push(url);
    });
  
  return Array.from(normalizedMap.entries())
    .filter(([_, urls]) => urls.length > 1)
    .map(([normalized, urls]) => ({
      url: normalized,
      duplicates: urls
    }));
}

/**
 * Normalize source URL to domain-only format: https://www.domain.com
 * Used specifically for source_url fields where we want to group by domain
 */
export function normalizeSourceUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }
  
  try {
    // Handle URLs without protocol
    let fullUrl = url.trim();
    if (!fullUrl.match(/^https?:\/\//)) {
      fullUrl = `https://${fullUrl}`;
    }

    const urlObj = new URL(fullUrl);
    
    // Get hostname and normalize to lowercase
    let hostname = urlObj.hostname.toLowerCase();
    
    // Always add www. prefix if not present (for consistency in source URLs)
    if (!hostname.startsWith('www.')) {
      hostname = `www.${hostname}`;
    }
    
    // Return domain-only URL with https and www prefix
    return `https://${hostname}`;
  } catch (error) {
    // If URL parsing fails, return a cleaned version
    let cleanUrl = url.trim().toLowerCase();
    
    // Remove protocol if present
    cleanUrl = cleanUrl.replace(/^https?:\/\//, '');
    
    // Remove path, query, and fragment
    cleanUrl = cleanUrl.split('/')[0].split('?')[0].split('#')[0];
    
    // Add www if not present
    if (!cleanUrl.startsWith('www.')) {
      cleanUrl = `www.${cleanUrl}`;
    }
    
    return `https://${cleanUrl}`;
  }
}

/**
 * Group discovered links by normalized source URL
 * Returns a map where key is the normalized source URL and value is an array of links
 */
export function groupLinksBySourceUrl<T extends { source_url: string }>(
  links: T[]
): Map<string, T[]> {
  if (!Array.isArray(links)) {
    return new Map();
  }
  
  const groupedLinks = new Map<string, T[]>();
  
  links
    .filter(link => link && link.source_url && typeof link.source_url === 'string')
    .forEach(link => {
      const normalizedSourceUrl = normalizeSourceUrl(link.source_url);
      
      if (!normalizedSourceUrl) {
        return; // Skip if normalization failed
      }
      
      if (!groupedLinks.has(normalizedSourceUrl)) {
        groupedLinks.set(normalizedSourceUrl, []);
      }
      
      groupedLinks.get(normalizedSourceUrl)!.push(link);
    });
  
  return groupedLinks;
}

/**
 * Get source domain statistics from grouped links
 */
export function getSourceDomainStats<T extends { source_url: string }>(
  links: T[]
): Array<{ source_url: string; count: number; links: T[] }> {
  if (!Array.isArray(links)) {
    return [];
  }
  
  const grouped = groupLinksBySourceUrl(links);
  
  return Array.from(grouped.entries()).map(([sourceUrl, linksList]) => ({
    source_url: sourceUrl,
    count: linksList.length,
    links: linksList
  }));
}