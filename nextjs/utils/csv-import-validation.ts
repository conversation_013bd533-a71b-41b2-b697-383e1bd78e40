/**
 * Utilities for CSV import validation and URL checking
 */

export interface ValidationResult {
  isValid: boolean;
  isAccessible?: boolean;
  statusCode?: number;
  error?: string;
  redirectUrl?: string;
}

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingLink?: any;
  similarity?: number;
  type: 'exact' | 'similar' | 'none';
}

/**
 * Validates if a URL is properly formatted and accessible
 */
export async function validateUrl(url: string): Promise<ValidationResult> {
  try {
    // Basic URL format validation
    const urlObj = new URL(url);
    
    // Check if protocol is http or https
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return {
        isValid: false,
        error: 'URL must use HTTP or HTTPS protocol'
      };
    }

    // Check accessibility with a HEAD request
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'LinkTrackPro-Validator/1.0'
        }
      });

      clearTimeout(timeoutId);

      return {
        isValid: true,
        isAccessible: response.ok,
        statusCode: response.status,
        redirectUrl: response.url !== url ? response.url : undefined
      };
    } catch (fetchError) {
      // If HEAD fails, try GET with a smaller timeout
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const response = await fetch(url, {
          method: 'GET',
          signal: controller.signal,
          headers: {
            'User-Agent': 'LinkTrackPro-Validator/1.0'
          }
        });

        clearTimeout(timeoutId);

        return {
          isValid: true,
          isAccessible: response.ok,
          statusCode: response.status,
          redirectUrl: response.url !== url ? response.url : undefined
        };
      } catch (getError) {
        return {
          isValid: true,
          isAccessible: false,
          error: fetchError instanceof Error ? fetchError.message : 'Network error'
        };
      }
    }
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Invalid URL format'
    };
  }
}

/**
 * Checks if a URL is a duplicate of existing links
 */
export async function checkDuplicate(
  url: string, 
  projectId: string, 
  existingLinks: any[]
): Promise<DuplicateCheckResult> {
  try {
    const normalizedUrl = normalizeUrlForComparison(url);
    
    // Check for exact matches
    const exactMatch = existingLinks.find(link => 
      normalizeUrlForComparison(link.url) === normalizedUrl
    );
    
    if (exactMatch) {
      return {
        isDuplicate: true,
        existingLink: exactMatch,
        type: 'exact',
        similarity: 1.0
      };
    }

    // Check for similar URLs (same domain, similar path)
    const urlObj = new URL(url);
    const domain = urlObj.hostname.replace(/^www\./, '');
    
    const similarLinks = existingLinks.filter(link => {
      try {
        const existingUrlObj = new URL(link.url);
        const existingDomain = existingUrlObj.hostname.replace(/^www\./, '');
        return domain === existingDomain;
      } catch {
        return false;
      }
    });

    if (similarLinks.length > 0) {
      // Calculate similarity based on path similarity
      const bestMatch = similarLinks.reduce((best, current) => {
        const similarity = calculateUrlSimilarity(url, current.url);
        return similarity > (best.similarity || 0) ? { ...current, similarity } : best;
      }, { similarity: 0 });

      if (bestMatch.similarity && bestMatch.similarity > 0.8) {
        return {
          isDuplicate: true,
          existingLink: bestMatch,
          type: 'similar',
          similarity: bestMatch.similarity
        };
      }
    }

    return {
      isDuplicate: false,
      type: 'none'
    };
  } catch (error) {
    console.error('Error checking duplicate:', error);
    return {
      isDuplicate: false,
      type: 'none'
    };
  }
}

/**
 * Normalizes URL for comparison (removes protocol, www, trailing slash, etc.)
 */
function normalizeUrlForComparison(url: string): string {
  try {
    const urlObj = new URL(url);
    let normalized = urlObj.hostname.replace(/^www\./, '') + urlObj.pathname;
    
    // Remove trailing slash
    if (normalized.endsWith('/') && normalized.length > 1) {
      normalized = normalized.slice(0, -1);
    }
    
    // Add query parameters if they exist
    if (urlObj.search) {
      normalized += urlObj.search;
    }
    
    return normalized.toLowerCase();
  } catch {
    return url.toLowerCase();
  }
}

/**
 * Calculates similarity between two URLs (0-1 scale)
 */
function calculateUrlSimilarity(url1: string, url2: string): number {
  try {
    const obj1 = new URL(url1);
    const obj2 = new URL(url2);
    
    // If different domains, return 0
    const domain1 = obj1.hostname.replace(/^www\./, '');
    const domain2 = obj2.hostname.replace(/^www\./, '');
    if (domain1 !== domain2) return 0;
    
    // Calculate path similarity using Levenshtein distance
    const path1 = obj1.pathname;
    const path2 = obj2.pathname;
    
    if (path1 === path2) return 1.0;
    
    const maxLength = Math.max(path1.length, path2.length);
    if (maxLength === 0) return 1.0;
    
    const distance = levenshteinDistance(path1, path2);
    return 1 - (distance / maxLength);
  } catch {
    return 0;
  }
}

/**
 * Calculates Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Batch validates multiple URLs with rate limiting
 */
export async function batchValidateUrls(
  urls: string[], 
  onProgress?: (completed: number, total: number) => void
): Promise<Map<string, ValidationResult>> {
  const results = new Map<string, ValidationResult>();
  const batchSize = 5; // Process 5 URLs at a time to avoid overwhelming the server
  
  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    const batchPromises = batch.map(async (url) => {
      const result = await validateUrl(url);
      results.set(url, result);
      return result;
    });
    
    await Promise.all(batchPromises);
    
    if (onProgress) {
      onProgress(Math.min(i + batchSize, urls.length), urls.length);
    }
    
    // Small delay between batches to be respectful
    if (i + batchSize < urls.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}
