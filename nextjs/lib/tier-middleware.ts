import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { findUserByUuid, getUserByApiKey } from '@/models/user';
import { 
  canCreateProject, 
  canAddDomain, 
  canAddLinkResource, 
  canMakeDrQuery, 
  canMakeTrafficUpdate,
  recordDrQueryUsage,
  recordTrafficUpdateUsage,
  TierValidationResult,
  getUserUsageSummary
} from '@/models/user-tier';
import { withRateLimit, RATE_LIMITS } from '@/lib/rate-limit';
import { 
  TierMiddlewareOptionsSchema, 
  sanitizeMetadata as validateMetadata,
  VALIDATION_ERROR_MESSAGES,
  type TierAction
} from '@/lib/validation/tier-schemas';

// TierAction type is now imported from validation schemas

export interface TierMiddlewareOptions {
  action: TierAction;
  recordUsage?: boolean;
  projectId?: string;
  apiEndpoint?: string;
  metadata?: any;
}

export interface TierMiddlewareResult {
  success: boolean;
  userUuid?: string;
  error?: string;
  status?: number;
  validationResult?: TierValidationResult;
}

// Main tier validation middleware
export async function validateTierAccess(
  request: NextRequest,
  options: TierMiddlewareOptions
): Promise<TierMiddlewareResult> {
  try {
    // Validate input options using Zod schema
    const validationResult = TierMiddlewareOptionsSchema.safeParse(options);
    if (!validationResult.success) {
      return {
        success: false,
        error: VALIDATION_ERROR_MESSAGES.INVALID_TIER_ACTION,
        status: 400
      };
    }

    const validatedOptions = validationResult.data;

    // Get user from session or API key
    const userUuid = await getUserUuidFromRequest(request);
    
    if (!userUuid) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      };
    }

    // Validate the specific action
    const actionValidationResult = await validateAction(userUuid, validatedOptions.action!);
    
    if (!actionValidationResult.allowed) {
      return {
        success: false,
        error: actionValidationResult.message || 'Action not allowed',
        status: 403,
        validationResult: actionValidationResult
      };
    }

    // Record usage if requested
    if (validatedOptions.recordUsage) {
      await recordUsageForAction(userUuid, {
        action: validatedOptions.action!,
        recordUsage: validatedOptions.recordUsage,
        projectId: validatedOptions.projectId,
        apiEndpoint: validatedOptions.apiEndpoint,
        metadata: validatedOptions.metadata
      });
    }

    return {
      success: true,
      userUuid,
      validationResult: actionValidationResult
    };
  } catch (error) {
    console.error('Tier validation error:', error);
    return {
      success: false,
      error: 'Internal server error',
      status: 500
    };
  }
}

// Get user UUID from request (session or API key)
async function getUserUuidFromRequest(request: NextRequest): Promise<string | null> {
  // Try to get user from NextAuth session
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET!
  });
  
  if (token?.user && typeof token.user === 'object' && 'uuid' in token.user && typeof token.user.uuid === 'string') {
    // Use the UUID from our custom user object in the token
    return token.user.uuid;
  } else if (token?.sub) {
    // Fallback to token.sub for backward compatibility
    const user = await findUserByUuid(token.sub);
    return user?.uuid || null;
  }

  // Try to get user from API key
  const apiKey = request.headers.get('x-api-key') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  if (apiKey) {
    const user = await getUserByApiKey(apiKey);
    return user?.uuid || null;
  }

  return null;
}

// Validate specific action based on user tier
async function validateAction(userUuid: string, action: TierAction): Promise<TierValidationResult> {
  switch (action) {
    case 'create_project':
      return await canCreateProject(userUuid);
    
    case 'add_domain':
      return await canAddDomain(userUuid);
    
    case 'add_link_resource':
      return await canAddLinkResource(userUuid);
    
    case 'dr_query':
      return await canMakeDrQuery(userUuid);
    
    case 'traffic_update':
      return await canMakeTrafficUpdate(userUuid);
    
    default:
      return {
        allowed: false,
        message: 'Unknown action'
      };
  }
}

// Sanitize input to prevent injection attacks
function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  return input.slice(0, 500).replace(/[<>"\';\\]/g, '');
}

function sanitizeMetadata(metadata: any): any {
  if (typeof metadata !== 'object' || metadata === null) {
    return {};
  }
  
  const sanitized: any = {};
  for (const [key, value] of Object.entries(metadata)) {
    if (typeof key === 'string' && key.length <= 100) {
      const sanitizedKey = sanitizeInput(key);
      if (sanitizedKey) {
        sanitized[sanitizedKey] = typeof value === 'string' 
          ? sanitizeInput(value) 
          : value;
      }
    }
  }
  return sanitized;
}

// Record usage for specific actions
async function recordUsageForAction(userUuid: string, options: TierMiddlewareOptions): Promise<void> {
  const sanitizedProjectId = options.projectId ? sanitizeInput(options.projectId) : undefined;
  const sanitizedApiEndpoint = options.apiEndpoint ? sanitizeInput(options.apiEndpoint) : undefined;
  const sanitizedMetadata = validateMetadata(options.metadata || {});

  switch (options.action) {
    case 'dr_query':
      await recordDrQueryUsage(userUuid, sanitizedProjectId, sanitizedApiEndpoint, sanitizedMetadata);
      break;
    
    case 'traffic_update':
      await recordTrafficUpdateUsage(userUuid, sanitizedProjectId, sanitizedApiEndpoint, sanitizedMetadata);
      break;
    
    // Other actions don't need usage recording
    default:
      break;
  }
}

// Standardized error messages to prevent information disclosure
const STANDARD_ERROR_MESSAGES = {
  TIER_LIMIT_EXCEEDED: 'Plan limit reached. Upgrade to continue.',
  AUTHENTICATION_REQUIRED: 'Please sign in to access this feature.',
  INSUFFICIENT_PERMISSIONS: 'This feature requires a higher tier plan.',
  INTERNAL_ERROR: 'Service temporarily unavailable. Please try again.',
  INVALID_REQUEST: 'Invalid request parameters.',
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later.'
};

// Helper function to create standardized error responses
export function createTierErrorResponse(result: TierMiddlewareResult): NextResponse {
  const status = result.status || 403;
  
  let errorMessage = STANDARD_ERROR_MESSAGES.TIER_LIMIT_EXCEEDED;
  let errorCode = 'TIER_LIMIT_EXCEEDED';
  
  if (status === 401) {
    errorMessage = STANDARD_ERROR_MESSAGES.AUTHENTICATION_REQUIRED;
    errorCode = 'AUTHENTICATION_REQUIRED';
  } else if (status === 500) {
    errorMessage = STANDARD_ERROR_MESSAGES.INTERNAL_ERROR;
    errorCode = 'INTERNAL_ERROR';
  } else if (status === 400) {
    errorMessage = STANDARD_ERROR_MESSAGES.INVALID_REQUEST;
    errorCode = 'INVALID_REQUEST';
  }
  
  const response = {
    error: errorMessage,
    code: errorCode,
    details: result.validationResult ? {
      currentUsage: result.validationResult.currentUsage,
      limit: result.validationResult.limit
    } : undefined
  };

  return NextResponse.json(response, { status });
}

// Helper function to create success response with usage info
export async function createTierSuccessResponse(data: any, userUuid: string): Promise<NextResponse> {
  try {
    console.log("createTierSuccessResponse - Getting usage summary for user:", userUuid);
    const usage = await getUserUsageSummary(userUuid);
    console.log("createTierSuccessResponse - Usage summary retrieved:", usage ? "success" : "null");

    const response = {
      ...data,
      usage: usage ? {
        tier: usage.tier,
        subscription_status: usage.subscription_status,
        limits: {
          projects: { used: usage.projects_count, limit: usage.projects_limit },
          domains: { used: usage.domains_count, limit: usage.domains_limit },
          link_resources: { used: usage.link_resources_count, limit: usage.link_resources_limit },
          monthly_dr_queries: { used: usage.monthly_dr_queries_used, limit: usage.monthly_dr_queries_limit },
          monthly_traffic_updates: { used: usage.monthly_traffic_updates_used, limit: usage.monthly_traffic_updates_limit }
        }
      } : undefined
    };

    console.log("createTierSuccessResponse - Response created successfully");
    return NextResponse.json(response);
  } catch (error) {
    console.error("createTierSuccessResponse - Error getting usage summary:", error);
    console.error("createTierSuccessResponse - Error stack:", error instanceof Error ? error.stack : 'No stack trace');

    // Fallback: return response without usage info
    console.log("createTierSuccessResponse - Falling back to response without usage info");
    return NextResponse.json(data);
  }
}

// Middleware wrapper function for easy use in API routes
export function withTierValidation(
  options: TierMiddlewareOptions,
  handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any) => {
    const result = await validateTierAccess(request, options);
    
    if (!result.success) {
      return createTierErrorResponse(result);
    }
    
    return handler(request, context, result.userUuid!);
  };
}

// Specific middleware functions for common actions
export const requireProjectCreation = (handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>) =>
  withTierValidation({ action: 'create_project' }, handler);

export const requireDomainAddition = (handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>) =>
  withTierValidation({ action: 'add_domain' }, handler);

export const requireLinkResourceAddition = (handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>) =>
  withTierValidation({ action: 'add_link_resource' }, handler);

export const requireDrQuery = (
  projectId?: string,
  apiEndpoint?: string,
  metadata?: any
) => (handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>) =>
  withRateLimit(RATE_LIMITS.DR_QUERIES, 
    withTierValidation({ 
      action: 'dr_query', 
      recordUsage: true,
      projectId,
      apiEndpoint,
      metadata 
    }, handler)
  );

export const requireTrafficUpdate = (
  projectId?: string,
  apiEndpoint?: string,
  metadata?: any
) => (handler: (request: NextRequest, context: any, userUuid: string) => Promise<NextResponse>) =>
  withRateLimit(RATE_LIMITS.TRAFFIC_UPDATES,
    withTierValidation({ 
      action: 'traffic_update', 
      recordUsage: true,
      projectId,
      apiEndpoint,
      metadata 
    }, handler)
  );

// Utility function to get usage summary for user
export async function getUserTierInfo(userUuid: string) {
  const usage = await getUserUsageSummary(userUuid);
  
  if (!usage) {
    return null;
  }
  
  return {
    tier: usage.tier,
    subscription_status: usage.subscription_status,
    limits: {
      projects: {
        used: usage.projects_count,
        limit: usage.projects_limit,
        canCreate: usage.projects_limit === -1 || usage.projects_count < usage.projects_limit
      },
      domains: {
        used: usage.domains_count,
        limit: usage.domains_limit,
        canAdd: usage.domains_limit === -1 || usage.domains_count < usage.domains_limit
      },
      link_resources: {
        used: usage.link_resources_count,
        limit: usage.link_resources_limit,
        canAdd: usage.link_resources_limit === -1 || usage.link_resources_count < usage.link_resources_limit
      },
      monthly_dr_queries: {
        used: usage.monthly_dr_queries_used,
        limit: usage.monthly_dr_queries_limit,
        canUse: usage.monthly_dr_queries_limit === -1 || usage.monthly_dr_queries_used < usage.monthly_dr_queries_limit
      },
      monthly_traffic_updates: {
        used: usage.monthly_traffic_updates_used,
        limit: usage.monthly_traffic_updates_limit,
        canUse: usage.monthly_traffic_updates_limit === -1 || usage.monthly_traffic_updates_used < usage.monthly_traffic_updates_limit
      }
    },
    usage_reset_date: usage.usage_reset_date
  };
}