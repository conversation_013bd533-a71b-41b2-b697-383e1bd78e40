import { getSupabaseClient } from "./db";
import { 
  LinkResource, 
  LinkResourceWithStats, 
  Project, 
  ProjectWithStats, 
  AllLinks, 
  AllLinksHistory, 
  LinkImportDataNew, 
  DiscoveredLink, 
  DiscoveredLinkWithStats,
  DomainStats,
  ProjectCategoryStats
} from "@/types/links";
import { PostgrestError } from "@supabase/supabase-js";
import { normalizeUrl, getCanonicalDomain, normalizeSourceUrl } from "@/utils/url-normalization";

// Helper function to normalize link_type to array format
function normalizeLinkType(linkType: string | ('free' | 'paid')[] | undefined): ('free' | 'paid')[] {
  if (!linkType) return ['free'];
  if (Array.isArray(linkType)) return linkType;

  // Handle string input - could be comma-separated values
  if (typeof linkType === 'string') {
    const types = linkType.split(',').map(t => t.trim()).filter(t => t === 'free' || t === 'paid');
    return types.length > 0 ? types as ('free' | 'paid')[] : ['free'];
  }

  return ['free'];
}

// ========================================
// LINK RESOURCES CRUD OPERATIONS
// ========================================

export async function createLinkResource(
  linkData: Omit<LinkResource, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: LinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  // Normalize URL before storing
  const normalizedUrl = normalizeUrl(linkData.url);
  
  // Check if a link with the same normalized URL already exists for this user
  const { data: existingLinks } = await client
    .from("link_resources")
    .select("id, url")
    .eq('user_id', linkData.user_id);
    
  if (existingLinks && existingLinks.length > 0) {
    const existingLink = existingLinks.find(link => {
      const existingNormalized = normalizeUrl(link.url);
      return existingNormalized.normalized === normalizedUrl.normalized;
    });
    
    if (existingLink) {
      return { 
        data: null, 
        error: { message: `Link already exists: ${existingLink.url}` } as PostgrestError 
      };
    }
  }
  
  const linkId = crypto.randomUUID();
  
  const { data, error } = await client
    .from("link_resources")
    .insert({
      id: linkId,
      ...linkData,
      url: normalizedUrl.normalized, // Store normalized URL
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  return { data, error };
}

export async function updateLinkResource(
  id: string, 
  linkData: Partial<LinkResource>
): Promise<{ data: LinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Normalize URL if it's being updated
  const updateData = { ...linkData };
  if (linkData.url) {
    const normalizedUrl = normalizeUrl(linkData.url);
    updateData.url = normalizedUrl.normalized;
  }

  const { data, error } = await client
    .from("link_resources")
    .update({
      ...updateData,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  return { data, error };
}

export async function deleteLinkResource(id: string): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("link_resources")
    .delete()
    .eq('id', id);

  return { error };
}

export async function getLinkResourcesByUser(
  user_id: string, 
  limit = 50, 
  offset = 0
): Promise<{ data: LinkResource[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();

  const { data, error, count } = await client
    .from("link_resources")
    .select("*", { count: 'exact' })
    .eq('user_id', user_id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  return { data, error, count };
}

export async function getLinkResourcesWithStats(
  user_id: string, 
  limit = 50, 
  offset = 0
): Promise<{ data: LinkResourceWithStats[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();

  // Get link resources
  const { data: linkResources, error: linksError, count } = await client
    .from("link_resources")
    .select("*", { count: 'exact' })
    .eq('user_id', user_id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (linksError || !linkResources) {
    return { data: null, error: linksError, count };
  }

  // Extract domains from URLs using canonical domain extraction
  const domains = linkResources.map(link => getCanonicalDomain(link.url)).filter(domain => domain);

  // Get domain stats
  const { data: domainStats } = await client
    .from("all_links")
    .select("*")
    .in('domain', domains);

  // Create lookup map
  const statsMap = (domainStats || []).reduce((map, stats) => {
    map[stats.domain] = stats;
    return map;
  }, {} as Record<string, AllLinks>);

  // Merge data
  const linkResourcesWithStats: LinkResourceWithStats[] = linkResources.map(link => {
    const domain = getCanonicalDomain(link.url);
    
    const stats = statsMap[domain];
    
    return {
      ...link,
      domain,
      dr_score: stats?.dr_score,
      traffic: stats?.traffic || 0,
      is_indexed: stats?.is_indexed || false,
      status: 'active' // Default status for backward compatibility
    };
  });

  return { data: linkResourcesWithStats, error: null, count };
}

// ========================================
// PROJECTS CRUD OPERATIONS  
// ========================================

export async function createProject(
  projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: Project | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const projectId = crypto.randomUUID();

  const { data, error } = await client
    .from("projects")
    .insert({
      id: projectId,
      ...projectData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  return { data, error };
}

export async function updateProject(
  id: string, 
  projectData: Partial<Project>
): Promise<{ data: Project | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("projects")
    .update({
      ...projectData,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  return { data, error };
}

export async function deleteProject(id: string): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Discovered links will be deleted automatically due to CASCADE
  const { error } = await client
    .from("projects")
    .delete()
    .eq('id', id);

  return { error };
}

export async function getProjectsByUser(user_id: string): Promise<{ data: Project[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("projects")
    .select("*")
    .eq('user_id', user_id)
    .order('created_at', { ascending: false });

  return { data, error };
}

export async function getProjectById(id: string, user_id: string): Promise<{ data: Project | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("projects")
    .select("*")
    .eq('id', id)
    .eq('user_id', user_id)
    .single();

  return { data, error };
}

export async function getProjectWithStats(id: string, user_id?: string): Promise<{ data: ProjectWithStats | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Get project
  let query = client
    .from("projects")
    .select("*")
    .eq('id', id);
    
  if (user_id) {
    query = query.eq('user_id', user_id);
  }

  const { data: project, error: projectError } = await query.single();

  if (projectError || !project) {
    return { data: null, error: projectError };
  }

  // Get domain stats
  const { data: domainStats } = await client
    .from("all_links")
    .select("dr_score, traffic, is_indexed")
    .eq('domain', project.domain)
    .single();

  const projectWithStats: ProjectWithStats = {
    ...project,
    dr_score: domainStats?.dr_score,
    traffic: domainStats?.traffic || 0,
    is_indexed: domainStats?.is_indexed || false
  };

  return { data: projectWithStats, error: null };
}

// 获取用户的项目分类统计
export async function getUserProjectCategories(user_id: string): Promise<{ data: ProjectCategoryStats[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client.rpc('get_user_project_categories', {
    p_user_id: user_id
  });

  return { data, error };
}

// ========================================
// DOMAIN STATS OPERATIONS
// ========================================

export async function getDomainStats(domain: string): Promise<{ data: DomainStats | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("all_links")
    .select("*")
    .eq('domain', domain)
    .single();

  if (!data) {
    return { 
      data: { domain, dr_score: undefined, traffic: 0, is_indexed: false }, 
      error 
    };
  }

  return { 
    data: {
      domain: data.domain,
      dr_score: data.dr_score,
      traffic: data.traffic,
      is_indexed: data.is_indexed
    }, 
    error 
  };
}

export async function getDomainFullStats(domain: string): Promise<{ data: AllLinks | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("all_links")
    .select("*")
    .eq('domain', domain)
    .single();

  if (!data) {
    return { 
      data: null, 
      error 
    };
  }

  return { 
    data: {
      id: data.id,
      domain: data.domain,
      dr_score: data.dr_score,
      traffic: data.traffic || 0,
      backlink_count: data.backlink_count || 0,
      is_indexed: data.is_indexed || false,
      last_updated: data.last_updated
    }, 
    error 
  };
}

export async function updateDomainStats(
  domain: string,
  dr_score?: number,
  traffic: number = 0,
  is_indexed: boolean = false
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Use the database function we created in migration
  const { error } = await client.rpc('update_domain_stats', {
    p_domain: domain,
    p_dr_score: dr_score,
    p_traffic: traffic,
    p_is_indexed: is_indexed
  });

  return { error };
}

export async function getDomainStatsHistory(
  domain: string, 
  limit = 30
): Promise<{ data: AllLinksHistory[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("all_links_history")
    .select("*")
    .eq('domain', domain)
    .order('checked_at', { ascending: false })
    .limit(limit);

  return { data, error };
}

// ========================================
// DISCOVERED LINKS OPERATIONS
// ========================================

export async function createDiscoveredLink(
  linkData: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: DiscoveredLink | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Normalize URL before storing
  const normalizedUrl = normalizeUrl(linkData.url);
  
  // Check if a discovered link with the same normalized URL already exists for this project
  const { data: existingLinks } = await client
    .from("discovered_links")
    .select("id, url")
    .eq('project_id', linkData.project_id);
    
  if (existingLinks && existingLinks.length > 0) {
    const existingLink = existingLinks.find(link => {
      const existingNormalized = normalizeUrl(link.url);
      return existingNormalized.normalized === normalizedUrl.normalized;
    });
    
    if (existingLink) {
      return { 
        data: null, 
        error: { message: `Discovered link already exists: ${existingLink.url}` } as PostgrestError 
      };
    }
  }

  const linkId = crypto.randomUUID();

  const { data, error } = await client
    .from("discovered_links")
    .insert({
      id: linkId,
      ...linkData,
      url: normalizedUrl.normalized, // Store normalized URL
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  return { data, error };
}

export async function getDiscoveredLinksByProject(
  project_id: string, 
  user_id: string
): Promise<{ data: DiscoveredLink[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("discovered_links")
    .select("*")
    .eq('project_id', project_id)
    .eq('user_id', user_id)
    .order('discovered_at', { ascending: false });

  return { data, error };
}

export async function getDiscoveredLinksWithStats(
  project_id: string, 
  user_id: string,
  includeArchived: boolean = false
): Promise<{ data: DiscoveredLinkWithStats[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Get discovered links (exclude ARCHIVED by default)
  let query = client
    .from("discovered_links")
    .select("*")
    .eq('project_id', project_id)
    .eq('user_id', user_id);
  
  // Filter out ARCHIVED links unless explicitly requested
  if (!includeArchived) {
    query = query.neq('status', 'ARCHIVED');
  }
  
  const { data: discoveredLinks, error: linksError } = await query
    .order('discovered_at', { ascending: false });

  if (linksError || !discoveredLinks) {
    return { data: null, error: linksError };
  }

  // Extract domains from URLs using canonical domain extraction
  const domains = discoveredLinks.map(link => getCanonicalDomain(link.url)).filter(domain => domain);

  // Get domain stats
  const { data: domainStats } = await client
    .from("all_links")
    .select("*")
    .in('domain', domains);

  // Create lookup map
  const statsMap = (domainStats || []).reduce((map, stats) => {
    map[stats.domain] = stats;
    return map;
  }, {} as Record<string, AllLinks>);

  // Merge data
  const discoveredLinksWithStats: DiscoveredLinkWithStats[] = discoveredLinks.map(link => {
    const domain = getCanonicalDomain(link.url);

    const stats = statsMap[domain];

    // For synced links from link_resources (where url === source_url),
    // consider them as indexed since they are known promotional platforms
    const isSyncedFromLinkResources = link.url === link.source_url;
    const isIndexed = isSyncedFromLinkResources ? true : (stats?.is_indexed || false);

    return {
      ...link,
      domain,
      dr_score: stats?.dr_score,
      traffic: stats?.traffic || 0,
      is_indexed: isIndexed,
      status: 'active' // Default status for backward compatibility
    };
  });

  return { data: discoveredLinksWithStats, error: null };
}

export async function archiveDiscoveredLink(id: string): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("discovered_links")
    .update({
      status: 'ARCHIVED',
      is_active: false,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id);

  return { error };
}

export async function updateDiscoveredLinkStatus(
  id: string, 
  status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED'
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("discovered_links")
    .update({
      status: status,
      is_active: status !== 'ARCHIVED',
      updated_at: new Date().toISOString(),
    })
    .eq('id', id);

  return { error };
}

// ========================================
// DISCOVERED LINKS MANAGEMENT OPERATIONS
// ========================================

export async function getDiscoveredLinkById(
  id: string,
  project_id: string,
  user_id: string
): Promise<{ data: DiscoveredLink | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("discovered_links")
    .select("*")
    .eq("id", id)
    .eq("project_id", project_id)
    .eq("user_id", user_id)
    .single();

  return { data, error };
}

export async function updateDiscoveredLink(
  id: string,
  updateData: Partial<DiscoveredLink>
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("discovered_links")
    .update({
      ...updateData,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id);

  return { error };
}

export async function convertDiscoveredLinkToResource(
  discoveredLinkId: string,
  linkResourceData?: Partial<LinkResource>
): Promise<{ data: LinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // First get the discovered link
  const { data: discoveredLink, error: fetchError } = await client
    .from("discovered_links")
    .select("*")
    .eq("id", discoveredLinkId)
    .single();

  if (fetchError || !discoveredLink) {
    return { data: null, error: fetchError };
  }

  // Create link resource payload
  const linkResourcePayload = {
    url: discoveredLink.url,
    title: discoveredLink.title || `Link from ${new URL(discoveredLink.url).hostname}`,
    link_type: normalizeLinkType(linkResourceData?.link_type),
    price: linkResourceData?.price,
    currency: linkResourceData?.currency || 'USD',
    source: linkResourceData?.source || 'Discovery',
    acquisition_method: linkResourceData?.acquisition_method || 'Automated Discovery',
    notes: linkResourceData?.notes || `Converted from discovered link on ${new Date().toLocaleDateString()}`,
    user_id: discoveredLink.user_id
  };

  // Create the link resource
  const { data: linkResource, error: createError } = await createLinkResource(linkResourcePayload);

  if (createError) {
    return { data: null, error: createError };
  }

  // Update discovered link status
  await updateDiscoveredLinkStatus(discoveredLinkId, 'SUBMITTED');

  return { data: linkResource, error: null };
}

export async function checkSourceUrlExists(
  sourceUrl: string
): Promise<{ exists: boolean, error: Error | null }> {
  try {
    const response = await fetch(sourceUrl, {
      method: 'HEAD',
      headers: {
        'User-Agent': 'MyBackLinks/1.0 (Link Discovery Bot)'
      },
      // Set a reasonable timeout
      signal: AbortSignal.timeout(10000) // 10 seconds
    });

    return { exists: response.ok, error: null };
  } catch (error) {
    console.error('Error checking source URL existence:', error);
    return { 
      exists: false, 
      error: error instanceof Error ? error : new Error('Unknown error') 
    };
  }
}

export async function getDiscoveredLinkForIndexCheck(
  id: string
): Promise<{ data: { project_id: string, source_url: string } | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("discovered_links")
    .select("project_id, source_url")
    .eq("id", id)
    .single();

  return { data, error };
}

export async function updateDomainStatsInAllLinks(
  domain: string,
  statsData: {
    dr_score?: number;
    traffic?: number;
    is_indexed?: boolean;
  }
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const updateData: any = {
    last_updated: new Date().toISOString()
  };

  if (statsData.dr_score !== undefined) {
    updateData.dr_score = parseInt(statsData.dr_score.toString());
  }
  if (statsData.traffic !== undefined) {
    updateData.traffic = parseInt(statsData.traffic.toString());
  }
  if (statsData.is_indexed !== undefined) {
    updateData.is_indexed = statsData.is_indexed;
  }

  const { error } = await client
    .from("all_links")
    .upsert({
      domain,
      ...updateData
    }, {
      onConflict: 'domain'
    });

  return { error };
}

export async function getLinkResourceById(
  id: string,
  user_id: string
): Promise<{ data: LinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("link_resources")
    .select("*")
    .eq("id", id)
    .eq("user_id", user_id)
    .single();

  return { data, error };
}

// ========================================
// CSV IMPORT OPERATIONS
// ========================================

export async function getProjectForUser(
  projectId: string,
  userId: string
): Promise<{ data: Project | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("projects")
    .select("*")
    .eq("id", projectId)
    .eq("user_id", userId)
    .single();

  return { data, error };
}

export async function getDiscoveredLinkByUrl(
  projectId: string,
  url: string
): Promise<{ data: DiscoveredLink | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("discovered_links")
    .select("*")
    .eq("project_id", projectId)
    .eq("url", url)
    .limit(1)
    .single();

  return { data, error };
}

export async function updateDiscoveredLinkFromImport(
  linkId: string,
  updateData: {
    title?: string;
    anchor_text?: string;
    status?: string;
  }
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("discovered_links")
    .update({
      ...updateData,
      is_active: true,
      updated_at: new Date().toISOString(),
    })
    .eq("id", linkId);

  return { error };
}

export async function updateDomainStatsViaRPC(
  domain: string,
  dr_score?: number | null,
  traffic?: number,
  is_indexed?: boolean,
  backlink_count?: number
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client.rpc('update_domain_stats_with_backlinks', {
    p_domain: domain,
    p_dr_score: dr_score,
    p_traffic: traffic || 0,
    p_is_indexed: is_indexed || false,
    p_backlink_count: backlink_count || 0
  });

  return { error };
}


// Deprecated - use archiveDiscoveredLink instead
export async function deleteDiscoveredLink(id: string): Promise<{ error: PostgrestError | null }> {
  return archiveDiscoveredLink(id);
}

export async function batchCreateDiscoveredLinks(
  linksData: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'>[],
  project_id: string,
  user_id: string
): Promise<{ success: number, failed: number, errors: string[] }> {
  const client = getSupabaseClient();
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (const linkData of linksData) {
    try {
      // Normalize URL before checking for duplicates
      const normalizedUrl = normalizeUrl(linkData.url);
      
      // Check if link already exists by comparing normalized URLs
      const { data: existingLinks } = await client
        .from("discovered_links")
        .select("id, url")
        .eq('project_id', project_id);
        
      const existingLink = existingLinks?.find(link => {
        const existingNormalized = normalizeUrl(link.url);
        return existingNormalized.normalized === normalizedUrl.normalized;
      });

      if (existingLink) {
        // Update existing record
        const { error } = await client
          .from("discovered_links")
          .update({
            ...linkData,
            url: normalizedUrl.normalized, // Store normalized URL
            source_url: normalizeSourceUrl(linkData.source_url), // Normalize source URL to domain-only format
            updated_at: new Date().toISOString()
          })
          .eq('id', existingLink.id);

        if (error) {
          failed++;
          errors.push(`Failed to update ${linkData.url}: ${error.message}`);
        } else {
          success++;
        }
      } else {
        // Create new record
        const linkId = crypto.randomUUID();
        
        const { error } = await client
          .from("discovered_links")
          .insert({
            id: linkId,
            ...linkData,
            url: normalizedUrl.normalized, // Store normalized URL
            source_url: normalizeSourceUrl(linkData.source_url), // Normalize source URL to domain-only format
            project_id: project_id,
            user_id: user_id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (error) {
          failed++;
          errors.push(`Failed to create ${linkData.url}: ${error.message}`);
        } else {
          success++;
        }
      }
    } catch (error) {
      failed++;
      errors.push(`Failed to process ${linkData.url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { success, failed, errors };
}

// ========================================
// IMPORT OPERATIONS
// ========================================

export async function importLinkResources(
  user_id: string, 
  linksData: LinkImportDataNew[]
): Promise<{ success: number, failed: number, errors: string[] }> {
  const client = getSupabaseClient();
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (const linkData of linksData) {
    try {
      // Normalize URL before storing
      const normalizedUrl = normalizeUrl(linkData.url);
      const linkId = crypto.randomUUID();
      
      const { error } = await client
        .from("link_resources")
        .insert({
          id: linkId,
          url: normalizedUrl.normalized, // Store normalized URL
          title: linkData.title,
          link_type: normalizeLinkType(linkData.link_type),
          price: linkData.price,
          currency: 'USD',
          source: linkData.source,
          acquisition_method: linkData.acquisition_method,
          notes: linkData.notes,
          submit_url: linkData.submit_url,
          user_id: user_id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        failed++;
        errors.push(`Failed to import ${linkData.url}: ${error.message}`);
      } else {
        success++;
      }
    } catch (error) {
      failed++;
      errors.push(`Failed to import ${linkData.url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { success, failed, errors };
}

// ========================================
// PROJECT STATS UPDATE
// ========================================

export async function updateProjectStats(project_id: string): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Use the database function we created in migration
  const { error } = await client.rpc('update_project_stats', {
    p_project_id: project_id
  });

  return { error };
}

// ========================================
// ADMIN FUNCTIONS
// ========================================

export async function getAllLinkResourcesForAdmin(
  limit = 100, 
  offset = 0, 
  filters?: {
    search?: string;
    link_type?: string;
    user_id?: string;
  }
): Promise<{ 
  data: (LinkResource & { user_email?: string })[] | null; 
  error: PostgrestError | null; 
  count: number | null 
}> {
  const client = getSupabaseClient();

  let query = client
    .from("link_resources")
    .select('*', { count: 'exact' });

  // Apply filters
  if (filters?.search) {
    query = query.or(`title.ilike.%${filters.search}%,url.ilike.%${filters.search}%`);
  }
  
  if (filters?.link_type) {
    query = query.eq('link_type', filters.link_type);
  }
  
  if (filters?.user_id) {
    query = query.eq('user_id', filters.user_id);
  }

  const { data: linkResources, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error || !linkResources) {
    return { data: null, error, count };
  }

  // Get user emails
  const userIds = Array.from(new Set(linkResources.map(link => link.user_id)));
  const { data: users } = await client
    .from("users")
    .select('uuid, email')
    .in('uuid', userIds);

  const userMap = (users || []).reduce((map, user) => {
    map[user.uuid] = user.email;
    return map;
  }, {} as Record<string, string>);

  const transformedData = linkResources.map(link => ({
    ...link,
    user_email: userMap[link.user_id] || '未知用户',
    status: 'active' // Default status for backward compatibility
  }));

  return { data: transformedData, error, count };
}

export async function getAllProjectsForAdmin(
  limit = 100, 
  offset = 0, 
  filters?: {
    search?: string;
    user_id?: string;
  }
): Promise<{ 
  data: (Project & { user_email?: string; discovered_links_count?: number })[] | null; 
  error: PostgrestError | null; 
  count: number | null 
}> {
  const client = getSupabaseClient();

  let query = client
    .from("projects")
    .select('*', { count: 'exact' });

  // Apply filters
  if (filters?.search) {
    query = query.or(`name.ilike.%${filters.search}%,domain.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
  }
  
  if (filters?.user_id) {
    query = query.eq('user_id', filters.user_id);
  }

  const { data: projects, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error || !projects) {
    return { data: null, error, count };
  }

  // Get user emails
  const userIds = Array.from(new Set(projects.map(project => project.user_id)));
  const { data: users } = await client
    .from("users")
    .select('uuid, email')
    .in('uuid', userIds);

  // Get discovered links count per project
  const projectIds = projects.map(project => project.id);
  let linkCountMap: Record<string, number> = {};
  
  for (const projectId of projectIds) {
    const { count } = await client
      .from("discovered_links")
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId);
    
    linkCountMap[projectId] = count || 0;
  }

  const userMap = (users || []).reduce((map, user) => {
    map[user.uuid] = user.email;
    return map;
  }, {} as Record<string, string>);

  const transformedData = projects.map(project => ({
    ...project,
    user_email: userMap[project.user_id] || '未知用户',
    discovered_links_count: linkCountMap[project.id] || 0
  }));

  return { data: transformedData, error, count };
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

export function extractDomainFromUrl(url: string): string {
  return getCanonicalDomain(url);
}

export async function getAllUsersWithStats(): Promise<{
  data: Array<{
    user_id: string;
    user_email: string;
    projects_count: number;
    link_resources_count: number;
    discovered_links_count: number;
    last_active?: string;
  }> | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();

  try {
    // Get all users
    const { data: users, error: usersError } = await client
      .from("users")
      .select('uuid, email, updated_at');

    if (usersError) {
      return { data: null, error: usersError };
    }

    // Get projects per user
    const { data: projects, error: projectsError } = await client
      .from("projects")
      .select('user_id');

    if (projectsError) {
      return { data: null, error: projectsError };
    }

    // Get link resources per user
    const { data: linkResources, error: linkResourcesError } = await client
      .from("link_resources")
      .select('user_id');

    if (linkResourcesError) {
      return { data: null, error: linkResourcesError };
    }

    // Get discovered links per user
    const { data: discoveredLinks, error: discoveredLinksError } = await client
      .from("discovered_links")
      .select('user_id');

    if (discoveredLinksError) {
      return { data: null, error: discoveredLinksError };
    }

    // Process and transform the data
    const userProjectCounts: Record<string, number> = {};
    const userLinkResourceCounts: Record<string, number> = {};
    const userDiscoveredLinkCounts: Record<string, number> = {};

    projects.forEach(project => {
      userProjectCounts[project.user_id] = (userProjectCounts[project.user_id] || 0) + 1;
    });

    linkResources.forEach(link => {
      userLinkResourceCounts[link.user_id] = (userLinkResourceCounts[link.user_id] || 0) + 1;
    });

    discoveredLinks.forEach(link => {
      userDiscoveredLinkCounts[link.user_id] = (userDiscoveredLinkCounts[link.user_id] || 0) + 1;
    });

    const transformedData = users.map(user => ({
      user_id: user.uuid,
      user_email: user.email,
      projects_count: userProjectCounts[user.uuid] || 0,
      link_resources_count: userLinkResourceCounts[user.uuid] || 0,
      discovered_links_count: userDiscoveredLinkCounts[user.uuid] || 0,
      last_active: user.updated_at
    }));

    return { data: transformedData, error: null };
  } catch (error) {
    console.error("Error in getAllUsersWithStats:", error);
    return { 
      data: null, 
      error: { message: error instanceof Error ? error.message : 'Unknown error' } as PostgrestError 
    };
  }
}

export async function deleteLinkResourcesInBatch(linkIds: string[]): Promise<{
  success: number;
  failed: number;
  errors: string[];
}> {
  const client = getSupabaseClient();
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (const linkId of linkIds) {
    try {
      const { error } = await client
        .from("link_resources")
        .delete()
        .eq('id', linkId);

      if (error) {
        failed++;
        errors.push(`Failed to delete link resource ${linkId}: ${error.message}`);
      } else {
        success++;
      }
    } catch (error) {
      failed++;
      errors.push(`Error deleting link resource ${linkId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { success, failed, errors };
}

export async function deleteProjectsInBatch(projectIds: string[]): Promise<{
  success: number;
  failed: number;
  errors: string[];
}> {
  const client = getSupabaseClient();
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (const projectId of projectIds) {
    try {
      // Discovered links will be deleted automatically due to CASCADE
      const { error } = await client
        .from("projects")
        .delete()
        .eq('id', projectId);

      if (error) {
        failed++;
        errors.push(`Failed to delete project ${projectId}: ${error.message}`);
      } else {
        success++;
      }
    } catch (error) {
      failed++;
      errors.push(`Error deleting project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { success, failed, errors };
}

// ========================================
// ADDITIONAL FUNCTIONS FOR COMPATIBILITY
// ========================================

export async function getProjectByIdForAnalytics(id: string): Promise<{ data: Project | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("projects")
    .select("*")
    .eq('id', id)
    .single();

  return { data, error };
}

export async function getLinksByProject(project_id: string, user_id: string): Promise<{ data: LinkResource[] | null, error: PostgrestError | null }> {
  // link_resources are not coupled to projects in the new architecture
  // Return user's link resources instead
  return await getLinkResourcesByUser(user_id);
}

export async function getProjectStats(project_id: string, limit = 30): Promise<{ data: any[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  try {
    // Get project to find its domain
    const { data: project, error: projectError } = await client
      .from("projects")
      .select("domain")
      .eq('id', project_id)
      .single();
      
    if (projectError || !project) {
      return { data: [], error: projectError };
    }
    
    // Get history data from all_links_history for this domain
    const { data: historyData, error } = await client
      .from("all_links_history")
      .select("*")
      .eq('domain', project.domain)
      .order('checked_at', { ascending: false })
      .limit(limit);
      
    if (error) {
      return { data: [], error };
    }
    
    // Transform data to match expected format
    const transformedData = historyData?.map(record => ({
      project_id: project_id,
      dr_score: record.dr_score || 0,
      traffic: record.traffic || 0,
      backlink_count: record.backlink_count || 0,
      total_links: record.backlink_count || 0, // Use backlink_count as total_links
      indexed_links: record.is_indexed ? record.backlink_count || 0 : 0,
      checked_at: record.checked_at,
      domain: record.domain
    })) || [];
    
    return { data: transformedData, error: null };
  } catch (error: any) {
    return { data: [], error: { message: error.message } as PostgrestError };
  }
}

export async function updateProjectSummaryStats(project_id: string): Promise<{ error: PostgrestError | null }> {
  // Use the existing updateProjectStats function
  return await updateProjectStats(project_id);
}

export async function createProjectStats(statsData: any): Promise<{ data: any | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  try {
    // Get project to find its domain
    const { data: project, error: projectError } = await client
      .from("projects")
      .select("domain")
      .eq('id', statsData.project_id)
      .single();
      
    if (projectError || !project) {
      return { data: null, error: projectError };
    }
    
    // Create a new record in all_links_history
    const { data, error } = await client
      .from("all_links_history")
      .insert({
        domain: project.domain,
        dr_score: statsData.dr_score || 0,
        traffic: statsData.traffic || 0,
        backlink_count: statsData.backlink_count || 0,
        is_indexed: statsData.is_indexed || false,
        checked_at: statsData.checked_at || new Date().toISOString()
      })
      .select()
      .single();
      
    return { data, error };
  } catch (error: any) {
    return { data: null, error: { message: error.message } as PostgrestError };
  }
}