import { getSupabaseClient } from "./db";
import { User } from "@/types/user";
import { retryDatabaseOperation, RetryableError } from "@/lib/utils/retry";

export type UserTier = 'free' | 'paid';

export interface TierLimits {
  projects: number;
  domains: number;
  link_resources: number;
  monthly_dr_queries: number;
  monthly_traffic_updates: number;
}

export interface UserUsageSummary {
  tier: UserTier;
  subscription_status: string;
  projects_count: number;
  projects_limit: number;
  domains_count: number;
  domains_limit: number;
  link_resources_count: number;
  link_resources_limit: number;
  monthly_dr_queries_used: number;
  monthly_dr_queries_limit: number;
  monthly_traffic_updates_used: number;
  monthly_traffic_updates_limit: number;
  usage_reset_date: string;
}

export interface TierValidationResult {
  allowed: boolean;
  message?: string;
  currentUsage?: number;
  limit?: number;
}

// Get user's current tier and usage summary
export async function getUserUsageSummary(userUuid: string): Promise<UserUsageSummary | null> {
  try {
    console.log('getUserUsageSummary - Starting for user:', userUuid);

    return await retryDatabaseOperation(async () => {
      console.log('getUserUsageSummary - Executing database operation');
      const supabase = getSupabaseClient();

      const { data, error } = await supabase.rpc('get_user_usage_summary', {
        p_user_id: userUuid
      });

      if (error) {
        console.error('getUserUsageSummary - Database error:', error);
        console.error('getUserUsageSummary - Error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        throw new RetryableError(`Database error: ${error.message}`);
      }

      console.log('getUserUsageSummary - Database query successful, data:', data);
      const result = data?.[0] || null;
      console.log('getUserUsageSummary - Returning result:', result ? 'found' : 'null');
      return result;
    });
  } catch (error) {
    console.error('getUserUsageSummary - Outer catch error:', error);
    console.error('getUserUsageSummary - Error stack:', error instanceof Error ? error.stack : 'No stack trace');

    // Return null instead of throwing to prevent cascading failures
    return null;
  }
}

// Check if user can create more projects
export async function canCreateProject(userUuid: string): Promise<TierValidationResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('can_create_project', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error checking project creation limit:', error);
      return { allowed: false, message: 'Error checking project limits' };
    }
    
    if (!data) {
      const summary = await getUserUsageSummary(userUuid);
      return {
        allowed: false,
        message: `Project limit reached. You have ${summary?.projects_count || 0} of ${summary?.projects_limit || 0} projects.`,
        currentUsage: summary?.projects_count || 0,
        limit: summary?.projects_limit || 0
      };
    }
    
    return { allowed: true };
  } catch (error) {
    console.error('Error checking project creation limit:', error);
    return { allowed: false, message: 'Error checking project limits' };
  }
}

// Check if user can add more domains
export async function canAddDomain(userUuid: string): Promise<TierValidationResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('can_add_domain', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error checking domain limit:', error);
      return { allowed: false, message: 'Error checking domain limits' };
    }
    
    if (!data) {
      const summary = await getUserUsageSummary(userUuid);
      return {
        allowed: false,
        message: `Domain limit reached. You have ${summary?.domains_count || 0} of ${summary?.domains_limit || 0} domains.`,
        currentUsage: summary?.domains_count || 0,
        limit: summary?.domains_limit || 0
      };
    }
    
    return { allowed: true };
  } catch (error) {
    console.error('Error checking domain limit:', error);
    return { allowed: false, message: 'Error checking domain limits' };
  }
}

// Check if user can add more link resources
export async function canAddLinkResource(userUuid: string): Promise<TierValidationResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('can_add_link_resource', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error checking link resource limit:', error);
      return { allowed: false, message: 'Error checking link resource limits' };
    }
    
    if (!data) {
      const summary = await getUserUsageSummary(userUuid);
      return {
        allowed: false,
        message: `Link resource limit reached. You have ${summary?.link_resources_count || 0} of ${summary?.link_resources_limit === -1 ? 'unlimited' : summary?.link_resources_limit || 0} link resources.`,
        currentUsage: summary?.link_resources_count || 0,
        limit: summary?.link_resources_limit || 0
      };
    }
    
    return { allowed: true };
  } catch (error) {
    console.error('Error checking link resource limit:', error);
    return { allowed: false, message: 'Error checking link resource limits' };
  }
}

// Check if user can make DR queries
export async function canMakeDrQuery(userUuid: string): Promise<TierValidationResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('can_make_dr_query', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error checking DR query limit:', error);
      return { allowed: false, message: 'Error checking DR query limits' };
    }
    
    if (!data) {
      const summary = await getUserUsageSummary(userUuid);
      if (summary?.tier === 'free') {
        return {
          allowed: false,
          message: 'DR queries are not available for free users. Please upgrade to paid plan.',
          currentUsage: summary?.monthly_dr_queries_used || 0,
          limit: summary?.monthly_dr_queries_limit || 0
        };
      }
      return {
        allowed: false,
        message: `Monthly DR query limit reached. You have used ${summary?.monthly_dr_queries_used || 0} of ${summary?.monthly_dr_queries_limit || 0} queries this month.`,
        currentUsage: summary?.monthly_dr_queries_used || 0,
        limit: summary?.monthly_dr_queries_limit || 0
      };
    }
    
    return { allowed: true };
  } catch (error) {
    console.error('Error checking DR query limit:', error);
    return { allowed: false, message: 'Error checking DR query limits' };
  }
}

// Check if user can make manual traffic updates
export async function canMakeTrafficUpdate(userUuid: string): Promise<TierValidationResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('can_make_traffic_update', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error checking traffic update limit:', error);
      return { allowed: false, message: 'Error checking traffic update limits' };
    }
    
    if (!data) {
      const summary = await getUserUsageSummary(userUuid);
      if (summary?.tier === 'free') {
        return {
          allowed: false,
          message: 'Manual traffic updates are not available for free users. Please upgrade to paid plan.',
          currentUsage: summary?.monthly_traffic_updates_used || 0,
          limit: summary?.monthly_traffic_updates_limit || 0
        };
      }
      return {
        allowed: false,
        message: `Monthly traffic update limit reached. You have used ${summary?.monthly_traffic_updates_used || 0} of ${summary?.monthly_traffic_updates_limit || 0} updates this month.`,
        currentUsage: summary?.monthly_traffic_updates_used || 0,
        limit: summary?.monthly_traffic_updates_limit || 0
      };
    }
    
    return { allowed: true };
  } catch (error) {
    console.error('Error checking traffic update limit:', error);
    return { allowed: false, message: 'Error checking traffic update limits' };
  }
}

// Record DR query usage
export async function recordDrQueryUsage(
  userUuid: string,
  projectId?: string,
  apiEndpoint?: string,
  metadata?: any
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase.rpc('record_dr_query_usage', {
      p_user_id: userUuid,
      p_project_id: projectId || null,
      p_api_endpoint: apiEndpoint || null,
      p_metadata: metadata || {}
    });
    
    if (error) {
      console.error('Error recording DR query usage:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error recording DR query usage:', error);
    return false;
  }
}

// Record traffic update usage
export async function recordTrafficUpdateUsage(
  userUuid: string,
  projectId?: string,
  apiEndpoint?: string,
  metadata?: any
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase.rpc('record_traffic_update_usage', {
      p_user_id: userUuid,
      p_project_id: projectId || null,
      p_api_endpoint: apiEndpoint || null,
      p_metadata: metadata || {}
    });
    
    if (error) {
      console.error('Error recording traffic update usage:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error recording traffic update usage:', error);
    return false;
  }
}

// Upgrade user to paid tier
export async function upgradeUserToPaid(
  userUuid: string,
  subscriptionPlan: string = 'monthly',
  subscriptionStartDate?: Date,
  subscriptionEndDate?: Date
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase.rpc('upgrade_user_to_paid', {
      p_user_id: userUuid,
      p_subscription_plan: subscriptionPlan,
      p_subscription_start_date: subscriptionStartDate?.toISOString() || new Date().toISOString(),
      p_subscription_end_date: subscriptionEndDate?.toISOString() || null
    });
    
    if (error) {
      console.error('Error upgrading user to paid:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error upgrading user to paid:', error);
    return false;
  }
}

// Downgrade user to free tier
export async function downgradeUserToFree(userUuid: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase.rpc('downgrade_user_to_free', {
      p_user_id: userUuid
    });
    
    if (error) {
      console.error('Error downgrading user to free:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error downgrading user to free:', error);
    return false;
  }
}

// Get tier limits for a specific tier
export async function getTierLimits(tier: UserTier): Promise<TierLimits | null> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('tier_limits')
      .select('limit_type, limit_value')
      .eq('tier', tier);
    
    if (error) {
      console.error('Error getting tier limits:', error);
      return null;
    }
    
    const limits: TierLimits = {
      projects: 0,
      domains: 0,
      link_resources: 0,
      monthly_dr_queries: 0,
      monthly_traffic_updates: 0
    };
    
    data?.forEach(limit => {
      if (limit.limit_type in limits) {
        limits[limit.limit_type as keyof TierLimits] = limit.limit_value;
      }
    });
    
    return limits;
  } catch (error) {
    console.error('Error getting tier limits:', error);
    return null;
  }
}

// Check if user is on paid tier
export async function isPaidUser(userUuid: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('users')
      .select('tier, subscription_status')
      .eq('uuid', userUuid)
      .single();
    
    if (error) {
      console.error('Error checking user tier:', error);
      return false;
    }
    
    return data?.tier === 'paid' && data?.subscription_status === 'active';
  } catch (error) {
    console.error('Error checking user tier:', error);
    return false;
  }
}

// Reset monthly usage counters (to be called by cron job)
export async function resetMonthlyUsageCounters(): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase.rpc('reset_monthly_usage_counters');
    
    if (error) {
      console.error('Error resetting monthly usage counters:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error resetting monthly usage counters:', error);
    return false;
  }
}