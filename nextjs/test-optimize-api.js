#!/usr/bin/env node

/**
 * Manual test script for the optimize API endpoint
 * This tests the actual issues we identified in the code
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing Content Optimization API');
console.log('=====================================\n');

// Test cases that should reveal the issues we found
const testCases = [
  {
    name: 'Valid Request - Basic',
    body: {
      text: 'This is a test text that needs optimization.',
      prompt: 'Make this more professional'
    },
    expectedStatus: 200
  },
  {
    name: 'Invalid Request - Missing Text',
    body: {
      prompt: 'Make this better'
      // Missing text field
    },
    expectedStatus: 400
  },
  {
    name: 'Invalid Request - Empty Text',
    body: {
      text: '',
      prompt: 'Make this better'
    },
    expectedStatus: 400
  },
  {
    name: 'Invalid Request - Malformed JSON',
    body: 'invalid json string',
    expectedStatus: 500,
    isRawBody: true
  },
  {
    name: 'Valid Request - With Authentication',
    body: {
      text: 'Test text for premium user',
      prompt: 'Optimize this professionally',
      tone: 'professional'
    },
    headers: {
      'Authorization': 'Bearer test-api-key'
    },
    expectedStatus: 200
  },
  {
    name: 'Valid Request - Long Text',
    body: {
      text: 'A'.repeat(1000) + ' This is a very long text that should test our optimization limits.',
      prompt: 'Optimize this long text',
      max_length: 100
    },
    expectedStatus: 200
  },
  {
    name: 'Valid Request - Special Characters',
    body: {
      text: 'Text with émojis 🚀 and spëcial chars: @#$%^&*()',
      prompt: 'Clean this up'
    },
    expectedStatus: 200
  }
];

async function runTest(testCase) {
  console.log(`\n📋 Testing: ${testCase.name}`);
  console.log('─'.repeat(50));
  
  try {
    const url = 'http://localhost:3001/api/extension/optimize';
    const headers = {
      'Content-Type': 'application/json',
      ...testCase.headers
    };
    
    const body = testCase.isRawBody ? testCase.body : JSON.stringify(testCase.body);
    
    console.log(`📤 Request: POST ${url}`);
    console.log(`📋 Headers:`, headers);
    console.log(`📄 Body:`, testCase.isRawBody ? 'Raw string' : JSON.stringify(testCase.body, null, 2));
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body
    });
    
    const responseData = await response.text();
    let parsedData;
    
    try {
      parsedData = JSON.parse(responseData);
    } catch (e) {
      parsedData = responseData;
    }
    
    console.log(`📥 Response Status: ${response.status}`);
    console.log(`📄 Response Data:`, typeof parsedData === 'object' ? JSON.stringify(parsedData, null, 2) : parsedData);
    
    // Check if the response matches expectations
    if (response.status === testCase.expectedStatus) {
      console.log('✅ Test PASSED');
      
      // Additional checks for successful responses
      if (response.status === 200 && parsedData.success) {
        console.log(`📊 Optimization Details:`);
        console.log(`   - Source: ${parsedData.source}`);
        console.log(`   - Tier: ${parsedData.tier}`);
        console.log(`   - Original Length: ${parsedData.original_length}`);
        console.log(`   - Optimized Length: ${parsedData.optimized_length}`);
        console.log(`   - Improvements: ${parsedData.improvements?.join(', ') || 'None'}`);
        console.log(`   - Confidence: ${parsedData.confidence}%`);
      }
    } else {
      console.log(`❌ Test FAILED - Expected status ${testCase.expectedStatus}, got ${response.status}`);
    }
    
  } catch (error) {
    console.log(`💥 Test ERROR: ${error.message}`);
    
    // Check if this is a connection error (server not running)
    if (error.code === 'ECONNREFUSED') {
      console.log('🚨 Server not running! Please start the Next.js development server:');
      console.log('   cd nextjs && pnpm dev');
      console.log('   Server should be running on http://localhost:3001');
      return false;
    }
  }
  
  return true;
}

async function runAllTests() {
  console.log('🚀 Starting API tests...\n');
  
  let serverRunning = true;
  
  for (const testCase of testCases) {
    if (!serverRunning) break;
    
    serverRunning = await runTest(testCase);
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  if (serverRunning) {
    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Summary:');
    console.log('   - Check for any failed tests above');
    console.log('   - Look for error patterns that match our identified issues');
    console.log('   - Verify that fallback optimization works when AI API fails');
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  console.log('   Or install node-fetch: npm install node-fetch');
  process.exit(1);
}

// Run the tests
runAllTests().catch(console.error);
