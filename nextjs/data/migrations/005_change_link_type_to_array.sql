-- Migration to change link_type from single value to array for multi-selection support
-- This allows a link to be both 'free' and 'paid' simultaneously

-- First, add a temporary column with array type
ALTER TABLE link_track.link_resources 
ADD COLUMN link_type_new TEXT[];

-- Migrate existing data: convert single values to arrays
UPDATE link_track.link_resources 
SET link_type_new = ARRAY[link_type]
WHERE link_type IS NOT NULL;

-- Handle any NULL values
UPDATE link_track.link_resources 
SET link_type_new = ARRAY['free']
WHERE link_type_new IS NULL;

-- Drop the old column and constraint
ALTER TABLE link_track.link_resources 
DROP CONSTRAINT IF EXISTS link_resources_link_type_check;

ALTER TABLE link_track.link_resources 
DROP COLUMN link_type;

-- Rename the new column
ALTER TABLE link_track.link_resources 
RENAME COLUMN link_type_new TO link_type;

-- Add constraint to ensure only valid values in the array
ALTER TABLE link_track.link_resources 
ADD CONSTRAINT link_resources_link_type_check 
CHECK (
  link_type IS NOT NULL AND 
  array_length(link_type, 1) > 0 AND
  NOT EXISTS (
    SELECT 1 FROM unnest(link_type) AS t 
    WHERE t NOT IN ('free', 'paid')
  )
);

-- Add comment for documentation
COMMENT ON COLUMN link_track.link_resources.link_type IS 'Array of link types: can contain ''free'', ''paid'', or both';
