#!/usr/bin/env node

/**
 * Unit test for the basic optimization functionality
 * This demonstrates that our fixes work correctly
 */

console.log('🧪 Testing Basic Optimization Function');
console.log('======================================\n');

// Test cases for the basic optimization function
const testCases = [
  {
    name: 'Whitespace Normalization',
    input: {
      text: '  this   is  a   test  ',
      prompt: 'Clean this up'
    },
    expectedImprovements: ['Normalized whitespace', 'Capitalized first letter', 'Added proper punctuation']
  },
  {
    name: 'Professional Tone Enhancement',
    input: {
      text: 'This is awesome stuff guys',
      prompt: 'Make professional',
      tone: 'professional'
    },
    expectedImprovements: ['Enhanced professional tone']
  },
  {
    name: 'Length Constraint',
    input: {
      text: 'This is a very long text that should be truncated',
      prompt: 'Shorten',
      max_length: 20
    },
    expectedImprovements: ['Shortened to 20 characters']
  },
  {
    name: 'SEO Optimization',
    input: {
      text: 'Basic content here',
      prompt: 'Optimize for SEO'
    },
    expectedImprovements: ['Added SEO-friendly keywords']
  },
  {
    name: 'Input Validation - Empty Text',
    input: {
      text: '',
      prompt: 'Optimize'
    },
    shouldThrow: true
  },
  {
    name: 'Input Validation - Invalid Text Type',
    input: {
      text: null,
      prompt: 'Optimize'
    },
    shouldThrow: true
  }
];

async function testBasicOptimization() {
  console.log('🚀 Testing basic optimization function...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`📋 Testing: ${testCase.name}`);
    console.log('─'.repeat(50));
    
    try {
      // Test via API call to ensure the function works in context
      const response = await fetch('http://localhost:3001/api/extension/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCase.input)
      });
      
      const data = await response.json();
      
      if (testCase.shouldThrow) {
        if (response.status >= 400) {
          console.log('✅ Correctly rejected invalid input');
          passedTests++;
        } else {
          console.log('❌ Should have rejected invalid input');
        }
      } else {
        if (response.status === 200 && data.success) {
          console.log('✅ Optimization successful');
          console.log(`📤 Input: "${testCase.input.text}"`);
          console.log(`📥 Output: "${data.optimized_text}"`);
          console.log(`📊 Improvements: ${data.improvements.join(', ')}`);
          
          // Check if expected improvements are present
          let hasExpectedImprovements = true;
          if (testCase.expectedImprovements) {
            for (const expectedImprovement of testCase.expectedImprovements) {
              if (!data.improvements.some(imp => imp.includes(expectedImprovement.split(' ')[0]))) {
                console.log(`⚠️  Missing expected improvement: ${expectedImprovement}`);
                hasExpectedImprovements = false;
              }
            }
          }
          
          if (hasExpectedImprovements) {
            passedTests++;
          }
        } else {
          console.log(`❌ Optimization failed: ${data.message || 'Unknown error'}`);
        }
      }
      
    } catch (error) {
      console.log(`💥 Test error: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`🎉 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('✅ All tests passed! Basic optimization function is working correctly.');
  } else {
    console.log('❌ Some tests failed. Check the output above for details.');
  }
  
  return passedTests === totalTests;
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  process.exit(1);
}

testBasicOptimization()
  .then(success => {
    if (success) {
      console.log('\n🎯 Summary: All critical fixes are working correctly!');
      console.log('   - Input validation is working');
      console.log('   - Text processing is functioning properly');
      console.log('   - Error handling is robust');
      console.log('   - Security measures are in place');
    } else {
      console.log('\n⚠️  Some issues may still exist. Review the test output.');
    }
  })
  .catch(console.error);
