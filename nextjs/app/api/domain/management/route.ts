import { NextRequest, NextResponse } from 'next/server';
import { DomainModel } from '@/models/domain';
import { DomainFilters } from '@/types/domain';
import { getAuthenticatedUser } from '@/lib/auth';
import { validateDomainFormat } from '@/utils/url-normalization';

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        const stats = await DomainModel.getUserDomainStats(user.uuid);
        return NextResponse.json(stats);

      case 'registrars':
        const registrars = await DomainModel.getUserRegistrars(user.uuid);
        return NextResponse.json(registrars);

      case 'dns-providers':
        const dnsProviders = await DomainModel.getUserDnsProviders(user.uuid);
        return NextResponse.json(dnsProviders);

      case 'expiring':
        const daysAhead = parseInt(searchParams.get('days') || '30');
        const expiringDomains = await DomainModel.getExpiringDomains(user.uuid, daysAhead);
        return NextResponse.json(expiringDomains);

      default:
        // Get domains with filters
        const filters: DomainFilters = {
          status: searchParams.get('status') as any || 'all',
          registrar: searchParams.get('registrar') || undefined,
          dnsProvider: searchParams.get('dnsProvider') || undefined,
          sortBy: searchParams.get('sortBy') as any || 'domain',
          sortOrder: searchParams.get('sortOrder') as any || 'asc',
          isFavorite: searchParams.get('favorites') === 'true' ? true : undefined,
          tags: searchParams.get('tags')?.split(',').filter(Boolean) || undefined
        };

        const domains = await DomainModel.getUserDomains(user.uuid, filters);
        return NextResponse.json(domains);
    }
  } catch (error) {
    console.error('Domain management API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, domain, domainData, whoisData, domainId, projectId } = body;

    switch (action) {
      case 'add-domain':
        if (!domain) {
          return NextResponse.json({ error: 'Domain is required' }, { status: 400 });
        }

        // Normalize and validate domain
        let normalizedDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
        
        // Validate domain format - supports both top-level domains and subdomains
        if (!validateDomainFormat(normalizedDomain)) {
          return NextResponse.json({
            error: 'Invalid domain format. Please enter a valid domain name (e.g., example.com, sub.example.com).'
          }, { status: 400 });
        }
        
        // Create domain record immediately without waiting for WHOIS
        const newDomain = await DomainModel.createOrUpdateDomain(user.uuid, {
          domain: normalizedDomain,
          ...domainData
        });

        // Auto-discover and associate related projects
        const relatedProjectsResult = await DomainModel.discoverAndAssociateRelatedProjects(user.uuid, normalizedDomain, newDomain.id);

        // Associate with specific project if provided
        if (projectId) {
          await DomainModel.associateDomainWithProject(user.uuid, newDomain.id, projectId, domainData?.isPrimary);
        }

        // Fetch WHOIS data in background (don't await)
        if (process.env.BACKEND_WORKER_URL && process.env.BACKEND_WORKER_API_KEY) {
          fetch(`${process.env.BACKEND_WORKER_URL}/api/whois?domain=${encodeURIComponent(normalizedDomain)}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY}`,
              'Content-Type': 'application/json',
            },
          })
            .then(async (whoisResponse) => {
              if (whoisResponse.ok) {
                const fetchedWhoisData = await whoisResponse.json();
                // Update domain with WHOIS data asynchronously
                if (fetchedWhoisData.success && fetchedWhoisData.data) {
                  await DomainModel.updateDomainWhois(user.uuid, normalizedDomain, fetchedWhoisData.data);
                }
              }
            })
            .catch(error => {
              console.warn('Background WHOIS fetch failed:', error);
            });
        }

        return NextResponse.json({ 
          message: 'Domain added successfully',
          domain: newDomain,
          relatedProjects: relatedProjectsResult,
          whoisStatus: 'fetching' // Indicates WHOIS is being fetched in background
        });

      case 'update-domain':
        if (!domainId) {
          return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
        }

        const updatedDomain = await DomainModel.createOrUpdateDomain(user.uuid, {
          ...domainData,
          domain: domainData.domain
        });

        return NextResponse.json({ 
          message: 'Domain updated successfully',
          domain: updatedDomain
        });

      case 'delete-domain':
        if (!domainId) {
          return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
        }

        await DomainModel.deleteDomain(user.uuid, domainId);
        return NextResponse.json({ message: 'Domain deleted successfully' });

      case 'associate-project':
        if (!domainId || !projectId) {
          return NextResponse.json({ error: 'Domain ID and Project ID are required' }, { status: 400 });
        }

        await DomainModel.associateDomainWithProject(user.uuid, domainId, projectId, body.isPrimary);
        return NextResponse.json({ message: 'Domain associated with project successfully' });

      case 'remove-project-association':
        if (!domainId || !projectId) {
          return NextResponse.json({ error: 'Domain ID and Project ID are required' }, { status: 400 });
        }

        await DomainModel.removeDomainProjectAssociation(domainId, projectId);
        return NextResponse.json({ message: 'Project association removed successfully' });

      case 'refresh-whois':
        if (!domain) {
          return NextResponse.json({ error: 'Domain is required' }, { status: 400 });
        }

        // Check if refresh is needed
        console.log('Checking if refresh is needed for domain:', domain);
        const needsRefresh = await DomainModel.needsWhoisRefresh(user.uuid, domain);
        if (!needsRefresh) {
          return NextResponse.json({ 
            message: 'WHOIS data is still fresh (less than 30 days old)',
            refreshed: false 
          });
        }

        // Validate backend worker configuration
        if (!process.env.BACKEND_WORKER_URL || !process.env.BACKEND_WORKER_API_KEY) {
          console.error('BACKEND_WORKER_URL or BACKEND_WORKER_API_KEY not configured');
          return NextResponse.json({ error: 'Backend worker not configured' }, { status: 500 });
        }

        // Fetch fresh WHOIS data using existing endpoint with proper authentication
        const refreshWhoisResponse = await fetch(`${process.env.BACKEND_WORKER_URL}/api/whois?domain=${encodeURIComponent(domain)}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY}`,
            'Content-Type': 'application/json',
          },
        });
        
        if (!refreshWhoisResponse.ok) {
          console.error('Failed to fetch WHOIS data:', refreshWhoisResponse.status, refreshWhoisResponse.statusText);
          return NextResponse.json({ error: 'Failed to fetch WHOIS data from backend' }, { status: 500 });
        }

        const freshWhoisData = await refreshWhoisResponse.json();
        
        // Check if the backend returned success: false
        if (!freshWhoisData.success) {
          console.warn('Backend WHOIS API returned error:', freshWhoisData.error);
          return NextResponse.json({ 
            message: 'WHOIS service temporarily unavailable',
            refreshed: false,
            error: freshWhoisData.error || 'WHOIS service unavailable'
          });
        }

        // Update the domain with fresh data
        await DomainModel.updateDomainWhois(user.uuid, domain, freshWhoisData.data);

        return NextResponse.json({ 
          message: 'WHOIS data refreshed successfully',
          refreshed: true,
          data: freshWhoisData.data 
        });

      case 'bulk-import':
        if (!body.domains || !Array.isArray(body.domains)) {
          return NextResponse.json({ error: 'Domains array is required' }, { status: 400 });
        }

        // Validate backend worker configuration for bulk import
        if (!process.env.BACKEND_WORKER_URL || !process.env.BACKEND_WORKER_API_KEY) {
          console.warn('BACKEND_WORKER_URL or BACKEND_WORKER_API_KEY not configured, skipping WHOIS data fetch');
        }

        const importResults = [];
        for (const domainName of body.domains) {
          try {
            const normalizedName = domainName.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
            
            // Validate domain format
            if (!validateDomainFormat(normalizedName)) {
              importResults.push({
                domain: domainName,
                success: false,
                error: 'Invalid domain format. Only top-level domains (xxx.xxx) are allowed.'
              });
              continue;
            }
            
            // Fetch WHOIS for each domain if backend is configured
            let whoisData = null;
            if (process.env.BACKEND_WORKER_URL && process.env.BACKEND_WORKER_API_KEY) {
              try {
                const whoisResp = await fetch(`${process.env.BACKEND_WORKER_URL}/api/whois?domain=${encodeURIComponent(normalizedName)}`, {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY}`,
                    'Content-Type': 'application/json',
                  },
                });
                
                if (whoisResp.ok) {
                  const whoisResult = await whoisResp.json();
                  if (whoisResult.success && whoisResult.data) {
                    whoisData = whoisResult.data;
                  }
                }
              } catch (whoisError) {
                console.warn(`WHOIS fetch failed for ${normalizedName}:`, whoisError);
                // Continue without WHOIS data
              }
            }

            const domain = await DomainModel.createOrUpdateDomain(user.uuid, {
              domain: normalizedName,
              whoisData
            });

            importResults.push({
              domain: normalizedName,
              success: true,
              domainId: domain.id,
              whoisFetched: !!whoisData
            });
          } catch (error) {
            importResults.push({
              domain: domainName,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        return NextResponse.json({ 
          message: 'Bulk import completed',
          results: importResults
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Domain management POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 