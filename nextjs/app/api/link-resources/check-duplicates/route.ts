import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getLinkResourcesByUser } from "@/models/links";
import { normalizeUrl } from "@/utils/url-normalization";

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { urls } = body;

    if (!Array.isArray(urls)) {
      return NextResponse.json({ error: "URLs must be an array" }, { status: 400 });
    }

    // 获取用户现有的链接资源
    const { data: existingLinks, error } = await getLinkResourcesByUser(user.id);
    
    if (error) {
      console.error("Error fetching existing links:", error);
      return NextResponse.json({ error: "Failed to check duplicates" }, { status: 500 });
    }

    // 规范化现有链接的URL
    const existingNormalizedUrls = new Set(
      (existingLinks || []).map(link => normalizeUrl(link.url).normalized)
    );

    // 检查哪些URL是重复的
    const duplicates = urls.filter(url => {
      const normalized = normalizeUrl(url).normalized;
      return existingNormalizedUrls.has(normalized);
    });

    return NextResponse.json({ duplicates });
  } catch (error) {
    console.error("Error in check-duplicates API:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
