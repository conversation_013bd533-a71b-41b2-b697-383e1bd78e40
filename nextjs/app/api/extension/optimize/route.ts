import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON>ey } from "@/models/user";
import { z } from "zod";

const optimizeContentSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  content: z.string().min(1, "Content is required"),
  max_tokens: z.number().optional(),
  temperature: z.number().min(0).max(2).optional()
});

// 导出一个异步函数，用于处理POST请求
export async function POST(request: NextRequest) {
  let body: any;

  try {
    // 获取请求头中的authorization字段
    const authHeader = request.headers.get("authorization");
    // 获取请求的body
    body = await request.json();

    // Validate request body first - don't log the entire body for security
    console.log("Validating request body for content optimization");
    const validation = optimizeContentSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { prompt, content, max_tokens, temperature } = validation.data;

    // Determine AI credentials to use
    let aiApiKey: string | undefined;
    let aiModelName: string;
    let aiApiBaseUrl: string;
    let userTier = "free";

    if (authHeader && authHeader.startsWith("Bearer ")) {
      const apiKey = authHeader.replace("Bearer ", "").trim();

      if (apiKey) {
        try {
          // Try to validate API key and get user
          const user = await getUserByApiKey(apiKey);

          if (user && user.uuid) {
            // Authenticated user - use premium AI credentials
            aiApiKey = process.env.AI_API_KEY;
            aiModelName = process.env.AI_MODEL_NAME || "gpt-3.5-turbo";
            aiApiBaseUrl = process.env.AI_API_BASE_URL || "https://api.openai.com/v1";
            userTier = "premium";
          }
        } catch (authError) {
          console.error("API key validation error:", authError);
          // Continue with free tier on auth error
        }
      }
    }

    // If no authenticated user, use free AI credentials
    if (userTier === "free") {
      aiApiKey = process.env.FREE_AI_API_KEY;
      aiModelName = process.env.FREE_AI_MODEL_NAME || "gpt-3.5-turbo";
      aiApiBaseUrl = process.env.FREE_AI_API_BASE_URL || "https://api.openai.com/v1";
    }
    
    if (!aiApiKey) {
      console.error(`${userTier.toUpperCase()}_AI_API_KEY not configured`);
      return NextResponse.json(
        {
          success: false,
          message: "AI service is not available. Please try again later.",
          error: "AI_API_KEY_NOT_CONFIGURED"
        },
        { status: 503 }
      );
    }

    // Validate AI API configuration
    if (!aiModelName || !aiApiBaseUrl) {
      console.error(`AI API configuration incomplete for ${userTier} tier`);
      return NextResponse.json(
        {
          success: false,
          message: "AI service configuration is incomplete. Please try again later.",
          error: "AI_CONFIG_INCOMPLETE"
        },
        { status: 503 }
      );
    }
    
    // Apply tier-specific limitations for free users
    let maxTokens = max_tokens || (userTier === "free" ? 500 : 1000);
    if (userTier === "free") {
      maxTokens = Math.min(maxTokens, 500); // Limit free users to 500 tokens
    }

    // Use provided temperature or default based on tier
    const requestTemperature = temperature !== undefined ? temperature : (userTier === "free" ? 0.5 : 0.7);
    
    // Call AI API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const aiResponse = await fetch(`${aiApiBaseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${aiApiKey}`,
        },
        body: JSON.stringify({
          model: aiModelName,
          messages: [
            {
              role: "system",
              content: prompt
            },
            {
              role: "user",
              content: content
            }
          ],
          max_tokens: maxTokens,
          temperature: requestTemperature
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!aiResponse.ok) {
        console.error("AI API error:", aiResponse.status, aiResponse.statusText);
        return NextResponse.json(
          {
            success: false,
            message: "AI service returned an error. Please try again later.",
            error: "AI_API_ERROR",
            details: `${aiResponse.status}: ${aiResponse.statusText}`
          },
          { status: 502 }
        );
      }
    
    const aiResult = await aiResponse.json();
    console.log("AI API response:", aiResult);
    
    if (aiResult.choices && aiResult.choices[0]?.message?.content) {
      const result = aiResult.choices[0].message.content.trim();

      return NextResponse.json({
        success: true,
        result: result,
        tier: userTier
      });
    } else {
      console.error("AI API returned invalid response format");
      return NextResponse.json(
        {
          success: false,
          message: "AI service returned an invalid response. Please try again later.",
          error: "AI_INVALID_RESPONSE"
        },
        { status: 502 }
      );
    }
    } catch (aiError) {
      clearTimeout(timeoutId);
      console.error("AI API request failed:", aiError);

      return NextResponse.json(
        {
          success: false,
          message: "AI service is currently unavailable. Please try again later.",
          error: "AI_REQUEST_FAILED"
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error("Extension optimize content error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error. Please try again later.",
        error: "INTERNAL_ERROR"
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": process.env.NODE_ENV === 'production'
        ? (process.env.ALLOWED_ORIGINS || "https://linktrackpro.com")
        : "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400", // 24 hours
    },
  });
}