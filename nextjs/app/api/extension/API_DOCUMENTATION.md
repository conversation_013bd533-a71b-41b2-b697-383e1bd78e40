# LinkTrackPro Extension API Documentation

This document provides comprehensive documentation for all API endpoints in the `/api/extension/` directory. These APIs are designed for browser extension integration with the LinkTrackPro platform.

## Base URL

```
http://localhost:3000/api/extension
```

## Authentication

The Extension API supports both authenticated and unauthenticated access with different service tiers:

### Authenticated Access (Premium Tier)
- Requires Bearer token authentication using the user's API key
- Access to full AI optimization features
- Higher token limits and better model quality
- Full access to all user data and features

### Unauthenticated Access (Free Tier)  
- No authentication required for content optimization
- Limited AI optimization capabilities
- Lower token limits (500 tokens max)
- Basic content optimization features

### Headers (Premium Tier)
```
Authorization: Bearer <api_key>
Content-Type: application/json
```

### Headers (Free Tier)
```
Content-Type: application/json
```

### Authentication Flow
1. **Premium**: Obtain API key from LinkTrackPro platform and include in Authorization header
2. **Free**: Make requests without authentication header for basic optimization
3. All requests validate the API key if provided and return appropriate service tier

---

## API Endpoints

### 1. Authentication Validation

#### `POST /auth`

Validates API key and returns user information.

**Request Body:**
```json
{
  "action": "validate"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "user": {
    "uuid": "string",
    "email": "string",
    "nickname": "string",
    "avatar_url": "string|null"
  }
}
```

**Response (Error - 401):**
```json
{
  "success": false,
  "message": "Invalid API key"
}
```

**Use Cases:**
- Extension initialization
- API key validation
- User profile display

---

### 2. Projects Management

#### `GET /projects`

Retrieves all projects belonging to the authenticated user.

**Query Parameters:** None

**Response (Success - 200):**
```json
{
  "success": true,
  "projects": [
    {
      "id": "uuid",
      "name": "string",
      "domain": "string",
      "description": "string",
      "user_id": "uuid",
      "created_at": "datetime"
    }
  ]
}
```

**Response (Error - 500):**
```json
{
  "success": false,
  "message": "Failed to fetch projects"
}
```

**Use Cases:**
- Project selection in extension
- Dropdown population
- Project-based link organization

---

### 3. Links Management

#### `GET /links`

Retrieves link resources for the authenticated user.

**Query Parameters:**
- `project_id` (optional): Filter by project (Note: LinkResources are project-decoupled)

**Response (Success - 200):**
```json
{
  "success": true,
  "links": [
    {
      "id": "uuid",
      "url": "string",
      "title": "string",
      "link_type": ["free", "paid"],
      "price": "number|null",
      "source": "string",
      "user_id": "uuid",
      "created_at": "datetime"
    }
  ]
}
```

#### `POST /links`

Creates a new link resource.

**Request Body:**
```json
{
  "url": "string (required, valid URL)",
  "title": "string (required, min 1 char)",
  "link_type": ["free", "paid"] (default: ["free"]),
  "price": "number (optional)",
  "source": "string (optional)",
  "project_id": "string (optional, not used due to decoupling)"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "link": {
    "id": "uuid",
    "url": "string",
    "title": "string",
    "link_type": ["free", "paid"],
    "price": "number|null",
    "source": "string",
    "user_id": "uuid",
    "created_at": "datetime"
  }
}
```

**Validation Errors (400):**
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    {
      "path": ["field"],
      "message": "Error message"
    }
  ]
}
```

**Use Cases:**
- Link discovery and saving
- Extension link management
- Resource tracking
- Mixed monetization model support (free + paid)

**Link Type Examples:**
- `["free"]` - Free resource only
- `["paid"]` - Paid resource only  
- `["free", "paid"]` - Both free and paid options available (e.g., freemium model)

---

### 4. AI Content Optimization

#### `POST /optimize`

A generic AI optimization endpoint that allows consumers to provide custom prompts and content. Supports both authenticated (premium) and unauthenticated (free) access.

**Request Body:**
```json
{
  "prompt": "string (required, custom AI instruction)",
  "content": "string (required, content to process)",
  "max_tokens": "number (optional, token limit)",
  "temperature": "number (optional, 0-2, creativity level)"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "result": "string (AI-generated result)",
  "tier": "premium|free"
}
```

**Service Tier Differences:**

**Premium Tier (Authenticated):**
- Full AI model access with higher quality
- Max tokens: up to 1000 tokens (or custom limit)
- Custom temperature settings supported
- Uses `AI_MODEL_NAME`, `AI_API_KEY`, `AI_API_BASE_URL`

**Free Tier (Unauthenticated):**
- Basic AI model access
- Max tokens: limited to 500 tokens
- Temperature limited to prevent abuse
- Uses `FREE_AI_MODEL_NAME`, `FREE_AI_API_KEY`, `FREE_AI_API_BASE_URL`

**Key Features:**
- **Generic Prompting**: Consumers provide their own custom prompts
- **Flexible Content**: Any type of content can be processed
- **Parameter Control**: Optional temperature and token limits
- **Simple Response**: Clean result without metadata overhead

**Response (Error - 400):**
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    {
      "path": ["field"],
      "message": "Error message"
    }
  ]
}
```

**Example Prompts:**
- "Make this text more professional and engaging"
- "Generate JSON values for form fields: {field1: 'name', field2: 'description'} based on project: {name: 'MyApp', domain: 'myapp.com'}"
- "Optimize for SEO while maintaining readability"
- "Translate the following content to Spanish"
- "Summarize this content in 100 words or less"
- "Convert this data to a structured format"

**Use Cases:**
- **Form Auto-filling**: Generate appropriate values for web forms
- **Content Optimization**: Improve text quality and readability
- **Data Processing**: Transform or restructure content
- **Translation**: Convert content between languages
- **Summarization**: Create concise versions of longer content
- **Custom AI Tasks**: Any task that can be described in a prompt

---

### 5. Submission Management

#### `POST /submit`

Creates a new submission record for link submission tracking.

**Request Body:**
```json
{
  "project_id": "uuid (required)",
  "link_id": "uuid (required)",
  "target_url": "string (required, valid URL)",
  "form_data": "object (optional)"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "submission": {
    "id": "uuid",
    "project_id": "uuid",
    "link_id": "uuid",
    "target_url": "string",
    "status": "pending",
    "created_at": "datetime"
  }
}
```

**Validation Requirements:**
- User must own the specified project
- User must own the specified link resource
- All UUID fields must be valid UUIDs
- Target URL must be a valid URL

#### `GET /submit/[id]`

Retrieves submission details by ID.

**Path Parameters:**
- `id` - Submission UUID

**Response (Success - 200):**
```json
{
  "success": true,
  "submission": {
    "id": "uuid",
    "project_id": "uuid",
    "link_id": "uuid",
    "target_url": "string",
    "submission_data": "object|null",
    "status": "pending|submitted|failed|completed",
    "created_at": "datetime",
    "updated_at": "datetime",
    "project": "object",
    "link": "object"
  }
}
```

#### `PATCH /submit/[id]`

Updates submission status.

**Path Parameters:**
- `id` - Submission UUID

**Request Body:**
```json
{
  "status": "pending|submitted|failed|completed"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "submission": {
    "id": "uuid",
    "status": "string",
    "updated_at": "datetime"
  }
}
```

**Status Flow:**
- `pending` - Initial state, submission prepared
- `submitted` - Successfully submitted to target
- `failed` - Submission failed
- `completed` - Submission process completed

**Use Cases:**
- Submission tracking
- Status updates during form submission
- Audit trail for extension activities

---

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "message": "Error description"
}
```

### Common HTTP Status Codes

- **200 OK** - Successful request
- **400 Bad Request** - Validation errors, malformed request
- **401 Unauthorized** - Missing, invalid, or expired API key
- **404 Not Found** - Resource not found or access denied
- **500 Internal Server Error** - Server-side errors
- **503 Service Unavailable** - External service dependencies unavailable

### CORS Support

All endpoints include CORS headers for cross-origin requests:

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PATCH, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

---

## Rate Limiting

- No explicit rate limiting is currently implemented
- Requests are limited by database connection pooling
- Consider implementing rate limiting for production use

## Security Considerations

### API Key Security
- API keys should be stored securely in extension storage
- Keys are validated against the database on each request
- Invalid keys immediately return 401 Unauthorized

### Data Validation
- All inputs are validated using Zod schemas
- SQL injection protection through parameterized queries
- XSS protection through proper data sanitization

### Authorization
- All data access is scoped to the authenticated user
- Project and link ownership is validated before operations
- No cross-user data access is possible

---

## Integration Examples

### Extension Initialization
```javascript
// Validate API key and get user info
const response = await fetch('/api/extension/auth', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ action: 'validate' })
});

const { success, user } = await response.json();
```

### Link Resources Workflow
```javascript
// Get all link resources with pagination
const response = await fetch('/api/extension/link-resources?limit=20&offset=0&withStats=true', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }
});

const { linkResources, pagination } = await response.json();
```

### AI Content Optimization (Free Tier)
```javascript
// Basic content optimization without authentication
const optimization = await fetch('/api/extension/optimize', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'Make this more professional and SEO-friendly',
    content: originalContent,
    max_tokens: 300
  })
});

const { result, tier } = await optimization.json();
// tier will be "free"
```

### AI Content Optimization (Premium Tier)
```javascript
// Advanced optimization with authentication for premium features
const optimization = await fetch('/api/extension/optimize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'Make this more professional and SEO-friendly',
    content: originalContent,
    max_tokens: 500,
    temperature: 0.7
  })
});

const { result, tier } = await optimization.json();
// tier will be "premium"
```

### Form Auto-filling Example
```javascript
// Generate form field values using AI
const projectInfo = {
  name: "MyAwesomeApp",
  domain: "myawesomeapp.com",
  description: "A productivity app for teams"
};

const formFields = [
  { name: "title", type: "text", label: "Project Title" },
  { name: "description", type: "textarea", label: "Description" },
  { name: "category", type: "select", label: "Category" }
];

const optimization = await fetch('/api/extension/optimize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'Generate JSON values for form fields based on project information. Return only valid JSON with field names as keys and appropriate values.',
    content: `Project: ${JSON.stringify(projectInfo)}\nForm fields: ${JSON.stringify(formFields)}`
  })
});

const { result } = await optimization.json();
const fieldValues = JSON.parse(result);
// fieldValues = { "title": "MyAwesomeApp - Team Productivity Tool", "description": "...", "category": "Productivity" }

// Create submission record
const submission = await fetch('/api/extension/submit', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    project_id: selectedProject,
    link_id: selectedLink,
    target_url: window.location.href,
    form_data: { description: result }
  })
});
```

---

## Data Models

### User
```typescript
interface User {
  uuid: string;
  email: string;
  nickname: string | null;
  avatar_url: string | null;
}
```

### Project
```typescript
interface Project {
  id: string;
  name: string;
  domain: string;
  description: string | null;
  user_id: string;
  created_at: string;
}
```

### LinkResource
```typescript
interface LinkResource {
  id: string;
  url: string;
  title: string;
  link_type: ("free" | "paid")[];
  price: number | null;
  currency: string;
  source: string;
  acquisition_method: string | null;
  notes: string | null;
  submit_url: string | null;
  user_id: string;
  created_at: string;
  updated_at: string;
}
```

### ContentOptimization
```typescript
interface ContentOptimizationRequest {
  prompt: string;
  content: string;
  max_tokens?: number;
  temperature?: number;
}

interface ContentOptimizationResponse {
  success: boolean;
  result: string;
  tier: "premium" | "free";
}
```

### Submission
```typescript
interface Submission {
  id: string;
  project_id: string;
  link_id: string;
  target_url: string;
  submission_data: object | null;
  status: "pending" | "submitted" | "failed" | "completed";
  created_at: string;
  updated_at: string;
}
```

---

## Migration Guide

### Changes from Previous Version

The `/optimize` endpoint has been simplified to provide more flexibility and remove built-in assumptions:

**What Changed:**
- Request format: `text` → `content`, removed `context`, `max_length`, `tone`
- Response format: `optimized_text` → `result`, removed `improvements`, `confidence`, `original_length`, etc.
- Added optional `max_tokens` and `temperature` parameters for fine control
- Consumers now provide complete custom prompts instead of relying on built-in prompt construction

**Migration Steps:**
1. Update request body structure:
   ```javascript
   // Old format
   {
     text: "content to optimize",
     prompt: "optimization instruction",
     tone: "professional",
     max_length: 300
   }

   // New format
   {
     content: "content to optimize",
     prompt: "Complete custom prompt with all instructions",
     max_tokens: 300,
     temperature: 0.7
   }
   ```

2. Update response handling:
   ```javascript
   // Old format
   const { optimized_text, confidence, improvements } = await response.json();

   // New format
   const { result } = await response.json();
   ```

3. Construct complete prompts:
   ```javascript
   // Old: API constructed prompts internally
   prompt: "Make this professional"

   // New: Provide complete instructions
   prompt: "Make this text more professional and engaging. Use a formal tone and improve clarity."
   ```

**Benefits:**
- More flexible and generic API
- Consumers have full control over AI instructions
- Simpler response format reduces complexity
- Better suited for diverse use cases beyond content optimization

---

## Dependencies

### External Services
- **AI Service**: Content optimization and analysis
- **Database**: PostgreSQL with user/project/link models
- **Authentication**: NextAuth.js session management

### Required Models
- `@/models/user` - User authentication and lookup
- `@/models/links` - Project and link resource management  
- `@/models/extension-submission` - Submission tracking

### Validation
- **Zod**: Request/response schema validation
- **UUID validation**: All ID fields require valid UUIDs
- **URL validation**: All URL fields require valid URLs

---

This API is designed to support comprehensive browser extension functionality for link resource management, AI-powered content optimization, and submission tracking within the LinkTrackPro ecosystem.