import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { importLinkResources } from "@/models/links";
import { LinkImportDataNew } from "@/types/links";


export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { project_id, links } = body;

    if (!project_id || !links || !Array.isArray(links)) {
      return NextResponse.json({ 
        error: "Invalid request. ProjectId and links array are required." 
      }, { status: 400 });
    }

    // Validate each link has required fields
    const validatedLinks: LinkImportDataNew[] = [];
    const validationErrors: string[] = [];

    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      
      if (!link.url || !link.title) {
        validationErrors.push(`Row ${i + 1}: URL and title are required`);
        continue;
      }

      // Validate URL format
      try {
        new URL(link.url);
      } catch {
        validationErrors.push(`Row ${i + 1}: Invalid URL format`);
        continue;
      }

      validatedLinks.push({
        url: link.url,
        title: link.title,
        link_type: Array.isArray(link.link_type) ? link.link_type :
                   typeof link.link_type === 'string' ?
                     link.link_type.split(',').map(t => t.trim()).filter(t => t === 'free' || t === 'paid') as ('free' | 'paid')[] :
                     ['free'],
        price: link.price ? parseFloat(link.price) : undefined,
        source: link.source,
        acquisition_method: link.acquisition_method,
        notes: link.notes,
        submit_url: link.submit_url
      });
    }

    if (validationErrors.length > 0) {
      return NextResponse.json({ 
        error: "Validation errors",
        details: validationErrors
      }, { status: 400 });
    }

    const result = await importLinkResources(user.uuid, validatedLinks);

    return NextResponse.json({
      success: result.success,
      failed: result.failed,
      errors: result.errors,
      message: `Successfully imported ${result.success} links. ${result.failed} failed.`
    });

  } catch (error) {
    console.error("Error importing links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 