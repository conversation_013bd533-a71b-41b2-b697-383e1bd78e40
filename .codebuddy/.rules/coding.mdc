## 需求分析与拆分

1. **优先理解需求**：在开始编码前，深入分析并全理解业务需求和用户期望。
2. **拆分子需求**：将复杂需求拆分为可管理的子需求，使得开发过程更加高效，减少返工。
3. **拆解出项目结构**：根据理解的需求拆解项目结构和文件。

## 基于设计原则
1. **SOLID 原则**：确保组件设计符合单一职责原则和其他 SOLID 原则。
2. **DRY 原则（Don't Repeat Yourself）**：通过提取和重用代码，避免重复。
3. **可复用性**：设计组件时考虑其在不同上下文中的重用性。
4. **可扩展性**：保证组件的结构能够轻松应对未来功能的扩展。

## 组件封装方式
1. **组件化设计**：将 UI 和逻辑分离，创建可复用的组件。
2. **功能组件**：每个组件应专注于单一功能，避免过于复杂的逻辑。
3. **Hooks 和纯函数组件**：使用函数组件和 React Hooks 简化逻辑，避免不必要复杂性。
4. **组合优于继承** ：通过组合而非继承来实现组件的复用和扩展。
5. **组件文件名称**：使用小写字母，- 分割，例如：generate-history-grid.tsx
6. **使用ES6**：严格使用ES6 和 箭头函数定义组件和方法。
7. **组件格式**：
```TypeScript
import React from "react";
const Name: React.FC = () => {
return <>Name</>;
};

Name.displayName = "Name";

export default Name;
```


## 状态管理
1. **状态提升**：在多个组件需要共享状态时，将状态提升至最近公共父组件。
2. **状态最小化**：保持状态简单不可变，确定最小必需状态。
3. **单一数据源**：确保有一个权威的数据源，减少冲突问题。
4. **Prop 数据传递**：通过 Props 传递数据，保持单向数据流。
5. **Hooks 使用**：多使用 Hooks（如 useState, useReducer）进行状态和副作用管理。
6. **状态生命周期理解**：严格遵循 React 生命周期最佳实践。
7. **副作用管理**：使用 useEffect 和 useLayoutEffect 管理副作用。

## 全局状态管理
1. **使用 Jotai**：使用 Jotai 作为全局状态管理解决方案，简化状态共享和管理。
2. **Atom 和 Selector**：使用 Atom 定义状态，Selector 计算派生状态。
3. **原子化状态**：将全局状态拆分为多个小的原子状态，便于管理和更新。
4. **避免全局状态污染**：
- 确保全局状态只包含必要的数据，避免过度依赖全局状态。
- 使用局部状态代替全局状态，除非确实需要跨组件共享数据。
5. **状态更新**：使用 Jotai 提供的 `useAtom` 钩子进行状态更新，确保组件在状态变化时正确重新渲染。
6. **性能优化**：使用 Jotai 的 `useAtomValue` 钩子获取状态值，避免不必要的重新渲染。



## 错误处理
1. **异常捕获**
2. **Error Boundary**：使用 Error Boundary 捕获并处理渲染异常。
3. **异步操作错误**：为异步请求设置适当的错误处理机制。


## 类型安全
1. **TypeScript 强类型**：推广使用 TypeScript，启用严格模式保证类型安全。
2. **避免使用 any**：减少或避免使用 any 类型，使用更具体的类型定义。
3. **处理空值**：在代码中妥善处理可能的空值或未定义情况。



## 样式管理
1. **使用 Tailwind CSS**：使用 Tailwind CSS 进行样式管理，确保样式一致性和可维护性。
2. **最小粒度的组件化**：将样式应用到最小可用组件，提高重用性。
3. **使用 framer-motion 动画效果**：为组件添加动画，提高用户体验。

## 渲染优化
1. **性能优化**：通过 React.memo、useMemo、useCallback 等技术优化性能，避免不必要的重新渲染。
2. **懒加载**：使用 React.lazy 和 Suspense 实现组件懒加载，提升初始加载速度。
3. **虚拟化列表**：对于长列表，使用 react-window 或 react-virtualized 实现虚拟化，提升渲染性能。

## 文档规范
1. **注释规范**
- **复杂逻辑注释**：解释复杂逻辑，帮助其他开发者理解。
- **JSDoc 标注**：为函数和组件添加详细的 JSDoc 文档。

2. **文档撰写**
- **组件说明书**：编写组件功能、依赖、输入和输出文档。
- **版本变更说明**：记录重要更新和 API 变更。


## 命令
1. 始终使用 pnpm 命令。
2. 使用 Shadcn-ui 时，命令为：pnpm dlx shadcn@Latest xxxx

## 网络请求
1. **使用 SWR**：使用 SWR 进行网络请求，确保请求和响应的类型安全。
2. **错误处理**：为网络请求添加错误处理逻辑，确保用户体验。
3. **缓存策略**：合理配置 SWR 的缓存策略，减少不必要的网络请求。
4. **数据获取**：使用 SWR 的 `useSWR` 钩子获取数据，确保数据的实时性和一致性。
5. **请求参数**：确保请求参数类型安全，避免使用 any 类型。
6. **响应处理**：对响应数据进行类型检查和处理，确保数据的。


## 其他说明
1. 网页内容语言始终使用英语，代码注释可以使用中文。
2. 组件和文件命名使用小写字母，- 分割。
3. 确保代码风格一致，遵循团队约定的代码规范。
4. 在 import 的时候，始终使用 @ 别名。

## 目录结构
```
/my-app
├── public/ # 静态资源目录（favicon、图像等）
│ └── images/
│ └── logo.png
│
├── src/ # 源代码目录
│ ├── app/ # App Router 页面结构
│ │ ├── layout.tsx # 全局布局（Header/Footer）
│ │ ├── page.tsx # 主页（/）
│ │ ├── about/page.tsx # 静态页面（/about）
│ │ ├── blog/[slug]/page.tsx # 动态页面（如 /blog/hello-world）
│ │ └── api/ # API routes（/api/...）
│ │ └── ping/route.ts
│ │
│ ├── components/ # 组件目录（结构清晰、按功能分类）
│ │ ├── layout/ # 页面结构组件
│ │ │ ├── header.tsx
│ │ │ └── footer.tsx
│ │ ├── ui/ # 原子 UI 组件（按钮、输入框等）
│ │ │ ├── button.tsx
│ │ │ └── input.tsx
│ │ ├── shared/ # 可复用功能性组件（如 PostCard、CardList）
│ │ │ └── post-card.tsx
│ │ └── seo/ # SEO Head 组件
│ │ └── seo-head.tsx
│ │
│ ├── hooks/ # 自定义 React Hooks
│ │ ├── use-search.ts
│ │ └── use-responsive.ts
│ │
│ ├── lib/ # 工具函数 / 逻辑封装（fetch、markdown、缓存等）
│ │ ├── api.ts
│ │ ├── markdown.ts
│ │ └── constants.ts
│ │
│ ├── types/ # 类型定义
│ │ ├── post.ts
│ │ └── index.ts
│ │
│ ├── data/ # 静态数据（JSON、MDX、YAML）
│ │ ├── posts/
│ │ │ └── hello-world.md
│ │ └── site-config.json
│ │
│ ├── styles/ # 样式文件（支持 Tailwind / SCSS / CSS Modules）
│ │ ├── globals.css
│ │ └── theme.css
│ │
│ └── middleware.ts # Next.js 中间件（可选）
│
├── .env.local # 环境变量
├── next.config.js # Next.js 配置
├── tailwind.config.ts # Tailwind 配置（如使用）
├── tsconfig.json # TypeScript 配置
└── README.md
```# coding

这是一个规则文件，用于帮助 AI 理解您的代码库和遵循项目约定。