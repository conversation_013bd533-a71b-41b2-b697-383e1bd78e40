// 图标生成工具
// 在浏览器控制台中运行此脚本来生成PNG图标

function generateIcon(size = 128) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = size;
  canvas.height = size;

  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#6366f1');
  gradient.addColorStop(1, '#3b82f6');

  // 绘制背景圆形
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(size/2, size/2, size * 0.45, 0, 2 * Math.PI);
  ctx.fill();

  // 绘制内圆边框
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
  ctx.lineWidth = 2;
  ctx.beginPath();
  ctx.arc(size/2, size/2, size * 0.375, 0, 2 * Math.PI);
  ctx.stroke();

  // 绘制链接图标
  const linkSize = size * 0.15;
  const linkStroke = size * 0.03;
  
  ctx.strokeStyle = 'white';
  ctx.lineWidth = linkStroke;
  ctx.lineCap = 'round';
  
  // 第一个链环
  const x1 = size * 0.3;
  const y1 = size * 0.35;
  ctx.strokeRect(x1, y1, linkSize * 1.5, linkSize, linkSize * 0.5);
  
  // 第二个链环
  const x2 = size * 0.45;
  const y2 = size * 0.5;
  ctx.strokeRect(x2, y2, linkSize * 1.5, linkSize, linkSize * 0.5);
  
  // 连接线
  ctx.beginPath();
  ctx.moveTo(x1 + linkSize, y1 + linkSize/2);
  ctx.lineTo(x2 + linkSize/2, y2 + linkSize/2);
  ctx.stroke();

  // 绘制上传箭头
  const arrowX = size * 0.5;
  const arrowY = size * 0.75;
  const arrowSize = size * 0.08;
  
  ctx.fillStyle = 'white';
  ctx.beginPath();
  ctx.moveTo(arrowX, arrowY);
  ctx.lineTo(arrowX - arrowSize, arrowY - arrowSize);
  ctx.lineTo(arrowX - arrowSize/2, arrowY - arrowSize);
  ctx.lineTo(arrowX - arrowSize/2, arrowY - arrowSize * 2);
  ctx.lineTo(arrowX + arrowSize/2, arrowY - arrowSize * 2);
  ctx.lineTo(arrowX + arrowSize/2, arrowY - arrowSize);
  ctx.lineTo(arrowX + arrowSize, arrowY - arrowSize);
  ctx.closePath();
  ctx.fill();

  // 绘制数据点
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
  ctx.beginPath();
  ctx.arc(size * 0.7, size * 0.3, size * 0.025, 0, 2 * Math.PI);
  ctx.fill();
  
  ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
  ctx.beginPath();
  ctx.arc(size * 0.75, size * 0.4, size * 0.015, 0, 2 * Math.PI);
  ctx.fill();
  
  ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
  ctx.beginPath();
  ctx.arc(size * 0.72, size * 0.45, size * 0.02, 0, 2 * Math.PI);
  ctx.fill();

  return canvas;
}

function downloadIcon(size = 128, filename = 'icon.png') {
  const canvas = generateIcon(size);
  
  // 转换为blob并下载
  canvas.toBlob(function(blob) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 'image/png');
}

// 使用方法：
// downloadIcon(128, 'icon.png');        // 主图标
// downloadIcon(64, 'icon-64.png');      // 中等图标
// downloadIcon(32, 'icon-32.png');      // 小图标
// downloadIcon(16, 'icon-16.png');      // 最小图标

console.log('图标生成工具已加载！');
console.log('使用 downloadIcon(128, "icon.png") 生成主图标');
console.log('使用 downloadIcon(64, "icon-64.png") 生成64x64图标');
console.log('使用 downloadIcon(32, "icon-32.png") 生成32x32图标');
console.log('使用 downloadIcon(16, "icon-16.png") 生成16x16图标');

// 自动生成所有尺寸
function generateAllSizes() {
  const sizes = [
    { size: 128, name: 'icon.png' },
    { size: 64, name: 'icon-64.png' },
    { size: 32, name: 'icon-32.png' },
    { size: 16, name: 'icon-16.png' }
  ];
  
  sizes.forEach(({ size, name }, index) => {
    setTimeout(() => {
      downloadIcon(size, name);
    }, index * 1000); // 延迟1秒避免同时下载
  });
}

// 使用 generateAllSizes() 一次性生成所有尺寸