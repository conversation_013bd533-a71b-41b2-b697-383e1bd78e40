# LinkTrackPro 扩展图标

## 生成新图标

### 方法一：使用图标预览页面（推荐）
1. 在浏览器中打开 `assets/icon-preview.html`
2. 点击相应的按钮生成PNG图标
3. 下载的文件会自动保存到浏览器下载文件夹
4. 将 `icon.png` 替换现有的 `assets/icon.png`

### 方法二：使用JavaScript控制台
1. 在浏览器中打开 `assets/icon-preview.html`
2. 打开开发者工具控制台
3. 运行以下命令：
   ```javascript
   downloadIcon(128, 'icon.png');        // 生成主图标
   downloadIcon(64, 'icon-64.png');      // 生成64x64图标
   downloadIcon(32, 'icon-32.png');      // 生成32x32图标
   downloadIcon(16, 'icon-16.png');      // 生成16x16图标
   generateAllSizes();                   // 一次生成所有尺寸
   ```

### 方法三：使用SVG文件
- `assets/icon.svg` 包含矢量图标
- 可以使用在线SVG到PNG转换工具
- 或者使用设计软件如Figma、Sketch等导出PNG

## 图标设计说明

### 设计元素
- **渐变背景**：使用蓝色渐变 (#6366f1 到 #3b82f6)
- **链接图标**：两个相连的链环，象征外链管理
- **上传箭头**：表示提交/上传功能
- **数据点**：小圆点表示数据和分析

### 尺寸规格
- **128x128px**：主图标，用于扩展商店和设置
- **64x64px**：工具栏图标
- **32x32px**：菜单和小尺寸显示
- **16x16px**：最小尺寸，用于标签栏

### 颜色规范
- 主色：#6366f1 (Indigo 500)
- 次色：#3b82f6 (Blue 500)
- 白色元素：#ffffff
- 透明度：0.2-0.9不等

## 更新图标后的操作

1. 替换 `assets/icon.png` 文件
2. 如需更新扩展配置，检查 `package.json` 中的 manifest 配置
3. 重新构建扩展：`pnpm build`
4. 重新加载扩展以查看新图标