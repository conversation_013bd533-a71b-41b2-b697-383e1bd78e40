<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkTrackPro Extension Icon Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .icon-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .icon {
            width: 128px;
            height: 128px;
        }
        .icon-small {
            width: 64px;
            height: 64px;
        }
        .icon-tiny {
            width: 32px;
            height: 32px;
        }
        .sizes {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        h2 {
            color: #333;
            margin: 0 0 10px 0;
        }
        p {
            color: #666;
            margin: 0;
        }
    </style>
</head>
<body>
    <h1>LinkTrackPro 扩展图标预览</h1>
    
    <div class="icon-container">
        <svg class="icon" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="linkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
                </linearGradient>
                <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                    <dropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
                </filter>
            </defs>
            
            <!-- Background Circle -->
            <circle cx="64" cy="64" r="58" fill="url(#bgGradient)" filter="url(#shadow)"/>
            
            <!-- Inner Circle -->
            <circle cx="64" cy="64" r="48" fill="none" stroke="white" stroke-width="2" opacity="0.2"/>
            
            <!-- Link Chain Icon -->
            <g transform="translate(34, 34)">
                <!-- First Link -->
                <rect x="8" y="16" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient)" stroke-width="4"/>
                <!-- Second Link -->
                <rect x="28" y="36" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient)" stroke-width="4"/>
                <!-- Connector -->
                <path d="M 24 24 L 36 44" stroke="url(#linkGradient)" stroke-width="4" stroke-linecap="round"/>
            </g>
            
            <!-- Upload/Submit Arrow -->
            <g transform="translate(64, 78)">
                <path d="M 0 8 L -8 0 L -4 0 L -4 -12 L 4 -12 L 4 0 L 8 0 Z" fill="white" opacity="0.9"/>
            </g>
            
            <!-- Small Dots for Data Points -->
            <circle cx="88" cy="40" r="3" fill="white" opacity="0.7"/>
            <circle cx="96" cy="48" r="2" fill="white" opacity="0.5"/>
            <circle cx="92" cy="56" r="2.5" fill="white" opacity="0.6"/>
        </svg>
        
        <div>
            <h2>主图标 (128x128)</h2>
            <p>用于扩展商店和设置页面</p>
        </div>
    </div>
    
    <div class="icon-container">
        <div class="sizes">
            <svg class="icon-small" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="linkGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <circle cx="64" cy="64" r="58" fill="url(#bgGradient2)"/>
                <circle cx="64" cy="64" r="48" fill="none" stroke="white" stroke-width="2" opacity="0.2"/>
                
                <g transform="translate(34, 34)">
                    <rect x="8" y="16" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient2)" stroke-width="4"/>
                    <rect x="28" y="36" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient2)" stroke-width="4"/>
                    <path d="M 24 24 L 36 44" stroke="url(#linkGradient2)" stroke-width="4" stroke-linecap="round"/>
                </g>
                
                <g transform="translate(64, 78)">
                    <path d="M 0 8 L -8 0 L -4 0 L -4 -12 L 4 -12 L 4 0 L 8 0 Z" fill="white" opacity="0.9"/>
                </g>
                
                <circle cx="88" cy="40" r="3" fill="white" opacity="0.7"/>
                <circle cx="96" cy="48" r="2" fill="white" opacity="0.5"/>
                <circle cx="92" cy="56" r="2.5" fill="white" opacity="0.6"/>
            </svg>
            
            <svg class="icon-tiny" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bgGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <circle cx="64" cy="64" r="58" fill="url(#bgGradient3)"/>
                
                <g transform="translate(34, 34)">
                    <rect x="8" y="16" width="24" height="16" rx="8" ry="8" fill="none" stroke="white" stroke-width="5"/>
                    <rect x="28" y="36" width="24" height="16" rx="8" ry="8" fill="none" stroke="white" stroke-width="5"/>
                    <path d="M 24 24 L 36 44" stroke="white" stroke-width="5" stroke-linecap="round"/>
                </g>
                
                <g transform="translate(64, 78)">
                    <path d="M 0 8 L -8 0 L -4 0 L -4 -12 L 4 -12 L 4 0 L 8 0 Z" fill="white"/>
                </g>
            </svg>
        </div>
        
        <div>
            <h2>不同尺寸预览</h2>
            <p>64x64 (工具栏) 和 32x32 (菜单)</p>
        </div>
    </div>
    
    <div style="max-width: 600px; text-align: center; background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
        <h3>一键生成PNG图标</h3>
        <div style="display: flex; gap: 10px; justify-content: center; margin: 20px 0;">
            <button onclick="downloadIcon(128, 'icon.png')" style="padding: 10px 20px; background: #6366f1; color: white; border: none; border-radius: 6px; cursor: pointer;">
                生成128x128
            </button>
            <button onclick="downloadIcon(64, 'icon-64.png')" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">
                生成64x64
            </button>
            <button onclick="downloadIcon(32, 'icon-32.png')" style="padding: 10px 20px; background: #1d4ed8; color: white; border: none; border-radius: 6px; cursor: pointer;">
                生成32x32
            </button>
            <button onclick="generateAllSizes()" style="padding: 10px 20px; background: #059669; color: white; border: none; border-radius: 6px; cursor: pointer;">
                生成所有尺寸
            </button>
        </div>
        <p style="color: #666; font-size: 14px;">点击按钮自动下载PNG图标文件，然后替换 assets/icon.png</p>
    </div>
    
    <script src="generate-icon.js"></script>
</body>
</html>