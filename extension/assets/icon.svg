<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="linkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <dropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="58" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Inner Circle -->
  <circle cx="64" cy="64" r="48" fill="none" stroke="white" stroke-width="2" opacity="0.2"/>
  
  <!-- Link Chain Icon -->
  <g transform="translate(34, 34)">
    <!-- First Link -->
    <rect x="8" y="16" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient)" stroke-width="4"/>
    <!-- Second Link -->
    <rect x="28" y="36" width="24" height="16" rx="8" ry="8" fill="none" stroke="url(#linkGradient)" stroke-width="4"/>
    <!-- Connector -->
    <path d="M 24 24 L 36 44" stroke="url(#linkGradient)" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Upload/Submit Arrow -->
  <g transform="translate(64, 78)">
    <path d="M 0 8 L -8 0 L -4 0 L -4 -12 L 4 -12 L 4 0 L 8 0 Z" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Small Dots for Data Points -->
  <circle cx="88" cy="40" r="3" fill="white" opacity="0.7"/>
  <circle cx="96" cy="48" r="2" fill="white" opacity="0.5"/>
  <circle cx="92" cy="56" r="2.5" fill="white" opacity="0.6"/>
</svg>