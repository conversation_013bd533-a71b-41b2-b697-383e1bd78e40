# 外链提交助手 - 快速开始指南

## 🚀 快速部署

### 1. 环境准备
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 2. 浏览器安装
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解包的扩展程序"
5. 选择 `build/chrome-mv3-dev` 目录

### 3. 基础配置
1. 点击扩展图标，选择"打开侧边栏"
2. 进入"设置"页面
3. 配置API Key（可选，用于付费功能）
4. 选择主题和语言偏好

## 📖 核心功能使用

### 项目管理
- **添加项目**: 点击"项目管理" → "添加项目"
- **编辑项目**: 点击项目卡片的编辑按钮
- **AI优化**: 启用AI优化功能自动改进项目描述

### 外链库管理  
- **保存当前网址**: 在任意网页点击"保存当前网址"
- **手动添加**: 点击"外链库" → "添加外链"
- **分类筛选**: 使用付费/免费标签筛选平台

### 表单自动填充
- **检测表单**: 在目标网站点击"检测页面表单"
- **选择项目**: 从项目列表中选择要提交的项目
- **自动填充**: 系统自动匹配字段并填充内容
- **手动调整**: 可手动调整字段映射关系

### AI内容优化
- **项目优化**: 在项目表单中启用AI优化
- **平台定制**: AI根据不同平台特点优化内容
- **批量处理**: 支持批量优化多个项目

## ⚙️ 高级配置

### 环境变量配置
复制 `env.example` 为 `.env` 并配置：
```bash
# API配置
PLASMO_PUBLIC_API_URL=https://api.linktrackpro.com
PLASMO_PUBLIC_API_TIMEOUT=10000

# 重试配置  
PLASMO_PUBLIC_RETRY_ATTEMPTS=3
PLASMO_PUBLIC_RETRY_DELAY=1000
```

### API Key 配置
1. 获取API Key（联系管理员）
2. 在设置页面输入API Key
3. 点击"验证"确认有效性
4. 保存配置自动切换到付费模式

### 数据管理
- **导出数据**: 设置 → 数据管理 → 导出数据
- **导入数据**: 设置 → 数据管理 → 导入数据
- **数据迁移**: 免费/付费模式间自动数据迁移

## 🔧 开发调试

### 开发命令
```bash
# 开发模式（热重载）
pnpm dev

# 生产构建
pnpm build

# 打包扩展
pnpm package

# 代码检查
pnpm lint
```

### 调试技巧
1. **查看日志**: 打开浏览器开发者工具 → Console
2. **扩展调试**: chrome://extensions → 点击"检查视图"
3. **网络请求**: 开发者工具 → Network 标签
4. **本地存储**: 开发者工具 → Application → Storage

## 🆘 常见问题

### Q: 扩展图标点击没反应？
A: 检查是否正确加载扩展，重新加载扩展程序。

### Q: 表单检测失败？
A: 确保网页已完全加载，刷新页面后重试。

### Q: API Key 验证失败？
A: 检查网络连接，确认API Key正确性。

### Q: 数据丢失了？
A: 检查本地存储，使用数据导出功能定期备份。

## 📞 技术支持

- **项目仓库**: [GitHub](https://github.com/linktrackpro/extension)
- **问题反馈**: 提交 Issue
- **功能建议**: 发起 Discussion
- **技术文档**: 查看项目 Wiki

---

🎉 恭喜！您已完成基础配置，开始高效的外链提交工作吧！