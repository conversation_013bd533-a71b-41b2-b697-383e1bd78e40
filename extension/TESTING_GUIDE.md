# LinkTrackPro 扩展测试指南

## 修复内容总结

我已经修复了以下三个主要问题：

### 1. 表单检测连接错误修复

**问题**: "Could not establish connection. Receiving end does not exist."

**修复内容**:
- 添加了重试机制（最多3次重试）
- 实现了content script加载检测和自动重注入
- 添加了超时处理（10秒超时）
- 改进了错误消息，提供用户友好的提示
- 添加了PING消息处理来检测content script状态

**修改文件**:
- `src/store/index.ts`: 改进了 `detectForms` 方法
- `src/contents/form-detector.tsx`: 添加了PING消息处理和详细日志
- `src/components/form/form-detector.tsx`: 改进了错误显示

### 2. API Key验证失败问题修复

**问题**: API key验证失败，缺少调试信息

**修复内容**:
- 添加了详细的调试日志输出
- 改进了错误处理和错误消息
- 添加了网络连接问题的诊断信息
- 提供了具体的故障排除步骤

**修改文件**:
- `src/services/managers/settings-manager.ts`: 添加了详细的验证日志
- `src/components/settings/settings-form.tsx`: 改进了错误处理和显示

### 3. 调试日志改进

**问题**: 缺少足够的调试信息

**修复内容**:
- 所有日志都添加了 `[LinkTrackPro]` 前缀便于识别
- 添加了详细的表单检测过程日志
- 添加了API验证过程的完整日志
- 改进了错误显示支持多行文本

## 测试步骤

### 1. 构建扩展

```bash
cd /Users/<USER>/Code/LinkTrackPro/extension
pnpm install
pnpm build
```

### 2. 加载扩展到Chrome

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `build` 文件夹

### 3. 测试表单检测功能

1. 访问任何包含表单的网页（如GitHub、Google Forms等）
2. 点击扩展图标打开popup
3. 切换到"表单检测"页面
4. 点击"检测表单"按钮
5. 查看结果，如果出现错误：
   - 打开浏览器开发者工具（F12）
   - 查看控制台中带有 `[LinkTrackPro]` 前缀的日志
   - 尝试刷新页面后重新检测

### 4. 测试API Key验证

1. 确保本地API服务器运行在 `http://localhost:3000`
2. 点击扩展图标，进入设置页面
3. 输入API Key和URL
4. 点击"验证"按钮
5. 如果验证失败：
   - 打开浏览器开发者工具（F12）
   - 查看控制台中的详细错误信息
   - 检查API服务器是否正常运行
   - 检查CORS设置

## 调试信息说明

### 表单检测日志示例

```
[LinkTrackPro] 开始检测表单...
[LinkTrackPro] 当前页面URL: https://example.com
[LinkTrackPro] 页面加载状态: complete
[LinkTrackPro] 页面中发现 2 个form元素
[LinkTrackPro] 检测到 1 个有效表单
[LinkTrackPro] 表单 1: {confidence: 0.85, fieldsCount: 5, hasSubmitButton: true}
```

### API验证日志示例

```
[LinkTrackPro] 开始验证API Key...
[LinkTrackPro] 验证URL: http://localhost:3000/auth/validate
[LinkTrackPro] API Key长度: 32
[LinkTrackPro] 发送验证请求...
[LinkTrackPro] 验证响应状态: 200
[LinkTrackPro] 验证成功响应: {valid: true, user: {...}}
```

## 常见问题解决

### 表单检测问题

1. **"页面内容脚本未加载"**
   - 刷新页面后重试
   - 检查扩展是否有content script权限

2. **"页面响应超时"**
   - 等待页面完全加载后重试
   - 检查页面是否有JavaScript错误

### API验证问题

1. **"API服务器未运行"**
   - 确保Next.js应用运行在指定端口
   - 检查API端点是否存在

2. **"CORS配置问题"**
   - 检查API服务器的CORS设置
   - 确保允许扩展的origin

## 开发模式调试

如果需要进一步调试，可以：

1. 修改 `manifest.json` 添加更多权限
2. 在content script中添加 `debugger;` 断点
3. 使用Chrome扩展开发者工具
4. 查看后台页面的控制台日志

## 生产部署前检查

- [ ] 表单检测功能正常
- [ ] API验证功能正常  
- [ ] 错误处理友好
- [ ] 日志信息适量（生产环境可考虑减少日志）
- [ ] 所有修改通过测试