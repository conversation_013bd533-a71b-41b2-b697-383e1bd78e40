<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transform函数修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>Transform函数修复测试页面</h2>
        <p>此页面用于测试AI提交时transform函数缺失的修复。包含多种字段类型，用于验证所有字段都能正确获得transform函数。</p>
    </div>

    <!-- 测试表单1：项目提交表单 -->
    <div class="form-container">
        <h2 class="form-title">项目提交表单</h2>
        <form id="project-form">
            <div class="form-group">
                <label for="project_name">项目名称 *</label>
                <input type="text" id="project_name" name="project_name" required>
            </div>
            
            <div class="form-group">
                <label for="website_url">项目网址</label>
                <input type="url" id="website_url" name="website_url">
            </div>
            
            <div class="form-group">
                <label for="description">项目描述 *</label>
                <textarea id="description" name="description" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="category">项目分类</label>
                <select id="category" name="category">
                    <option value="">请选择分类</option>
                    <option value="web-app">Web应用</option>
                    <option value="mobile-app">移动应用</option>
                    <option value="tool">工具</option>
                    <option value="game">游戏</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="author_name">作者姓名</label>
                <input type="text" id="author_name" name="author_name">
            </div>
            
            <div class="form-group">
                <label for="mcp_avatar_url">头像URL</label>
                <input type="url" id="mcp_avatar_url" name="mcp_avatar_url">
            </div>
            
            <div class="form-group">
                <label for="email">联系邮箱</label>
                <input type="email" id="email" name="email">
            </div>
            
            <div class="form-group">
                <label for="detail">详细信息</label>
                <textarea id="detail" name="detail"></textarea>
            </div>
            
            <div class="form-group">
                <label for="subscribe_newsletter">订阅新闻通讯</label>
                <input type="checkbox" id="subscribe_newsletter" name="subscribe_newsletter">
            </div>
            
            <button type="submit">提交项目</button>
            <button type="reset">重置表单</button>
        </form>
    </div>

    <!-- 测试表单2：联系表单 -->
    <div class="form-container">
        <h2 class="form-title">联系我们</h2>
        <form id="contact-form">
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="contact_email">邮箱 *</label>
                <input type="email" id="contact_email" name="contact_email" required>
            </div>
            
            <div class="form-group">
                <label for="subject">主题</label>
                <input type="text" id="subject" name="subject">
            </div>
            
            <div class="form-group">
                <label for="message">消息内容 *</label>
                <textarea id="message" name="message" required></textarea>
            </div>
            
            <button type="submit">发送消息</button>
        </form>
    </div>

    <!-- 测试表单3：注册表单 -->
    <div class="form-container">
        <h2 class="form-title">用户注册</h2>
        <form id="signup-form">
            <div class="form-group">
                <label for="username">用户名 *</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="user_email">邮箱 *</label>
                <input type="email" id="user_email" name="user_email" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码 *</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认密码 *</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <div class="form-group">
                <label for="bio">个人简介</label>
                <textarea id="bio" name="bio" placeholder="简单介绍一下自己..."></textarea>
            </div>
            
            <button type="submit">注册账户</button>
        </form>
    </div>

    <script>
        // 添加表单提交事件监听器，防止实际提交
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('表单提交被阻止（测试模式）');
                alert('表单提交被阻止（测试模式）');
            });
        });
        
        console.log('Transform函数修复测试页面已加载');
        console.log('页面包含', document.querySelectorAll('form').length, '个表单');
        console.log('总共', document.querySelectorAll('input, textarea, select').length, '个表单字段');
    </script>
</body>
</html>
