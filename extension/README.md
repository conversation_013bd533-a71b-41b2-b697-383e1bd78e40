# plasmo-tailwindcss-shadcn-template

A chrome extension template using 
- [Plasmo](https://docs.plasmo.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn/ui](https://ui.shadcn.com/)

## Getting Started

First, run the development server:

```bash
pnpm dev
# or
npm run dev
```

For further guidance, [visit our Documentation](https://docs.plasmo.com/)

## Making production build

Run the following:

```bash
pnpm build
# or
npm run build
```

This should create a production bundle for your extension, ready to be zipped and published to the stores.

## Feature

plasmo + shadcn + tailwindcss


## Adding shadcn components

```
pnpm dlx shadcn@latest add #name
e.g. pnpm dlx shadcn@latest add card
```