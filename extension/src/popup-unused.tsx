import "~main.css"

import React from 'react'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'

// 注意：此弹窗组件目前不作为默认操作使用
// 扩展图标点击时会直接打开侧边栏（通过 background.ts 处理）
// 此文件保留用于潜在的手动访问或未来功能

function IndexPopup() {
  const openSidePanel = async () => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]?.id) {
        await chrome.sidePanel.open({ tabId: tabs[0].id })
        // 关闭popup
        window.close()
      }
    } catch (error) {
      console.error('打开侧边栏失败:', error)
    }
  }

  const openInNewTab = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('sidepanel.html') })
    window.close()
  }

  return (
    <div className="w-80 p-4 bg-background text-foreground">
      <Card>
        <CardContent className="py-6">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 bg-primary rounded-lg">
              <svg className="w-6 h-6 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h1 className="text-lg font-semibold mb-1">外链提交助手</h1>
            <p className="text-sm text-muted-foreground">智能项目提交和表单填充工具</p>
          </div>

          <div className="space-y-3">
            <Button 
              onClick={openSidePanel}
              className="w-full h-11"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              打开侧边栏
            </Button>

            <Button 
              onClick={openInNewTab}
              variant="outline"
              className="w-full h-11"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              在新标签页中打开
            </Button>

            <div className="pt-3 border-t">
              <div className="text-xs text-muted-foreground text-center">
                版本 1.0.0 | by LinkTrackPro
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default IndexPopup
