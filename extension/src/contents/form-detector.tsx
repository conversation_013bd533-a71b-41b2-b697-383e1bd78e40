import type { PlasmoCSConfig } from "plasmo"
import { AdvancedFormDetector, FormMonitorService, PageAnalyzer } from "~services/form"
import type { FormElement, DetectionEnvironment } from "~types"

export const config: PlasmoCSConfig = {
  matches: ["https://*/*"],
  all_frames: true
}

class ContentFormDetector {
  private detector: AdvancedFormDetector
  private monitor: FormMonitorService
  private detectedForms: FormElement[] = []
  private highlightedElements: Set<HTMLElement> = new Set()
  private isActive = false
  private isMonitoring = false
  private contextHealthCheck: number | null = null
  private isContextInvalidated = false

  constructor() {
    this.detector = new AdvancedFormDetector()
    this.monitor = new FormMonitorService()
    this.initializeMessageHandlers()
    this.setupFormMonitoring()
    this.startContextHealthCheck()
  }

  private initializeMessageHandlers(): void {
    // 检查extension context是否有效
    const checkContext = (): boolean => {
      try {
        return !!chrome.runtime?.id
      } catch {
        return false
      }
    }

    // 监听来自侧边栏的消息
    const messageHandler = (message: any, sender: any, sendResponse: any) => {
      // 检查context有效性
      if (!checkContext()) {
        console.warn('[ContentFormDetector] Extension context invalidated, ignoring message:', message.type)
        try {
          sendResponse({ success: false, error: 'Extension context invalidated' })
        } catch (error) {
          console.warn('[ContentFormDetector] Failed to send error response:', error)
        }
        return false
      }

      try {
        switch (message.type) {
          case 'PING':
            sendResponse({ success: true, message: 'Content script loaded and ready' })
            break

          case 'DETECT_FORMS':
            this.handleDetectForms()
              .then(forms => {
                if (checkContext()) {
                  sendResponse({ success: true, forms })
                }
              })
              .catch(error => {
                if (checkContext()) {
                  sendResponse({ success: false, error: error.message })
                }
              })
            return true // 异步响应

          case 'START_MONITORING':
            this.handleStartMonitoring(message.options)
            sendResponse({ success: true })
            break

          case 'STOP_MONITORING':
            this.handleStopMonitoring()
            sendResponse({ success: true })
            break

          case 'HIGHLIGHT_FORM':
            this.handleHighlightForm(message.formIndex)
            sendResponse({ success: true })
            break

          case 'CLEAR_HIGHLIGHTS':
            this.handleClearHighlights()
            sendResponse({ success: true })
            break

          case 'GET_FORM_CONTEXT':
            this.handleGetFormContext(message.formIndex)
              .then(context => {
                if (checkContext()) {
                  sendResponse({ success: true, context })
                }
              })
              .catch(error => {
                if (checkContext()) {
                  sendResponse({ success: false, error: error.message })
                }
              })
            return true

          case 'PREVIEW_FORM_FILL':
            this.handlePreviewFormFill(message.formIndex, message.project)
              .then(preview => {
                if (checkContext()) {
                  sendResponse({ success: true, preview })
                }
              })
              .catch(error => {
                if (checkContext()) {
                  sendResponse({ success: false, error: error.message })
                }
              })
            return true

          case 'FILL_FORM':
            this.handleFillForm(message.formIndex, message.project, message.mapping)
              .then(result => {
                if (checkContext()) {
                  sendResponse({ success: true, result })
                }
              })
              .catch(error => {
                if (checkContext()) {
                  sendResponse({ success: false, error: error.message })
                }
              })
            return true

          case 'CLEAR_FORM_FIELDS':
            this.handleClearFormFields(message.formIndex)
              .then(result => {
                if (checkContext()) {
                  sendResponse({ success: true, result })
                }
              })
              .catch(error => {
                if (checkContext()) {
                  sendResponse({ success: false, error: error.message })
                }
              })
            return true

          default:
            sendResponse({ success: false, error: 'Unknown message type' })
        }
      } catch (error) {
        console.error('[ContentFormDetector] Message handler error:', error)
        try {
          if (checkContext()) {
            sendResponse({ success: false, error: error instanceof Error ? error.message : 'Unknown error' })
          }
        } catch (responseError) {
          console.warn('[ContentFormDetector] Failed to send error response:', responseError)
        }
      }
    }

    try {
      chrome.runtime.onMessage.addListener(messageHandler)
    } catch (error) {
      console.error('[ContentFormDetector] Failed to add message listener:', error)
    }
  }

  private async handleDetectForms(): Promise<any[]> {
    try {
      console.log('[LinkTrackPro] 开始高级表单检测...')
      console.log('[LinkTrackPro] 当前页面URL:', window.location.href)
      console.log('[LinkTrackPro] 页面加载状态:', document.readyState)
      
      // 等待页面完全加载
      if (document.readyState !== 'complete') {
        console.log('[LinkTrackPro] 页面尚未完全加载，等待中...')
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve(void 0)
          } else {
            window.addEventListener('load', resolve, { once: true })
          }
        })
        console.log('[LinkTrackPro] 页面加载完成')
      }

      // 创建检测环境
      const pageContext = PageAnalyzer.analyzePageContext(document, window)
      const detectionEnv: DetectionEnvironment = {
        document,
        window,
        pageContext,
        options: {
          strategy: 'deep_scan',
          enableCache: true,
          deepScan: true,
          minConfidence: 0.3,
          maxForms: 10
        }
      }

      console.log('[LinkTrackPro] 页面分析完成:', {
        framework: pageContext.hasJavaScript ? 'SPA' : 'Static',
        complexity: pageContext.domComplexity,
        language: pageContext.language
      })

      // 使用新的检测器
      this.detectedForms = await this.detector.detectForms(detectionEnv)
      console.log(`[LinkTrackPro] 检测到 ${this.detectedForms.length} 个有效表单`)

      // 输出详细的表单信息用于调试
      this.detectedForms.forEach((form, index) => {
        console.log(`[LinkTrackPro] 表单 ${index + 1}:`, {
          id: form.id,
          confidence: form.confidence,
          fieldsCount: form.fields.length,
          hasSubmitButton: !!form.submitButton,
          category: form.metadata.category,
          platform: form.metadata.platform,
          framework: form.metadata.framework,
          semanticFields: form.fields.filter(f => f.semanticType !== 'unknown').length
        })
      })

      // 为每个表单添加序列化信息
      const serializedForms = this.detectedForms.map((form, index) => ({
        index,
        id: form.id,
        confidence: form.confidence,
        fieldsCount: form.fields.length,
        hasSubmitButton: !!form.submitButton,
        context: {
          title: form.metadata.title,
          description: form.metadata.description,
          category: form.metadata.category,
          platform: form.metadata.platform,
          action: form.element.action,
          method: form.metadata.method
        },
        fields: form.fields.map(field => ({
          id: field.id,
          name: field.name,
          type: field.type,
          label: field.label,
          placeholder: field.placeholder,
          required: field.required,
          maxLength: field.maxLength,
          semanticType: field.semanticType,
          confidence: field.confidence
        }))
      }))

      console.log('[LinkTrackPro] 表单序列化完成，返回结果')
      return serializedForms
    } catch (error) {
      console.error('[LinkTrackPro] 表单检测失败:', error)
      console.error('[LinkTrackPro] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace')
      throw error
    }
  }

  private setupFormMonitoring(): void {
    try {
      // 设置监控回调
      this.monitor.onFormAdded((form) => {
        console.log('[LinkTrackPro] 监控发现新表单:', form.id)
        this.sendMessageToSidebar({
          type: 'FORM_ADDED',
          form: this.serializeForm(form)
        })
      })

      this.monitor.onFormRemoved((formId) => {
        console.log('[LinkTrackPro] 监控检测到表单移除:', formId)
        this.sendMessageToSidebar({
          type: 'FORM_REMOVED',
          formId
        })
      })

      this.monitor.onFormChanged((form, changes) => {
        console.log('[LinkTrackPro] 监控检测到表单变化:', form.id, changes)
        this.sendMessageToSidebar({
          type: 'FORM_CHANGED',
          form: this.serializeForm(form),
          changes
        })
      })

    } catch (error) {
      console.error('[LinkTrackPro] 设置表单监控失败:', error)
    }
  }

  private handleStartMonitoring(options?: any): void {
    if (this.isMonitoring) {
      console.log('[LinkTrackPro] 表单监控已在运行')
      return
    }

    try {
      const monitoringOptions = {
        debounceMs: 2000,
        enableMutationObserver: true,
        enableIntersectionObserver: true,
        trackVisibilityChanges: true,
        ...options
      }

      this.monitor.startMonitoring(monitoringOptions)
      this.isMonitoring = true
      
      console.log('[LinkTrackPro] 表单实时监控已启动')
    } catch (error) {
      console.error('[LinkTrackPro] 启动表单监控失败:', error)
    }
  }

  private handleStopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    try {
      this.monitor.stopMonitoring()
      this.isMonitoring = false
      
      console.log('[LinkTrackPro] 表单实时监控已停止')
    } catch (error) {
      console.error('[LinkTrackPro] 停止表单监控失败:', error)
    }
  }

  private serializeForm(form: FormElement): any {
    return {
      id: form.id,
      confidence: form.confidence,
      fieldsCount: form.fields.length,
      hasSubmitButton: !!form.submitButton,
      context: {
        title: form.metadata.title,
        description: form.metadata.description,
        category: form.metadata.category,
        platform: form.metadata.platform,
        action: form.element.action,
        method: form.metadata.method
      },
      fields: form.fields.map(field => ({
        id: field.id,
        name: field.name,
        type: field.type,
        label: field.label,
        placeholder: field.placeholder,
        required: field.required,
        maxLength: field.maxLength,
        semanticType: field.semanticType,
        confidence: field.confidence
      }))
    }
  }

  private sendMessageToSidebar(message: any): void {
    try {
      // 检查上下文有效性
      if (!this.checkContext()) {
        console.warn('[ContentFormDetector] Extension context not available, cannot send message')
        this.handleContextInvalidation()
        return
      }

      chrome.runtime.sendMessage(message).catch(error => {
        // 检查是否是上下文失效错误
        if (error.message.includes('Extension context invalidated')) {
          console.warn('[ContentFormDetector] Extension context invalidated, message not sent:', message.type)
          this.handleContextInvalidation()
        } else {
          console.warn('[ContentFormDetector] 发送消息到侧边栏失败:', error)
        }
      })
    } catch (error) {
      console.warn('[ContentFormDetector] 发送消息失败，扩展上下文可能已失效:', error)
      this.handleContextInvalidation()
    }
  }

  private checkContext(): boolean {
    try {
      return !!chrome.runtime?.id
    } catch {
      return false
    }
  }

  private startContextHealthCheck(): void {
    // 每10秒检查一次扩展上下文健康状态
    this.contextHealthCheck = window.setInterval(() => {
      if (!this.checkContext() && !this.isContextInvalidated) {
        console.warn('[ContentFormDetector] 检测到扩展上下文失效')
        this.handleContextInvalidation()
      } else if (this.checkContext() && this.isContextInvalidated) {
        console.log('[ContentFormDetector] 扩展上下文已恢复')
        this.handleContextRecovery()
      }
    }, 10000)
  }

  private handleContextInvalidation(): void {
    if (this.isContextInvalidated) return

    console.warn('[ContentFormDetector] 处理扩展上下文失效')
    this.isContextInvalidated = true

    // 停止监控以避免更多错误
    this.handleStopMonitoring()
    
    // 清除高亮显示
    this.clearHighlights()
    
    // 在页面显示警告信息
    this.showContextInvalidationWarning()
  }

  private handleContextRecovery(): void {
    console.log('[ContentFormDetector] 处理扩展上下文恢复')
    this.isContextInvalidated = false
    
    // 移除警告信息
    this.removeContextInvalidationWarning()
    
    // 重新初始化
    try {
      this.initializeMessageHandlers()
      this.setupFormMonitoring()
    } catch (error) {
      console.error('[ContentFormDetector] 上下文恢复时重新初始化失败:', error)
    }
  }

  private showContextInvalidationWarning(): void {
    // 移除之前的警告（如果存在）
    this.removeContextInvalidationWarning()

    const warning = document.createElement('div')
    warning.id = 'linktrackpro-context-warning'
    warning.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 12px 16px;
      border-radius: 8px;
      font-family: system-ui, sans-serif;
      font-size: 14px;
      z-index: 10000;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      max-width: 300px;
    `
    warning.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <svg style="width: 16px; height: 16px; flex-shrink: 0;" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <div style="font-weight: 600; margin-bottom: 4px;">LinkTrackPro 扩展异常</div>
          <div style="font-size: 13px;">扩展上下文已失效，请刷新页面或重新加载扩展</div>
        </div>
      </div>
    `
    
    document.body.appendChild(warning)
    
    // 5秒后自动消失
    setTimeout(() => {
      this.removeContextInvalidationWarning()
    }, 5000)
  }

  private removeContextInvalidationWarning(): void {
    const warning = document.getElementById('linktrackpro-context-warning')
    if (warning) {
      warning.remove()
    }
  }

  private cleanup(): void {
    // 清理健康检查定时器
    if (this.contextHealthCheck) {
      clearInterval(this.contextHealthCheck)
      this.contextHealthCheck = null
    }
    
    // 停止监控
    this.handleStopMonitoring()
    
    // 清除高亮
    this.clearHighlights()
    
    // 移除警告
    this.removeContextInvalidationWarning()
  }

  private handleHighlightForm(formIndex: number): void {
    this.clearHighlights()

    if (formIndex >= 0 && formIndex < this.detectedForms.length) {
      const form = this.detectedForms[formIndex]
      
      // 高亮表单
      this.highlightElement(form.element, 'form')
      
      // 高亮表单字段
      form.fields.forEach(field => {
        this.highlightElement(field.element, 'field')
      })

      // 高亮提交按钮
      if (form.submitButton) {
        this.highlightElement(form.submitButton, 'submit')
      }

      this.isActive = true
    }
  }

  private handleClearHighlights(): void {
    this.clearHighlights()
    this.isActive = false
  }

  private async handleGetFormContext(formIndex: number): Promise<any> {
    if (formIndex >= 0 && formIndex < this.detectedForms.length) {
      const form = this.detectedForms[formIndex]
      return this.detector.getFormContext(form.element)
    }
    throw new Error('表单索引无效')
  }

  private async handlePreviewFormFill(formIndex: number, project: any): Promise<any> {
    if (formIndex < 0 || formIndex >= this.detectedForms.length) {
      throw new Error('表单索引无效')
    }

    const form = this.detectedForms[formIndex]
    
    // 动态导入FormFiller
    const { FormFiller } = await import('~services/form')
    const filler = new FormFiller()

    try {
      const preview = await filler.previewFill(form, project)
      return preview
    } catch (error) {
      console.error('预览填充失败:', error)
      throw error
    }
  }

  private async handleFillForm(formIndex: number, project: any, mapping?: any): Promise<any> {
    if (formIndex < 0 || formIndex >= this.detectedForms.length) {
      throw new Error('表单索引无效')
    }

    const form = this.detectedForms[formIndex]

    // 动态导入FormFiller（避免在content script中直接导入大模块）
    const { FormFiller } = await import('~services/form')
    const filler = new FormFiller()

    try {
      // 重建映射对象的transform函数（Chrome消息传递会丢失函数）
      const reconstructedMapping = this.reconstructFieldMapping(mapping, form, project, filler)

      const result = await filler.fillForm(form, project, reconstructedMapping)

      // 如果填充成功，添加视觉反馈
      if (result.success) {
        this.showFillSuccess(form.element)
      }

      return result
    } catch (error) {
      console.error('表单填充失败:', error)
      throw error
    }
  }

  /**
   * 重建字段映射的transform函数
   * Chrome消息传递会序列化对象，导致函数丢失，需要在content script端重新创建
   */
  private reconstructFieldMapping(mapping: any, form: any, project: any, filler: any): any {
    if (!mapping) {
      // 如果没有映射，使用FormFiller的默认映射
      return filler.createFieldMapping(form.fields, project)
    }

    const reconstructedMapping: any = {}

    for (const [fieldName, fieldMapping] of Object.entries(mapping)) {
      const typedMapping = fieldMapping as any

      if (typedMapping.projectField === 'custom') {
        // 对于自定义字段，需要重新创建transform函数
        // 从原始映射中提取值（如果有的话）
        const customValue = typedMapping.customValue || ''

        reconstructedMapping[fieldName] = {
          projectField: 'custom',
          transform: () => customValue
        }

        console.log(`[ContentScript] 重建自定义字段 ${fieldName} 的transform函数，值: "${customValue}"`)
      } else {
        // 对于标准字段，使用FormFiller重新创建transform函数
        const field = form.fields.find((f: any) => f.name === fieldName)
        if (field) {
          reconstructedMapping[fieldName] = {
            projectField: typedMapping.projectField,
            transform: filler.createTransformFunction?.(field, typedMapping.projectField)
          }

          console.log(`[ContentScript] 重建标准字段 ${fieldName} 的transform函数`)
        }
      }
    }

    // 确保所有表单字段都有映射
    for (const field of form.fields) {
      if (!reconstructedMapping[field.name]) {
        // 为缺失的字段创建默认映射
        reconstructedMapping[field.name] = {
          projectField: 'custom',
          transform: () => ''
        }
        console.log(`[ContentScript] 为缺失字段 ${field.name} 创建默认映射`)
      }
    }

    console.log('[ContentScript] 字段映射重建完成:', Object.keys(reconstructedMapping))
    return reconstructedMapping
  }

  private async handleClearFormFields(formIndex: number): Promise<any> {
    if (formIndex < 0 || formIndex >= this.detectedForms.length) {
      throw new Error('表单索引无效')
    }

    const form = this.detectedForms[formIndex]
    
    try {
      console.log('[ContentFormDetector] 开始清空表单字段:', form.element)
      
      const clearedFields: string[] = []
      const errors: string[] = []

      // 遍历表单中的所有字段并清空
      form.fields.forEach((field) => {
        try {
          const element = field.element as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
          
          if (!element || !element.isConnected) {
            errors.push(`字段 ${field.name} 元素不存在或已移除`)
            return
          }

          // 根据字段类型清空内容
          switch (field.type) {
            case 'text':
            case 'email':
            case 'url':
            case 'tel':
            case 'password':
            case 'search':
            case 'textarea':
              (element as HTMLInputElement | HTMLTextAreaElement).value = ''
              // 触发 input 和 change 事件
              element.dispatchEvent(new Event('input', { bubbles: true }))
              element.dispatchEvent(new Event('change', { bubbles: true }))
              clearedFields.push(field.name || field.label || '未知字段')
              break
              
            case 'select':
              const selectElement = element as HTMLSelectElement
              selectElement.selectedIndex = -1
              // 如果有默认选项，选择第一个
              if (selectElement.options.length > 0) {
                selectElement.selectedIndex = 0
              }
              selectElement.dispatchEvent(new Event('change', { bubbles: true }))
              clearedFields.push(field.name || field.label || '未知字段')
              break
              
            case 'checkbox':
            case 'radio':
              (element as HTMLInputElement).checked = false
              element.dispatchEvent(new Event('change', { bubbles: true }))
              clearedFields.push(field.name || field.label || '未知字段')
              break
              
            default:
              // 对于其他类型，尝试清空 value
              if ('value' in element) {
                (element as any).value = ''
                element.dispatchEvent(new Event('input', { bubbles: true }))
                element.dispatchEvent(new Event('change', { bubbles: true }))
                clearedFields.push(field.name || field.label || '未知字段')
              }
          }
        } catch (fieldError) {
          console.error(`清空字段 ${field.name} 失败:`, fieldError)
          errors.push(`清空字段 ${field.name} 失败: ${fieldError instanceof Error ? fieldError.message : '未知错误'}`)
        }
      })

      console.log('[ContentFormDetector] 表单字段清空完成:', {
        clearedFields: clearedFields.length,
        errors: errors.length
      })

      return {
        success: errors.length === 0,
        clearedFields,
        errors,
        totalFields: form.fields.length
      }
    } catch (error) {
      console.error('清空表单字段失败:', error)
      throw error
    }
  }

  private highlightElement(element: HTMLElement, type: 'form' | 'field' | 'submit'): void {
    // 创建高亮样式
    const originalStyle = element.style.cssText
    element.setAttribute('data-original-style', originalStyle)

    let highlightStyle = ''
    switch (type) {
      case 'form':
        highlightStyle = `
          outline: 3px solid #3b82f6 !important;
          outline-offset: 2px !important;
          background-color: rgba(59, 130, 246, 0.1) !important;
        `
        break
      case 'field':
        highlightStyle = `
          outline: 2px solid #10b981 !important;
          outline-offset: 1px !important;
          background-color: rgba(16, 185, 129, 0.1) !important;
        `
        break
      case 'submit':
        highlightStyle = `
          outline: 2px solid #f59e0b !important;
          outline-offset: 1px !important;
          background-color: rgba(245, 158, 11, 0.1) !important;
        `
        break
    }

    element.style.cssText += highlightStyle
    element.setAttribute('data-linktrackpro-highlight', type)
    this.highlightedElements.add(element)

    // 添加点击事件监听
    element.addEventListener('click', this.handleHighlightClick, { once: true })
  }

  private handleHighlightClick = (event: Event): void => {
    if (this.isActive) {
      event.preventDefault()
      event.stopPropagation()
      
      const element = event.target as HTMLElement
      const type = element.getAttribute('data-linktrackpro-highlight')
      
      // 发送点击事件到侧边栏
      this.sendMessageToSidebar({
        type: 'FORM_ELEMENT_CLICKED',
        elementType: type,
        elementInfo: {
          tagName: element.tagName,
          name: (element as HTMLInputElement).name || element.id,
          type: (element as HTMLInputElement).type
        }
      })
    }
  }

  private clearHighlights(): void {
    this.highlightedElements.forEach(element => {
      // 恢复原始样式
      const originalStyle = element.getAttribute('data-original-style')
      if (originalStyle !== null) {
        element.style.cssText = originalStyle
        element.removeAttribute('data-original-style')
      }
      
      element.removeAttribute('data-linktrackpro-highlight')
    })
    
    this.highlightedElements.clear()
  }

  private showFillSuccess(form: HTMLFormElement): void {
    // 创建成功提示
    const successBadge = document.createElement('div')
    successBadge.style.cssText = `
      position: absolute;
      top: -10px;
      right: -10px;
      background: #10b981;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    `
    successBadge.textContent = '✓ 填充完成'

    // 添加到表单
    form.style.position = form.style.position || 'relative'
    form.appendChild(successBadge)

    // 3秒后移除
    setTimeout(() => {
      if (successBadge.parentNode) {
        successBadge.parentNode.removeChild(successBadge)
      }
    }, 3000)
  }

  // 页面变化监听
  private observePageChanges(): void {
    const observer = new MutationObserver((mutations) => {
      let shouldRedetect = false

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (element.tagName === 'FORM' || element.querySelector('form')) {
                shouldRedetect = true
              }
            }
          })
        }
      })

      if (shouldRedetect) {
        // 通知侧边栏页面结构发生变化
        this.sendMessageToSidebar({
          type: 'PAGE_STRUCTURE_CHANGED'
        })
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }
}

// 初始化内容脚本
const contentDetector = new ContentFormDetector()

// 页面加载完成后开始监听变化
if (document.readyState === 'complete') {
  contentDetector['observePageChanges']()
} else {
  window.addEventListener('load', () => {
    contentDetector['observePageChanges']()
  })
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  contentDetector['cleanup']()
})

// 当页面变为不可见时也进行清理
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'hidden') {
    // 延迟清理，给扩展一些时间处理pending操作
    setTimeout(() => {
      if (document.visibilityState === 'hidden') {
        contentDetector['cleanup']()
      }
    }, 1000)
  }
})

// 导出空组件（Plasmo要求）
export default function FormDetectorContent() {
  return null
}