// 背景脚本：处理扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // 直接打开侧边栏
    if (tab.id) {
      await chrome.sidePanel.open({ tabId: tab.id })
    }
  } catch (error) {
    console.error('打开侧边栏失败:', error)
    
    // 如果侧边栏打开失败，作为备选方案在新标签页中打开
    try {
      await chrome.tabs.create({ 
        url: chrome.runtime.getURL('sidepanel.html'),
        active: true
      })
    } catch (fallbackError) {
      console.error('备选方案也失败:', fallbackError)
    }
  }
})

// 可选：监听扩展安装事件，进行初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('外链提交助手已安装')
})