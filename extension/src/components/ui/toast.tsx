import React, { useState, useEffect } from 'react'
import { cn } from '~/lib/utils'
import { Button } from './button'
import type { Toast } from '~/lib/error-handler'

interface ToastProps {
  toast: Toast
  onClose: (id: string) => void
}

export function ToastComponent({ toast, onClose }: ToastProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    // 进入动画
    const enterTimer = setTimeout(() => setIsVisible(true), 10)

    // 自动关闭
    let autoCloseTimer: NodeJS.Timeout
    if (toast.duration && toast.duration > 0) {
      autoCloseTimer = setTimeout(() => {
        handleClose()
      }, toast.duration)
    }

    return () => {
      clearTimeout(enterTimer)
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer)
      }
    }
  }, [toast.duration])

  const handleClose = () => {
    setIsLeaving(true)
    setTimeout(() => {
      onClose(toast.id)
    }, 300) // 等待退出动画完成
  }

  const getToastStyles = () => {
    const baseStyles = 'relative flex items-start gap-3 p-4 rounded-lg shadow-lg border transition-all duration-300 transform'
    
    const visibilityStyles = isVisible && !isLeaving 
      ? 'translate-x-0 opacity-100' 
      : 'translate-x-full opacity-0'

    const typeStyles = {
      success: 'bg-green-50 border-green-200 text-green-800',
      error: 'bg-red-50 border-red-200 text-red-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      info: 'bg-blue-50 border-blue-200 text-blue-800'
    }

    return cn(baseStyles, visibilityStyles, typeStyles[toast.type])
  }

  const getIcon = () => {
    const iconClass = 'w-5 h-5 flex-shrink-0 mt-0.5'
    
    switch (toast.type) {
      case 'success':
        return (
          <svg className={cn(iconClass, 'text-green-500')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case 'error':
        return (
          <svg className={cn(iconClass, 'text-red-500')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
      case 'warning':
        return (
          <svg className={cn(iconClass, 'text-yellow-500')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      case 'info':
        return (
          <svg className={cn(iconClass, 'text-blue-500')} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  return (
    <div className={getToastStyles()}>
      {getIcon()}
      
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm">
          {toast.title}
        </div>
        
        {toast.message && (
          <div className="text-sm opacity-90 mt-1">
            {toast.message}
          </div>
        )}
        
        {toast.actions && toast.actions.length > 0 && (
          <div className="flex gap-2 mt-3">
            {toast.actions.map((action, index) => (
              <Button
                key={index}
                size="sm"
                variant="outline"
                onClick={() => {
                  action.action()
                  handleClose()
                }}
                className="h-7 text-xs"
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      <Button
        onClick={handleClose}
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </Button>
    </div>
  )
}

interface ToastContainerProps {
  children: React.ReactNode
}

export function ToastContainer({ children }: ToastContainerProps) {
  const [toasts, setToasts] = useState<Toast[]>([])

  useEffect(() => {
    // 这里应该从 ErrorHandler 监听 toast 事件
    // 由于我们在组件中，这里只是示例结构
    // 实际的 toast 管理将在 store 中处理
  }, [])

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  return (
    <>
      {children}
      
      {/* Toast 容器 */}
      <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
        {toasts.map(toast => (
          <ToastComponent
            key={toast.id}
            toast={toast}
            onClose={removeToast}
          />
        ))}
      </div>
    </>
  )
}