import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Checkbox } from '~/components/ui/checkbox'
import { useAppStore } from '~/store'
import { SettingsManager } from '~/services/managers/settings-manager'
import { getDefaultApiUrl } from '~/lib/config'

interface SettingsFormProps {
  onClose?: () => void
}

export function SettingsForm({ onClose }: SettingsFormProps) {
  const { settings, updateSettings } = useAppStore()
  const [settingsManager] = useState(() => new SettingsManager())
  
  const [apiKey, setApiKey] = useState('')
  const [apiUrl, setApiUrl] = useState(getDefaultApiUrl())
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<'success' | 'error' | null>(null)
  const [error, setError] = useState('')
  
  const [localSettings, setLocalSettings] = useState(settings)
  const [isSaving, setIsSaving] = useState(false)

  // 导入/导出相关状态
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)

  useEffect(() => {
    loadApiConfig()
  }, [])

  const loadApiConfig = async () => {
    try {
      const config = await settingsManager.getApiConfig()
      if (config) {
        setApiKey(config.apiKey || '')
        setApiUrl(config.apiUrl || getDefaultApiUrl())
      }
    } catch (error) {
      console.error('加载API配置失败:', error)
    }
  }

  const handleValidateApiKey = async () => {
    if (!(apiKey || '').trim()) {
      setError('请输入API Key')
      return
    }

    // 检查API Key格式
    if (!/^[\x00-\x7F]*$/.test(apiKey)) {
      setError('API Key包含无效字符，请确保只包含英文字母、数字和标准符号')
      return
    }

    setIsValidating(true)
    setError('')
    setValidationResult(null)

    console.log('[LinkTrackPro] 开始验证API Key...')

    try {
      const isValid = await settingsManager.validateApiKey(apiKey, apiUrl)
      setValidationResult(isValid ? 'success' : 'error')
      
      if (!isValid) {
        const errorMessage = `API Key验证失败。请检查以下几点：
1. API Key是否正确（只能包含ASCII字符）
2. API服务器是否运行在 ${apiUrl}
3. 网络连接是否正常
4. 扩展是否有访问权限
5. 浏览器控制台查看详细错误信息

💡 提示：如果使用本地开发服务器，请确保：
- 扩展已重新加载（权限已更新）
- API服务器正确响应 ${apiUrl}/extension/auth 端点
- API Key不包含中文字符或特殊字符`
        setError(errorMessage)
        console.error('[LinkTrackPro] API Key验证失败')
      } else {
        console.log('[LinkTrackPro] API Key验证成功')
      }
    } catch (error) {
      console.error('[LinkTrackPro] 验证过程中出现错误:', error)
      setValidationResult('error')
      setError(`验证过程中出现错误: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsValidating(false)
    }
  }

  const handleSaveApiKey = async () => {
    if (!(apiKey || '').trim()) {
      setError('请输入API Key')
      return
    }

    setIsValidating(true)
    setError('')

    try {
      const success = await settingsManager.setApiKey({
        apiKey: (apiKey || '').trim(),
        apiUrl: (apiUrl || '').trim()
      })

      if (success) {
        setValidationResult('success')
        // 刷新应用状态
        await updateSettings({ isPaidUser: true, aiOptimizationEnabled: true })
      } else {
        setValidationResult('error')
        setError('保存API Key失败')
      }
    } catch (error) {
      setValidationResult('error')
      setError('保存过程中出现错误')
    } finally {
      setIsValidating(false)
    }
  }

  const handleRemoveApiKey = async () => {
    if (!confirm('确定要移除API Key配置吗？这将切换到本地存储模式。')) {
      return
    }

    try {
      await settingsManager.removeApiKey()
      setApiKey('')
      setValidationResult(null)
      setError('')
      
      // 刷新应用状态
      await updateSettings({ isPaidUser: false, aiOptimizationEnabled: false })
    } catch (error) {
      setError('移除API Key失败')
    }
  }

  const handleSaveSettings = async () => {
    setIsSaving(true)
    setError('')

    try {
      await updateSettings(localSettings)
      onClose?.()
    } catch (error) {
      setError('保存设置失败')
    } finally {
      setIsSaving(false)
    }
  }

  const handleExportData = async () => {
    setIsExporting(true)
    try {
      const data = await settingsManager.exportData()
      
      // 创建下载链接
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `linktrackpro-backup-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      setError('导出数据失败')
    } finally {
      setIsExporting(false)
    }
  }

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    setError('')

    try {
      const text = await file.text()
      const data = JSON.parse(text)
      
      if (!confirm('导入数据将覆盖现有数据，确定继续吗？')) {
        return
      }

      await settingsManager.importData(data)
      alert('数据导入成功！')
      
      // 重新加载应用状态
      window.location.reload()
    } catch (error) {
      setError('导入数据失败：' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setIsImporting(false)
      // 清空文件输入
      event.target.value = ''
    }
  }

  return (
    <div className="space-y-4 max-h-[75vh] overflow-y-auto px-1">
      {/* API 配置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">API 配置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          <div>
            <label className="text-sm font-medium">API URL</label>
            <Input
              value={apiUrl}
              onChange={(e) => setApiUrl(e.target.value)}
              placeholder="https://api.linktrackpro.com"
              className="mt-1"
            />
          </div>

          <div>
            <label className="text-sm font-medium">API Key</label>
            <div className="flex gap-2 mt-1">
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => {
                  const value = e.target.value
                  // 实时检查字符格式
                  if (!/^[\x00-\x7F]*$/.test(value)) {
                    setError('API Key只能包含ASCII字符（英文字母、数字、标准符号）')
                  } else {
                    setError('')
                  }
                  setApiKey(value)
                }}
                placeholder="输入您的API Key（仅支持ASCII字符）"
                className="flex-1"
              />
              <Button
                onClick={handleValidateApiKey}
                disabled={isValidating}
                variant="outline"
                size="sm"
              >
                {isValidating ? '验证中...' : '验证'}
              </Button>
            </div>
            
            {validationResult === 'success' && (
              <div className="text-sm text-green-600 mt-1">✓ API Key验证成功</div>
            )}
            
            {validationResult === 'error' && (
              <div className="text-sm text-red-600 mt-1">✗ API Key验证失败</div>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleSaveApiKey}
              disabled={isValidating || !(apiKey || '').trim()}
              size="sm"
            >
              {isValidating ? '保存中...' : '保存API Key'}
            </Button>
            
            {settings.isPaidUser && (
              <Button
                onClick={handleRemoveApiKey}
                variant="outline"
                size="sm"
              >
                移除配置
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 基础设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">基础设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="autoOpenSidebar"
                checked={localSettings.autoOpenSidebar}
                onCheckedChange={(checked) =>
                  setLocalSettings(prev => ({ ...prev, autoOpenSidebar: !!checked }))
                }
              />
              <label htmlFor="autoOpenSidebar" className="text-sm">
                自动打开侧边栏
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="aiOptimization"
                checked={localSettings.aiOptimizationEnabled}
                onCheckedChange={(checked) =>
                  setLocalSettings(prev => ({ ...prev, aiOptimizationEnabled: !!checked }))
                }
                disabled={!settings.isPaidUser}
              />
              <label htmlFor="aiOptimization" className="text-sm">
                启用AI优化 {!settings.isPaidUser && '(需要API Key)'}
              </label>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">主题</label>
            <select
              value={localSettings.theme}
              onChange={(e) => setLocalSettings(prev => ({ 
                ...prev, 
                theme: e.target.value as 'light' | 'dark' | 'system' 
              }))}
              className="w-full mt-1 px-3 py-2 border rounded-md"
            >
              <option value="system">跟随系统</option>
              <option value="light">浅色</option>
              <option value="dark">深色</option>
            </select>
          </div>

          <div>
            <label className="text-sm font-medium">语言</label>
            <select
              value={localSettings.language}
              onChange={(e) => setLocalSettings(prev => ({ ...prev, language: e.target.value }))}
              className="w-full mt-1 px-3 py-2 border rounded-md"
            >
              <option value="zh-CN">简体中文</option>
              <option value="en-US">English</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* 数据管理 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">数据管理</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 pt-0">
          <div className="flex gap-2">
            <Button
              onClick={handleExportData}
              disabled={isExporting}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              {isExporting ? '导出中...' : '导出数据'}
            </Button>

            <label className="flex-1">
              <Button
                disabled={isImporting}
                variant="outline"
                size="sm"
                className="w-full"
                asChild
              >
                <span>{isImporting ? '导入中...' : '导入数据'}</span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImportData}
                className="hidden"
              />
            </label>
          </div>

          <div className="text-xs text-muted-foreground">
            导出的数据包含所有项目、外链和设置信息
          </div>
        </CardContent>
      </Card>

      {/* 错误信息 */}
      {error && (
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
          <pre className="whitespace-pre-wrap font-inherit">{error}</pre>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end gap-2 pt-2 sticky bottom-0 bg-background border-t mt-4">
        <div className="flex gap-2 py-3">
          <Button onClick={onClose} variant="outline" size="sm">
            取消
          </Button>
          <Button
            onClick={handleSaveSettings}
            disabled={isSaving}
            size="sm"
          >
            {isSaving ? '保存中...' : '保存设置'}
          </Button>
        </div>
      </div>
    </div>
  )
}