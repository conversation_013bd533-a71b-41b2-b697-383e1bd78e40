import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import type { Project } from '~/types'

interface ProjectFormProps {
  isOpen: boolean
  onClose: () => void
  project?: Project | null
}

export function ProjectForm({ isOpen, onClose, project }: ProjectFormProps) {
  const { addProject, updateProject, optimizeContent, aiOptimizing } = useAppStore()
  
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    description: '',
    shortDescription: '',
    category: '',
    tags: [] as string[],
    screenshots: [] as string[]
  })
  
  const [tagInput, setTagInput] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        url: project.url || '',
        description: project.description || '',
        shortDescription: project.shortDescription || '',
        category: project.category || '',
        tags: project.tags ? [...project.tags] : [],
        screenshots: project.screenshots ? [...project.screenshots] : []
      })
    } else {
      setFormData({
        name: '',
        url: '',
        description: '',
        shortDescription: '',
        category: '',
        tags: [],
        screenshots: []
      })
    }
    setErrors({})
    setTagInput('')
  }, [project, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    const name = formData.name || ''
    const url = formData.url || ''
    const description = formData.description || ''
    const shortDescription = formData.shortDescription || ''

    if (!name.trim()) {
      newErrors.name = '项目名称不能为空'
    } else if (name.length > 100) {
      newErrors.name = '项目名称不能超过100个字符'
    }

    if (!url.trim()) {
      newErrors.url = '项目URL不能为空'
    } else {
      try {
        new URL(url)
      } catch {
        newErrors.url = 'URL格式不正确'
      }
    }

    if (!description.trim()) {
      newErrors.description = '项目描述不能为空'
    } else if (description.length > 5000) {
      newErrors.description = '项目描述不能超过5000个字符'
    }

    if (shortDescription.length > 200) {
      newErrors.shortDescription = '项目简介不能超过200个字符'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    
    try {
      if (project) {
        await updateProject(project.id, formData)
      } else {
        await addProject(formData)
      }
      onClose()
    } catch (error) {
      // 错误已在store中处理
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase()
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 20) {
      setFormData(prev => ({ ...prev, tags: [...prev.tags, tag] }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleOptimizeDescription = async () => {
    const description = formData.description || ''
    if (!description.trim()) {
      return
    }

    try {
      const optimized = await optimizeContent(description, {
        fieldType: 'description',
        maxLength: 500,
        tone: 'professional'
      })
      
      setFormData(prev => ({ ...prev, description: optimized }))
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {project ? '编辑项目' : '添加项目'}
          </DialogTitle>
          <DialogDescription>
            {project ? '更新项目信息' : '添加新的项目到您的项目库'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                项目名称 <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="输入项目名称"
                maxLength={100}
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                项目URL <span className="text-red-500">*</span>
              </label>
              <Input
                type="url"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                placeholder="https://example.com"
              />
              {errors.url && (
                <p className="text-sm text-red-600 mt-1">{errors.url}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                项目简介
              </label>
              <Input
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                placeholder="简短描述项目（用于列表显示）"
                maxLength={200}
              />
              {errors.shortDescription && (
                <p className="text-sm text-red-600 mt-1">{errors.shortDescription}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {(formData.shortDescription || '').length}/200 字符
              </p>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  项目描述 <span className="text-red-500">*</span>
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleOptimizeDescription}
                  disabled={aiOptimizing || !(formData.description || '').trim()}
                >
                  {aiOptimizing ? (
                    <>
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      优化中...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      AI优化
                    </>
                  )}
                </Button>
              </div>
              <textarea
                className="w-full min-h-[120px] px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 resize-vertical"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="详细描述您的项目..."
                maxLength={5000}
              />
              {errors.description && (
                <p className="text-sm text-red-600 mt-1">{errors.description}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {(formData.description || '').length}/5000 字符
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                项目分类
              </label>
              <Input
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                placeholder="如：Web应用、移动应用、工具等"
              />
            </div>
          </div>

          {/* 标签管理 */}
          <div>
            <label className="block text-sm font-medium mb-2">
              项目标签
            </label>
            <div className="flex gap-2 mb-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="输入标签并按回车"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddTag()
                  }
                }}
              />
              <Button type="button" onClick={handleAddTag} variant="outline">
                添加
              </Button>
            </div>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-full text-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                ))}
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              已添加 {formData.tags.length}/20 个标签
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {project ? '更新中...' : '添加中...'}
                </>
              ) : (
                project ? '更新项目' : '添加项目'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}