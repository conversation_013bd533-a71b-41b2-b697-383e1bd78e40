import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Checkbox } from '~/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import type { ExternalLink } from '~/types'

interface LinkFormProps {
  isOpen: boolean
  onClose: () => void
  link?: ExternalLink | null
}

export function LinkForm({ isOpen, onClose, link }: LinkFormProps) {
  const { addLink, updateLink } = useAppStore()
  
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    submitUrl: '',
    isPaid: false,
    category: '',
    requirements: [] as string[],
    notes: ''
  })
  
  const [requirementInput, setRequirementInput] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (link) {
      setFormData({
        name: link.name || '',
        url: link.url || '',
        submitUrl: link.submitUrl || '',
        isPaid: link.isPaid || false,
        category: link.category || '',
        requirements: [...(link.requirements || [])],
        notes: link.notes || ''
      })
    } else {
      setFormData({
        name: '',
        url: '',
        submitUrl: '',
        isPaid: false,
        category: '',
        requirements: [],
        notes: ''
      })
    }
    setErrors({})
    setRequirementInput('')
  }, [link, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    const name = formData.name || ''
    const url = formData.url || ''
    const submitUrl = formData.submitUrl || ''

    if (!name.trim()) {
      newErrors.name = '平台名称不能为空'
    } else if (name.length > 100) {
      newErrors.name = '平台名称不能超过100个字符'
    }

    if (!url.trim()) {
      newErrors.url = '平台URL不能为空'
    } else {
      try {
        new URL(url)
      } catch {
        newErrors.url = 'URL格式不正确'
      }
    }

    if (submitUrl && submitUrl.trim()) {
      try {
        new URL(submitUrl)
      } catch {
        newErrors.submitUrl = '提交URL格式不正确'
      }
    }

    if (formData.notes.length > 1000) {
      newErrors.notes = '备注不能超过1000个字符'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    
    try {
      const submitData = {
        ...formData,
        submitUrl: (formData.submitUrl || '').trim() || undefined,
        category: (formData.category || '').trim() || undefined,
        notes: (formData.notes || '').trim() || undefined
      }

      if (link) {
        await updateLink(link.id, submitData)
      } else {
        await addLink(submitData)
      }
      onClose()
    } catch (error) {
      // 错误已在store中处理
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // 自动提取网站名称
    if (field === 'url' && typeof value === 'string' && value && !(formData.name || '').trim()) {
      try {
        const url = new URL(value)
        const hostname = url.hostname.replace(/^www\./, '')
        const parts = hostname.split('.')
        if (parts.length >= 2) {
          const siteName = parts[parts.length - 2]
          setFormData(prev => ({ 
            ...prev, 
            [field]: value,
            name: siteName.charAt(0).toUpperCase() + siteName.slice(1)
          }))
        }
      } catch {
        // URL格式不正确，忽略自动提取
      }
    }
  }

  const handleAddRequirement = () => {
    const requirement = requirementInput.trim()
    if (requirement && !formData.requirements.includes(requirement) && formData.requirements.length < 50) {
      setFormData(prev => ({ ...prev, requirements: [...prev.requirements, requirement] }))
      setRequirementInput('')
    }
  }

  const handleRemoveRequirement = (requirementToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter(req => req !== requirementToRemove)
    }))
  }

  const handleExtractFromCurrentPage = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tab.url && tab.title) {
        setFormData(prev => ({
          ...prev,
          name: prev.name || tab.title || '',
          url: prev.url || tab.url || ''
        }))
      }
    } catch (error) {
      console.error('获取当前页面信息失败:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {link ? '编辑外链平台' : '添加外链平台'}
          </DialogTitle>
          <DialogDescription>
            {link ? '更新外链平台信息' : '添加新的外链平台到您的外链库'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium">
                平台名称 <span className="text-red-500">*</span>
              </label>
              {!link && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleExtractFromCurrentPage}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                  </svg>
                  从当前页面获取
                </Button>
              )}
            </div>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="输入平台名称"
              maxLength={100}
            />
            {errors.name && (
              <p className="text-sm text-red-600 mt-1">{errors.name}</p>
            )}

            <div>
              <label className="block text-sm font-medium mb-2">
                平台URL <span className="text-red-500">*</span>
              </label>
              <Input
                type="url"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                placeholder="https://example.com"
              />
              {errors.url && (
                <p className="text-sm text-red-600 mt-1">{errors.url}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                提交页面URL
              </label>
              <Input
                type="url"
                value={formData.submitUrl}
                onChange={(e) => handleInputChange('submitUrl', e.target.value)}
                placeholder="https://example.com/submit （可选）"
              />
              {errors.submitUrl && (
                <p className="text-sm text-red-600 mt-1">{errors.submitUrl}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                如果有专门的提交页面，请填写此URL
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                平台分类
              </label>
              <Input
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                placeholder="如：产品目录、开发者社区、新闻媒体等"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isPaid"
                checked={formData.isPaid}
                onCheckedChange={(checked) => handleInputChange('isPaid', !!checked)}
              />
              <label htmlFor="isPaid" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                付费平台
              </label>
            </div>
          </div>

          {/* 提交要求 */}
          <div>
            <label className="block text-sm font-medium mb-2">
              提交要求
            </label>
            <div className="flex gap-2 mb-2">
              <Input
                value={requirementInput}
                onChange={(e) => setRequirementInput(e.target.value)}
                placeholder="输入提交要求并按回车"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddRequirement()
                  }
                }}
              />
              <Button type="button" onClick={handleAddRequirement} variant="outline">
                添加
              </Button>
            </div>
            {formData.requirements.length > 0 && (
              <div className="space-y-2">
                {formData.requirements.map((requirement, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-secondary rounded text-sm"
                  >
                    <span>{requirement}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveRequirement(requirement)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              已添加 {formData.requirements.length}/50 项要求
            </p>
          </div>

          {/* 备注 */}
          <div>
            <label className="block text-sm font-medium mb-2">
              备注
            </label>
            <textarea
              className="w-full min-h-[80px] px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 resize-vertical"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="添加备注信息..."
              maxLength={1000}
            />
            {errors.notes && (
              <p className="text-sm text-red-600 mt-1">{errors.notes}</p>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              {formData.notes.length}/1000 字符
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {link ? '更新中...' : '添加中...'}
                </>
              ) : (
                link ? '更新平台' : '添加平台'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}