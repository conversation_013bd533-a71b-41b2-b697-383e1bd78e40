import React, { useState } from 'react'
import { Card, CardContent } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Skeleton } from '~/components/ui/skeleton'
import type { PageScanResult, SubmitLink } from '~/types'
import { submitLinkScanner } from '~/services/page-scanner'

interface PageScannerProps {
  onLinksFound?: (newLinks: SubmitLink[]) => void
  onLinksSaved?: (savedLinks: any[]) => void
  className?: string
}

export function PageScanner({ 
  onLinksFound, 
  onLinksSaved,
  className = '' 
}: PageScannerProps) {
  const [scanResult, setScanResult] = useState<PageScanResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [savedLinks, setSavedLinks] = useState<Set<string>>(new Set())

  const handleScan = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await submitLinkScanner.scanCurrentPage()
      setScanResult(result)
      
      if (result && result.newLinksNotInDatabase.length > 0 && onLinksFound) {
        onLinksFound(result.newLinksNotInDatabase)
      }
    } catch (err) {
      console.error('Failed to scan page:', err)
      setError('页面扫描失败，请检查页面权限')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveLinks = async (links: SubmitLink[]) => {
    setSaving(true)
    setError(null)
    
    try {
      const saved = await submitLinkScanner.saveNewSubmitLinks(links)
      
      // 更新已保存链接的状态
      const newSavedUrls = new Set(savedLinks)
      links.forEach(link => newSavedUrls.add(link.url))
      setSavedLinks(newSavedUrls)
      
      if (onLinksSaved) {
        onLinksSaved(saved)
      }
      
      // 更新扫描结果，移除已保存的链接
      if (scanResult) {
        setScanResult({
          ...scanResult,
          newLinksNotInDatabase: scanResult.newLinksNotInDatabase.filter(
            link => !newSavedUrls.has(link.url)
          )
        })
      }
    } catch (err) {
      console.error('Failed to save links:', err)
      setError('保存链接失败，请稍后重试')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveSingleLink = (link: SubmitLink) => {
    handleSaveLinks([link])
  }

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  const getLinkTypeText = (type: SubmitLink['type']): string => {
    const typeMap: Record<SubmitLink['type'], string> = {
      'submit': '提交',
      'form': '表单',
      'signup': '注册',
      'contact': '联系',
      'apply': '申请'
    }
    return typeMap[type] || type
  }

  return (
    <Card className={className}>
      <CardContent className="py-4">
        <div className="space-y-4">
          {/* 标题栏 */}
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">页面扫描</h3>
            <Button 
              onClick={handleScan}
              disabled={loading}
              size="sm"
              className="h-8"
            >
              {loading ? (
                <>
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                  扫描中...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  扫描页面
                </>
              )}
            </Button>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}

          {/* 加载状态 */}
          {loading && (
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          )}

          {/* 扫描结果 */}
          {scanResult && !loading && (
            <div className="space-y-4">
              {/* 统计信息 */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <div className="text-muted-foreground">发现链接</div>
                  <div className="font-semibold">{scanResult.totalLinksFound}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">新链接</div>
                  <div className="font-semibold text-green-600">
                    {scanResult.newLinksNotInDatabase.length}
                  </div>
                </div>
              </div>

              {/* 新发现的链接 */}
              {scanResult.newLinksNotInDatabase.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">新发现的提交链接</h4>
                    <Button
                      onClick={() => handleSaveLinks(scanResult.newLinksNotInDatabase)}
                      disabled={saving}
                      size="sm"
                      variant="outline"
                      className="h-7 text-xs"
                    >
                      {saving ? '保存中...' : '全部保存'}
                    </Button>
                  </div>
                  
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {scanResult.newLinksNotInDatabase.map((link, index) => (
                      <div 
                        key={`${link.url}-${index}`} 
                        className="border rounded p-3 space-y-2"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                              {link.text || '无标题'}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {link.url}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2 ml-2">
                            <span className={`text-xs font-medium ${getConfidenceColor(link.confidence)}`}>
                              {getConfidenceText(link.confidence)}
                            </span>
                            <Button
                              onClick={() => handleSaveSingleLink(link)}
                              disabled={saving || savedLinks.has(link.url)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                            >
                              {savedLinks.has(link.url) ? (
                                <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                              )}
                            </Button>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 flex-wrap">
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded">
                            {getLinkTypeText(link.type)}
                          </span>
                          <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                            {link.isInternalLink ? '内部链接' : '外部链接'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            置信度: {Math.round(link.confidence * 100)}%
                          </span>
                        </div>
                        
                        {link.context && (
                          <div className="text-xs text-muted-foreground">
                            上下文: {link.context}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 所有链接摘要 */}
              {scanResult.submitLinks.length > scanResult.newLinksNotInDatabase.length && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">
                    已存在的链接 ({scanResult.submitLinks.length - scanResult.newLinksNotInDatabase.length})
                  </h4>
                  <div className="text-xs text-muted-foreground">
                    这些链接已在您的外链库中，无需重复添加
                  </div>
                </div>
              )}

              {/* 扫描时间 */}
              <div className="text-xs text-muted-foreground">
                扫描时间: {scanResult.scannedAt.toLocaleString()}
              </div>
            </div>
          )}

          {/* 空状态 */}
          {!scanResult && !loading && !error && (
            <div className="text-center text-muted-foreground text-sm py-8">
              点击"扫描页面"开始查找提交相关的链接
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default PageScanner