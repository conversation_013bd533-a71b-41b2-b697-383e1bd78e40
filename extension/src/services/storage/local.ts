import { Storage } from '@plasmohq/storage'
import type { 
  Project, 
  CreateProjectDto, 
  ExternalLink, 
  CreateLinkDto, 
  UserSettings 
} from '~/types'
import { BaseStorageService } from './base'

export class LocalStorageService extends BaseStorageService {
  protected storageType = 'local' as const
  private storage: Storage

  constructor() {
    super()
    this.storage = new Storage({
      area: 'local'
    })
  }

  // 项目操作
  async getProjects(): Promise<Project[]> {
    try {
      const projects = await this.storage.get('projects') || []
      return projects.map((p: any) => this.deserializeProject(p))
    } catch (error) {
      console.error('获取项目列表失败:', error)
      return []
    }
  }

  async addProject(project: CreateProjectDto): Promise<Project> {
    this.validateProject(project)

    const newProject: Project = {
      ...project,
      id: this.generateId(),
      screenshots: project.screenshots || [],
      tags: project.tags || [],
      createdAt: this.createTimestamp(),
      updatedAt: this.createTimestamp()
    }

    const projects = await this.getProjects()
    const updatedProjects = [...projects, newProject]
    
    await this.storage.set('projects', updatedProjects.map(p => this.serializeProject(p)))
    
    return newProject
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    const projects = await this.getProjects()
    const projectIndex = projects.findIndex(p => p.id === id)
    
    if (projectIndex === -1) {
      throw new Error('项目不存在')
    }

    const updatedProject: Project = {
      ...projects[projectIndex],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: this.createTimestamp()
    }

    projects[projectIndex] = updatedProject
    await this.storage.set('projects', projects.map(p => this.serializeProject(p)))
    
    return updatedProject
  }

  async deleteProject(id: string): Promise<void> {
    const projects = await this.getProjects()
    const filteredProjects = projects.filter(p => p.id !== id)
    
    if (filteredProjects.length === projects.length) {
      throw new Error('项目不存在')
    }

    await this.storage.set('projects', filteredProjects.map(p => this.serializeProject(p)))
  }

  // 外链操作
  async getLinks(): Promise<ExternalLink[]> {
    try {
      const links = await this.storage.get('links') || []
      return links.map((l: any) => this.deserializeLink(l))
    } catch (error) {
      console.error('获取外链列表失败:', error)
      return []
    }
  }

  async addLink(link: CreateLinkDto): Promise<ExternalLink> {
    this.validateLink(link)

    const newLink: ExternalLink = {
      ...link,
      id: this.generateId(),
      isPaid: link.isPaid ?? false,
      requirements: link.requirements || [],
      createdAt: this.createTimestamp(),
      updatedAt: this.createTimestamp()
    }

    const links = await this.getLinks()
    const updatedLinks = [...links, newLink]
    
    await this.storage.set('links', updatedLinks.map(l => this.serializeLink(l)))
    
    return newLink
  }

  async updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink> {
    const links = await this.getLinks()
    const linkIndex = links.findIndex(l => l.id === id)
    
    if (linkIndex === -1) {
      throw new Error('外链平台不存在')
    }

    const updatedLink: ExternalLink = {
      ...links[linkIndex],
      ...updates,
      id, // 确保ID不被覆盖
      updatedAt: this.createTimestamp()
    }

    links[linkIndex] = updatedLink
    await this.storage.set('links', links.map(l => this.serializeLink(l)))
    
    return updatedLink
  }

  async deleteLink(id: string): Promise<void> {
    const links = await this.getLinks()
    const filteredLinks = links.filter(l => l.id !== id)
    
    if (filteredLinks.length === links.length) {
      throw new Error('外链平台不存在')
    }

    await this.storage.set('links', filteredLinks.map(l => this.serializeLink(l)))
  }

  // 设置操作
  async getSettings(): Promise<UserSettings> {
    try {
      const settings = await this.storage.get('settings')
      return {
        isPaidUser: false,
        theme: 'system',
        language: 'zh-CN',
        autoOpenSidebar: false,
        aiOptimizationEnabled: true,
        ...settings
      }
    } catch (error) {
      console.error('获取设置失败:', error)
      return {
        isPaidUser: false,
        theme: 'system',
        language: 'zh-CN',
        autoOpenSidebar: false,
        aiOptimizationEnabled: true
      }
    }
  }

  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    const currentSettings = await this.getSettings()
    const newSettings = { ...currentSettings, ...updates }
    
    await this.storage.set('settings', newSettings)
    return newSettings
  }

  // 同步操作 (本地存储不需要同步)
  async syncWithAPI(): Promise<void> {
    // 本地存储不需要同步
    return Promise.resolve()
  }

  isOnline(): boolean {
    return navigator.onLine
  }

  // 通用键值对操作
  async get(key: string): Promise<any> {
    try {
      return await this.storage.get(key)
    } catch (error) {
      console.error(`获取键值 ${key} 失败:`, error)
      return null
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      await this.storage.set(key, value)
    } catch (error) {
      console.error(`设置键值 ${key} 失败:`, error)
      throw error
    }
  }

  // 额外的本地存储方法
  async clearAllData(): Promise<void> {
    await Promise.all([
      this.storage.remove('projects'),
      this.storage.remove('links'),
      this.storage.remove('settings')
    ])
  }

  async exportData(): Promise<{projects: Project[], links: ExternalLink[], settings: UserSettings}> {
    const [projects, links, settings] = await Promise.all([
      this.getProjects(),
      this.getLinks(),
      this.getSettings()
    ])

    return { projects, links, settings }
  }

  async importData(data: {projects?: Project[], links?: ExternalLink[], settings?: UserSettings}): Promise<void> {
    if (data.projects) {
      await this.storage.set('projects', data.projects.map(p => this.serializeProject(p)))
    }
    if (data.links) {
      await this.storage.set('links', data.links.map(l => this.serializeLink(l)))
    }
    if (data.settings) {
      await this.storage.set('settings', data.settings)
    }
  }
}