import type { 
  Project, 
  CreateProjectDto, 
  ExternalLink, 
  CreateLinkDto, 
  UserSettings 
} from '~/types'
import { BaseStorageService } from './base'
import { getAppConfig, formatApiUrl, validateApiUrl } from '~/lib/config'
import { Storage } from '@plasmohq/storage'

interface APIConfig {
  baseUrl: string
  apiKey: string
  timeout: number
}

export class APIStorageService extends BaseStorageService {
  protected storageType = 'api' as const
  private config: APIConfig
  private retryAttempts: number
  private retryDelay: number
  private localStorage: Storage

  constructor(config: APIConfig) {
    super()
    
    // 获取全局配置
    const appConfig = getAppConfig()
    
    // 验证并格式化URL
    if (!validateApiUrl(config.baseUrl)) {
      throw new Error(`无效的API URL: ${config.baseUrl}`)
    }
    
    this.config = {
      ...config,
      baseUrl: formatApiUrl(config.baseUrl),
      timeout: config.timeout || appConfig.timeout
    }
    
    this.retryAttempts = appConfig.retryAttempts
    this.retryDelay = appConfig.retryDelay
    
    // 初始化本地存储用于设置
    this.localStorage = new Storage({ area: 'local' })
  }

  // HTTP请求工具方法
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`
    
    // 确保API Key只包含ASCII字符
    const cleanApiKey = this.sanitizeApiKey(this.config.apiKey)
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${cleanApiKey}`,
      ...options.headers
    }

    let lastError: Error
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

        const response = await fetch(url, {
          ...options,
          headers,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`API请求失败 (${response.status}): ${errorText}`)
        }

        return (await response.json()) as T
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误')
        
        if (attempt < this.retryAttempts && this.shouldRetry(error)) {
          await this.delay(this.retryDelay * attempt)
          continue
        }
        
        break
      }
    }

    throw lastError!
  }

  private shouldRetry(error: any): boolean {
    // 网络错误或5xx服务器错误才重试
    return error.name === 'AbortError' || 
           error.message.includes('NetworkError') ||
           error.message.includes('5')
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 清理API Key，确保只包含ASCII字符
  private sanitizeApiKey(apiKey: string): string {
    // 移除非ASCII字符并trim
    return apiKey.replace(/[^\x00-\x7F]/g, "").trim()
  }

  // 验证API Key格式
  private validateApiKeyFormat(apiKey: string): boolean {
    // 检查是否包含非ASCII字符
    if (!/^[\x00-\x7F]*$/.test(apiKey)) {
      return false
    }
    // 检查长度和基本格式
    return apiKey.length > 0 && apiKey.trim() === apiKey
  }

  // 项目操作
  async getProjects(): Promise<Project[]> {
    try {
      const response = await this.request<{projects: any[]}>('/projects')
      return response.projects.map(p => this.deserializeProject(p))
    } catch (error) {
      console.error('获取项目列表失败:', error)
      throw error
    }
  }

  async addProject(project: CreateProjectDto): Promise<Project> {
    this.validateProject(project)

    try {
      const response = await this.request<{project: any}>('/projects', {
        method: 'POST',
        body: JSON.stringify(project)
      })
      
      return this.deserializeProject(response.project)
    } catch (error) {
      console.error('添加项目失败:', error)
      throw error
    }
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    try {
      const response = await this.request<{project: any}>(`/projects/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      return this.deserializeProject(response.project)
    } catch (error) {
      console.error('更新项目失败:', error)
      throw error
    }
  }

  async deleteProject(id: string): Promise<void> {
    try {
      await this.request(`/projects/${id}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('删除项目失败:', error)
      throw error
    }
  }

  // 外链操作
  async getLinks(): Promise<ExternalLink[]> {
    try {
      const response = await this.request<{links: any[]}>('/links')
      return response.links.map(l => this.deserializeLink(l))
    } catch (error) {
      console.error('获取外链列表失败:', error)
      throw error
    }
  }

  async addLink(link: CreateLinkDto): Promise<ExternalLink> {
    this.validateLink(link)

    try {
      const response = await this.request<{link: any}>('/links', {
        method: 'POST',
        body: JSON.stringify(link)
      })
      
      return this.deserializeLink(response.link)
    } catch (error) {
      console.error('添加外链失败:', error)
      throw error
    }
  }

  async updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink> {
    try {
      const response = await this.request<{link: any}>(`/links/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      
      return this.deserializeLink(response.link)
    } catch (error) {
      console.error('更新外链失败:', error)
      throw error
    }
  }

  async deleteLink(id: string): Promise<void> {
    try {
      await this.request(`/links/${id}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.error('删除外链失败:', error)
      throw error
    }
  }

  // 设置操作 - 使用本地存储，不发送API请求
  async getSettings(): Promise<UserSettings> {
    try {
      const settings = await this.localStorage.get('settings') as Partial<UserSettings> | null
      const defaultSettings: UserSettings = {
        isPaidUser: true, // API用户默认为付费用户
        theme: 'system',
        language: 'zh-CN',
        autoOpenSidebar: false,
        aiOptimizationEnabled: true
      }
      
      return {
        ...defaultSettings,
        ...(settings || {})
      }
    } catch (error) {
      console.error('获取设置失败:', error)
      // 返回默认设置
      return {
        isPaidUser: true, // API用户默认为付费用户
        theme: 'system',
        language: 'zh-CN',
        autoOpenSidebar: false,
        aiOptimizationEnabled: true
      }
    }
  }

  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const currentSettings = await this.getSettings()
      const newSettings = { ...currentSettings, ...updates }
      
      await this.localStorage.set('settings', newSettings)
      return newSettings
    } catch (error) {
      console.error('更新设置失败:', error)
      throw error
    }
  }

  // 同步操作
  async syncWithAPI(): Promise<void> {
    // API存储服务本身就是与API同步的，所以这里不需要额外操作
    return Promise.resolve()
  }

  isOnline(): boolean {
    return navigator.onLine
  }

  // API认证相关
  static async validateApiKey(apiKey: string, baseUrl: string): Promise<boolean> {
    try {
      console.log('[LinkTrackPro] APIStorageService.validateApiKey 开始验证...')
      
      // 验证API Key格式
      if (!/^[\x00-\x7F]*$/.test(apiKey)) {
        console.error('[LinkTrackPro] API Key包含非ASCII字符，请检查API Key格式')
        return false
      }
      
      const cleanApiKey = apiKey.replace(/[^\x00-\x7F]/g, "").trim()
      if (!cleanApiKey) {
        console.error('[LinkTrackPro] API Key为空或格式无效')
        return false
      }
      
      const validateUrl = `${baseUrl}/extension/auth`
      
      const response = await fetch(validateUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${cleanApiKey}`
        },
        body: JSON.stringify({
          action: 'validate'
        })
      })

      console.log('[LinkTrackPro] APIStorageService.validateApiKey 响应状态:', response.status)
      
      if (!response.ok) {
        const responseText = await response.text()
        console.log('[LinkTrackPro] APIStorageService.validateApiKey 失败响应:', responseText)
        return false
      }
      
      const responseData = await response.json()
      console.log('[LinkTrackPro] APIStorageService.validateApiKey 成功响应:', responseData)
      
      return !!(responseData && responseData.success)
    } catch (error) {
      console.error('[LinkTrackPro] APIStorageService.validateApiKey 失败:', error)
      return false
    }
  }

  // 获取用户信息
  async getUserInfo(): Promise<{isPaidUser: boolean, plan: string}> {
    try {
      const response = await this.request<{user: {isPaidUser: boolean, plan: string}}>('/auth/me')
      return response.user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { isPaidUser: false, plan: 'free' }
    }
  }

  // 通用键值对操作（委托给本地存储）
  async get(key: string): Promise<any> {
    try {
      return await this.localStorage.get(key)
    } catch (error) {
      console.error(`获取键值 ${key} 失败:`, error)
      return null
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      await this.localStorage.set(key, value)
    } catch (error) {
      console.error(`设置键值 ${key} 失败:`, error)
      throw error
    }
  }

  // AI优化服务
  async optimizeContent(content: string, context: any): Promise<string> {
    try {
      const response = await this.request<{optimized: string}>('/optimize', {
        method: 'POST',
        body: JSON.stringify({ content, context })
      })
      
      return response.optimized
    } catch (error) {
      console.error('内容优化失败:', error)
      throw error
    }
  }
}