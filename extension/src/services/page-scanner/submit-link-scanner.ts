import type { SubmitLink, PageScanResult, ExternalLink } from '~/types'
import { LinkManager } from '../managers/link-manager'

export class SubmitLinkScanner {
  private linkManager: LinkManager

  constructor() {
    this.linkManager = new LinkManager()
  }

  /**
   * 扫描当前页面的提交相关链接
   */
  async scanCurrentPage(): Promise<PageScanResult | null> {
    try {
      // 检查Chrome API是否可用
      if (!chrome?.tabs || !chrome?.scripting) {
        console.warn('Chrome tabs or scripting API not available')
        return this.getMockScanResult()
      }

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab?.url || !tab.id) {
        console.warn('无法获取当前页面信息，使用模拟数据')
        return this.getMockScanResult()
      }

      // 注入内容脚本来扫描页面
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: this.scanPageForSubmitLinks,
      })

      if (!results || results.length === 0 || !results[0].result) {
        console.warn('扫描脚本执行失败，使用模拟数据')
        return this.getMockScanResult()
      }

      const submitLinks = results[0].result as SubmitLink[]
      
      // 检查哪些链接不在数据库中
      const newLinks = await this.filterNewLinks(submitLinks)
      
      return {
        currentUrl: tab.url,
        submitLinks,
        totalLinksFound: submitLinks.length,
        newLinksNotInDatabase: newLinks,
        scannedAt: new Date()
      }
    } catch (error) {
      console.error('Error scanning current page:', error)
      return this.getMockScanResult()
    }
  }

  /**
   * 在页面中执行的脚本，用于查找提交相关的链接
   */
  private scanPageForSubmitLinks(): SubmitLink[] {
    // 注意：这个函数会被注入到页面中执行，所以不能使用类的私有方法
    const submitKeywords = [
      'submit', 'signup', 'sign up', 'register', 'contact', 'apply', 'join',
      '提交', '注册', '申请', '联系', '加入', '报名', '投稿', '发布'
    ]

    const formKeywords = [
      'form', 'contact', 'feedback', 'inquiry', 'application', 'submission',
      '表单', '联系', '反馈', '咨询', '申请', '提交'
    ]

    // 内联工具函数
    const isInternalLink = (href: string, origin: string): boolean => {
      try {
        const url = new URL(href)
        return url.origin === origin
      } catch {
        return true // 相对链接视为内部链接
      }
    }

    const calculateConfidence = (
      text: string, 
      title: string, 
      className: string, 
      href: string,
      submitKws: string[],
      formKws: string[]
    ): number => {
      let confidence = 0
      const allText = `${text} ${title} ${className} ${href}`.toLowerCase()

      // 检查提交关键词
      submitKws.forEach(keyword => {
        if (allText.includes(keyword.toLowerCase())) {
          confidence += 0.3
        }
      })

      // 检查表单关键词
      formKws.forEach(keyword => {
        if (allText.includes(keyword.toLowerCase())) {
          confidence += 0.2
        }
      })

      // URL路径加分
      if (href.toLowerCase().includes('/submit') || 
          href.toLowerCase().includes('/contact') ||
          href.toLowerCase().includes('/signup') ||
          href.toLowerCase().includes('/register')) {
        confidence += 0.4
      }

      // 按钮样式加分
      if (className.includes('btn') || className.includes('button')) {
        confidence += 0.1
      }

      return Math.min(confidence, 1.0)
    }

    const determineLinkType = (text: string, title: string, className: string, href: string): 'submit' | 'form' | 'signup' | 'contact' | 'apply' => {
      const allText = `${text} ${title} ${className} ${href}`.toLowerCase()

      if (allText.includes('signup') || allText.includes('sign up') || allText.includes('register') || allText.includes('注册')) {
        return 'signup'
      }
      if (allText.includes('contact') || allText.includes('联系')) {
        return 'contact'
      }
      if (allText.includes('apply') || allText.includes('申请')) {
        return 'apply'
      }
      if (allText.includes('form') || allText.includes('表单')) {
        return 'form'
      }
      return 'submit'
    }

    const getElementContext = (element: HTMLElement): string => {
      const parent = element.parentElement
      const siblings = parent ? Array.from(parent.children) : []
      const context = siblings
        .map(el => el.textContent?.trim())
        .filter(text => text && text.length > 0)
        .slice(0, 3)
        .join(' | ')
      
      return context || element.textContent?.trim() || ''
    }

    const getFormContext = (form: HTMLFormElement): string => {
      const title = form.title || ''
      const heading = form.querySelector('h1, h2, h3, h4, h5, h6')?.textContent?.trim() || ''
      const legend = form.querySelector('legend')?.textContent?.trim() || ''
      const labels = Array.from(form.querySelectorAll('label'))
        .map(label => label.textContent?.trim())
        .filter(text => text && text.length > 0)
        .slice(0, 3)
        .join(', ')

      return [title, heading, legend, labels].filter(text => text).join(' | ') || '表单'
    }

    const calculateFormConfidence = (context: string, submitKws: string[], formKws: string[]): number => {
      let confidence = 0.5 // 表单基础分数
      const contextLower = context.toLowerCase()

      submitKws.forEach(keyword => {
        if (contextLower.includes(keyword.toLowerCase())) {
          confidence += 0.2
        }
      })

      formKws.forEach(keyword => {
        if (contextLower.includes(keyword.toLowerCase())) {
          confidence += 0.1
        }
      })

      return Math.min(confidence, 1.0)
    }

    const submitLinks: any[] = []
    const processedUrls = new Set<string>()

    // 查找所有链接
    const links = document.querySelectorAll('a[href]')
    
    links.forEach((link: Element) => {
      const anchorLink = link as HTMLAnchorElement
      const href = anchorLink.href
      const text = anchorLink.textContent?.trim() || ''
      const title = anchorLink.title || ''
      const className = anchorLink.className || ''
      
      // 避免重复处理同一个URL
      if (processedUrls.has(href)) {
        return
      }
      processedUrls.add(href)

      // 检查是否为相对链接或当前域的链接
      const isInternal = isInternalLink(href, window.location.origin)
      
      // 计算匹配置信度
      const confidence = calculateConfidence(text, title, className, href, submitKeywords, formKeywords)
      
      if (confidence > 0.3) { // 置信度阈值
        const linkType = determineLinkType(text, title, className, href)
        
        submitLinks.push({
          url: href,
          text: text,
          type: linkType,
          confidence: confidence,
          context: getElementContext(anchorLink),
          isInternalLink: isInternal
        })
      }
    })

    // 查找表单元素
    const forms = document.querySelectorAll('form')
    forms.forEach((form: Element) => {
      const formElement = form as HTMLFormElement
      const action = formElement.action || window.location.href
      const method = formElement.method || 'get'
      
      if (method.toLowerCase() === 'post' && !processedUrls.has(action)) {
        processedUrls.add(action)
        
        const context = getFormContext(formElement)
        const confidence = calculateFormConfidence(context, submitKeywords, formKeywords)
        
        if (confidence > 0.5) {
          submitLinks.push({
            url: action,
            text: context,
            type: 'form',
            confidence: confidence,
            context: `Form: ${context}`,
            isInternalLink: isInternalLink(action, window.location.origin)
          })
        }
      }
    })

    // 按置信度降序排序
    return submitLinks.sort((a, b) => b.confidence - a.confidence)
  }



  /**
   * 获取模拟扫描结果（用于演示和错误处理）
   */
  private getMockScanResult(): PageScanResult {
    const mockLinks: SubmitLink[] = [
      {
        url: 'https://example.com/submit',
        text: '提交项目',
        type: 'submit',
        confidence: 0.9,
        context: '项目提交表单',
        isInternalLink: true
      },
      {
        url: 'https://example.com/contact',
        text: '联系我们',
        type: 'contact',
        confidence: 0.8,
        context: '联系表单',
        isInternalLink: true
      },
      {
        url: 'https://external.com/signup',
        text: '注册账号',
        type: 'signup',
        confidence: 0.7,
        context: '注册页面链接',
        isInternalLink: false
      }
    ]

    return {
      currentUrl: window.location?.href || 'https://example.com',
      submitLinks: mockLinks,
      totalLinksFound: mockLinks.length,
      newLinksNotInDatabase: mockLinks, // 假设都是新链接
      scannedAt: new Date()
    }
  }

  /**
   * 过滤出不在数据库中的新链接
   */
  private async filterNewLinks(submitLinks: SubmitLink[]): Promise<SubmitLink[]> {
    try {
      const existingLinks = await this.linkManager.getLinks()
      const existingUrls = new Set(existingLinks.map(link => this.normalizeUrl(link.url)))
      
      return submitLinks.filter(submitLink => 
        !existingUrls.has(this.normalizeUrl(submitLink.url))
      )
    } catch (error) {
      console.error('Error filtering new links:', error)
      // 如果无法获取现有链接，返回所有扫描到的链接
      return submitLinks
    }
  }

  /**
   * 标准化URL用于比较
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      let hostname = urlObj.hostname
      
      // 移除www前缀
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }
      
      // 统一使用https协议
      urlObj.protocol = 'https:'
      urlObj.hostname = hostname
      
      // 移除尾部斜杠
      if (urlObj.pathname.endsWith('/') && urlObj.pathname.length > 1) {
        urlObj.pathname = urlObj.pathname.slice(0, -1)
      }
      
      return urlObj.toString()
    } catch {
      return url
    }
  }

  /**
   * 保存新发现的提交链接到数据库
   */
  async saveNewSubmitLinks(newLinks: SubmitLink[]): Promise<ExternalLink[]> {
    const savedLinks: ExternalLink[] = []

    for (const submitLink of newLinks) {
      try {
        const linkData = {
          name: submitLink.text || this.extractDomainName(submitLink.url),
          url: submitLink.url,
          submitUrl: submitLink.url,
          isPaid: false,
          category: this.getCategoryFromType(submitLink.type),
          notes: `自动扫描发现 - ${submitLink.context || ''} (置信度: ${Math.round(submitLink.confidence * 100)}%)`
        }

        const savedLink = await this.linkManager.addLink(linkData)
        savedLinks.push(savedLink)
      } catch (error) {
        console.error(`Failed to save submit link ${submitLink.url}:`, error)
      }
    }

    return savedLinks
  }

  /**
   * 从URL提取域名作为默认名称
   */
  private extractDomainName(url: string): string {
    try {
      const urlObj = new URL(url)
      let hostname = urlObj.hostname

      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }

      const parts = hostname.split('.')
      if (parts.length >= 2) {
        return parts[parts.length - 2]
      }

      return hostname
    } catch {
      return '未知平台'
    }
  }

  /**
   * 根据链接类型获取分类
   */
  private getCategoryFromType(type: SubmitLink['type']): string {
    const categoryMap: Record<SubmitLink['type'], string> = {
      'submit': '提交平台',
      'form': '表单提交',
      'signup': '注册平台',
      'contact': '联系方式',
      'apply': '申请平台'
    }
    
    return categoryMap[type] || '其他'
  }
}

// 导出单例实例
export const submitLinkScanner = new SubmitLinkScanner()