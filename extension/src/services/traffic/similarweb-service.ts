import type { SimilarWebData, WebsiteTraffic, TrafficCountry, TrafficKeyword } from '~/types'

export class SimilarWebService {
  private readonly baseUrl = 'https://data.similarweb.com/api/v1/data'
  
  constructor() {}

  /**
   * 获取网站流量数据
   */
  async getWebsiteTraffic(domain: string): Promise<SimilarWebData | null> {
    try {
      const cleanDomain = this.cleanDomain(domain)
      console.log(`正在获取域名 ${cleanDomain} 的流量数据...`)
      
      const response = await fetch(`${this.baseUrl}?domain=${cleanDomain}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'LinkTrackPro-Extension/1.0'
        }
      })
      
      if (!response.ok) {
        console.warn(`API请求失败 ${domain}:`, {
          status: response.status,
          statusText: response.statusText,
          url: response.url
        })
        return null
      }
      
      const responseText = await response.text()
      console.log('API原始响应:', responseText)
      
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('JSON解析失败:', parseError, '原始响应:', responseText)
        return null
      }
      
      const transformedData = this.transformApiResponse(cleanDomain, data)
      console.log('转换后的数据:', transformedData)
      
      return transformedData
    } catch (error) {
      console.error('获取SimilarWeb数据时出错:', error)
      return null
    }
  }

  /**
   * 获取当前页面的流量数据
   */
  async getCurrentPageTraffic(): Promise<SimilarWebData | null> {
    try {
      // 检查Chrome API是否可用
      if (!chrome?.tabs) {
        console.warn('Chrome tabs API not available, using fallback')
        return this.getCurrentPageTrafficFallback()
      }

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab?.url) {
        console.warn('无法获取当前页面URL，使用备选方案')
        return this.getCurrentPageTrafficFallback()
      }
      
      const domain = this.extractDomain(tab.url)
      return this.getWebsiteTraffic(domain)
    } catch (error) {
      console.error('Error getting current page traffic:', error)
      return this.getCurrentPageTrafficFallback()
    }
  }

  /**
   * 获取当前页面流量数据的备选方案
   */
  private async getCurrentPageTrafficFallback(): Promise<SimilarWebData | null> {
    try {
      // 尝试从window.location获取域名
      if (typeof window !== 'undefined' && window.location) {
        const domain = this.extractDomain(window.location.href)
        return this.getWebsiteTraffic(domain)
      }
      
      console.warn('无法获取当前域名')
      return null
    } catch (error) {
      console.error('Fallback method failed:', error)
      return null
    }
  }

  /**
   * 转换API响应数据为内部数据格式
   */
  private transformApiResponse(domain: string, apiData: any): SimilarWebData {
    console.log('开始转换API响应数据:', JSON.stringify(apiData, null, 2))
    
    // 如果API返回了数组，尝试获取第一个元素
    let data = apiData
    if (Array.isArray(apiData) && apiData.length > 0) {
      data = apiData[0]
      console.log('检测到数组响应，使用第一个元素:', data)
    }
    
    // 如果数据被包装在特定字段中，尝试提取
    if (data.result) data = data.result
    if (data.data) data = data.data
    if (data.response) data = data.response
    
    // 支持多种可能的字段名和数据结构
    const traffic: WebsiteTraffic = {
      domain,
      totalVisits: this.extractNumber(data, [
        'visits', 'totalVisits', 'EstimatedMonthlyVisits', 'Visits', 
        'EstimateCounts.visits', 'Estimations.visits'
      ]),
      uniqueVisitors: this.extractNumber(data, [
        'unique_visitors', 'uniqueVisitors', 'UniqueVisitors',
        'EstimateCounts.unique_visitors', 'Estimations.unique_visitors'
      ]),
      pageViews: this.extractNumber(data, [
        'page_views', 'pageViews', 'PageViews', 'PagePerVisit',
        'EstimateCounts.page_views', 'Estimations.page_views'
      ]),
      bounceRate: this.extractNumber(data, [
        'bounce_rate', 'bounceRate', 'BounceRate',
        'EstimateCounts.bounce_rate', 'Estimations.bounce_rate'
      ], 100), // 可能需要转换百分比
      averageVisitDuration: this.extractNumber(data, [
        'avg_visit_duration', 'averageVisitDuration', 'AvgVisitDuration',
        'EstimateCounts.avg_visit_duration', 'Estimations.avg_visit_duration'
      ]),
      pagesPerVisit: this.extractNumber(data, [
        'pages_per_visit', 'pagesPerVisit', 'PagesPerVisit',
        'EstimateCounts.pages_per_visit', 'Estimations.pages_per_visit'
      ]),
      lastUpdated: new Date()
    }

    console.log('转换后的流量数据:', traffic)

    const topCountries: TrafficCountry[] = this.transformCountriesData(
      data.countries || data.TopCountryShares || data.Countries || 
      data.CountryBreakdown || data.country_breakdown || []
    )
    
    const topKeywords: TrafficKeyword[] = this.transformKeywordsData(
      data.keywords || data.TopKeywords || data.Keywords || 
      data.SearchKeywords || data.search_keywords || []
    )

    console.log('转换后的国家数据:', topCountries)
    console.log('转换后的关键词数据:', topKeywords)

    const result = {
      domain,
      traffic,
      topCountries,
      topKeywords,
      globalRank: this.extractNumber(data, [
        'global_rank', 'globalRank', 'GlobalRank', 'Rank.Global'
      ]),
      countryRank: this.extractNumber(data, [
        'country_rank', 'countryRank', 'CountryRank', 'Rank.Country'
      ]),
      categoryRank: this.extractNumber(data, [
        'category_rank', 'categoryRank', 'CategoryRank', 'Rank.Category'
      ]),
      category: data.category || data.Category || data.MainCategory || data.main_category
    }

    console.log('最终转换结果:', result)
    return result
  }

  /**
   * 从对象中提取数字值，支持多个可能的字段名
   */
  private extractNumber(obj: any, fieldNames: string[], divisor: number = 1): number {
    for (const fieldName of fieldNames) {
      const value = this.getNestedValue(obj, fieldName)
      if (typeof value === 'number' && !Number.isNaN(value)) {
        return value / divisor
      }
      if (typeof value === 'string') {
        const parsed = parseFloat(value.replace(/[,%]/g, ''))
        if (!Number.isNaN(parsed)) {
          return parsed / divisor
        }
      }
    }
    return 0
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 转换国家流量数据
   */
  private transformCountriesData(countries: any[]): TrafficCountry[] {
    if (!Array.isArray(countries)) return []
    
    return countries.slice(0, 5).map((country: any) => ({
      countryCode: country.code || country.country_code || country.Code || country.CountryCode || '',
      countryName: country.name || country.country_name || country.Name || country.Country || '',
      trafficShare: this.extractNumber(country, ['share', 'traffic_share', 'Share', 'Value'], 100), // 转换百分比
      visits: this.extractNumber(country, ['visits', 'Visits'])
    })).filter(country => country.countryCode || country.countryName)
  }

  /**
   * 转换关键词数据
   */
  private transformKeywordsData(keywords: any[]): TrafficKeyword[] {
    if (!Array.isArray(keywords)) return []
    
    return keywords.slice(0, 10).map((keyword: any) => ({
      keyword: keyword.keyword || keyword.term || keyword.Keyword || keyword.Term || '',
      trafficShare: this.extractNumber(keyword, ['share', 'traffic_share', 'Share', 'Value'], 100), // 转换百分比
      position: this.extractNumber(keyword, ['position', 'Position', 'rank', 'Rank']),
      volume: this.extractNumber(keyword, ['volume', 'search_volume', 'Volume', 'SearchVolume'])
    })).filter(keyword => keyword.keyword)
  }

  /**
   * 清理域名，移除协议和www前缀
   */
  private cleanDomain(url: string): string {
    try {
      // 如果不是完整URL，直接返回
      if (!url.includes('://')) {
        return url.replace(/^www\./, '')
      }
      
      const urlObj = new URL(url)
      let hostname = urlObj.hostname
      
      // 移除www前缀
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }
      
      return hostname
    } catch {
      // 如果URL解析失败，尝试简单处理
      return url.replace(/^(https?:\/\/)?(www\.)?/, '').split('/')[0]
    }
  }

  /**
   * 从URL中提取域名
   */
  private extractDomain(url: string): string {
    return this.cleanDomain(url)
  }


}

// 导出单例实例
export const similarWebService = new SimilarWebService()