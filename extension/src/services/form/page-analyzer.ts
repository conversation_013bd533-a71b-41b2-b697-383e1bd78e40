import type { 
  PageContext, 
  WebFramework, 
  DetectionEnvironment 
} from '~/types'

export class PageAnalyzer {
  static analyzePageContext(document: Document, window: Window): PageContext {
    const startTime = performance.now()
    
    const context: PageContext = {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      language: this.detectLanguage(document),
      contentType: document.contentType || 'text/html',
      hasJavaScript: this.hasJavaScriptContent(document),
      loadTime: this.getPageLoadTime(window),
      domComplexity: this.calculateDOMComplexity(document)
    }

    const endTime = performance.now()
    console.log(`[PageAnalyzer] 页面分析耗时: ${endTime - startTime}ms`)

    return context
  }

  static detectWebFramework(document: Document, window: Window): WebFramework {
    // React检测
    if (this.hasReact(window)) {
      return 'react'
    }

    // Vue检测
    if (this.hasVue(window)) {
      return 'vue'
    }

    // Angular检测
    if (this.hasAngular(window, document)) {
      return 'angular'
    }

    // Svelte检测
    if (this.hasSvelte(document)) {
      return 'svelte'
    }

    // jQuery检测
    if (this.hasjQuery(window)) {
      return 'jquery'
    }

    return 'vanilla'
  }

  private static detectLanguage(document: Document): string | undefined {
    // 从html标签获取语言
    const htmlLang = document.documentElement.lang
    if (htmlLang) return htmlLang

    // 从meta标签获取语言
    const metaLang = document.querySelector('meta[http-equiv="content-language"]')
    if (metaLang) {
      const content = metaLang.getAttribute('content')
      if (content) return content
    }

    // 从页面内容推断语言（简单实现）
    const text = document.body?.textContent || ''
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh'
    if (/[あ-ん]|[ア-ン]|[ひ-ゟ]/.test(text)) return 'ja'
    if (/[가-힣]/.test(text)) return 'ko'

    return 'en' // 默认英语
  }

  private static hasJavaScriptContent(document: Document): boolean {
    const scripts = document.querySelectorAll('script[src], script:not([src])')
    return scripts.length > 0
  }

  private static getPageLoadTime(window: Window): number {
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing
      return timing.loadEventEnd - timing.navigationStart
    }
    return 0
  }

  private static calculateDOMComplexity(document: Document): number {
    const totalElements = document.querySelectorAll('*').length
    const formElements = document.querySelectorAll('form, input, textarea, select').length
    const scriptElements = document.querySelectorAll('script').length
    const iframeElements = document.querySelectorAll('iframe').length
    
    // 复杂度计算公式
    let complexity = totalElements * 0.1
    complexity += formElements * 2 // 表单元素权重更高
    complexity += scriptElements * 1.5
    complexity += iframeElements * 3

    return Math.round(complexity)
  }

  // 框架检测方法
  private static hasReact(window: Window): boolean {
    return !!(
      window.React ||
      window.ReactDOM ||
      document.querySelector('[data-reactroot], [data-react-helmet]') ||
      document.querySelector('*[data-reactid]') ||
      (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__
    )
  }

  private static hasVue(window: Window): boolean {
    return !!(
      window.Vue ||
      document.querySelector('[data-v-]') ||
      document.querySelector('*[v-]') ||
      (window as any).__VUE__ ||
      (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__
    )
  }

  private static hasAngular(window: Window, document: Document): boolean {
    return !!(
      (window as any).angular ||
      (window as any).ng ||
      document.querySelector('[ng-app], [data-ng-app]') ||
      document.querySelector('*[ng-]') ||
      document.querySelector('app-root') ||
      (window as any).getAllAngularRootElements
    )
  }

  private static hasSvelte(document: Document): boolean {
    return !!(
      document.querySelector('*[class*="svelte-"]') ||
      document.querySelector('script[data-svelte]')
    )
  }

  private static hasjQuery(window: Window): boolean {
    return !!(window.jQuery || window.$)
  }

  // 检测平台类型
  static detectPlatform(document: Document, window: Window): string | undefined {
    const url = window.location.hostname.toLowerCase()
    const title = document.title.toLowerCase()
    const bodyClass = document.body?.className.toLowerCase() || ''

    // 常见平台检测
    const platforms = [
      { name: 'ProductHunt', patterns: ['producthunt.com', 'product hunt'] },
      { name: 'Hacker News', patterns: ['news.ycombinator.com', 'ycombinator'] },
      { name: 'Reddit', patterns: ['reddit.com', 'reddit'] },
      { name: 'Twitter', patterns: ['twitter.com', 'x.com'] },
      { name: 'GitHub', patterns: ['github.com', 'github'] },
      { name: 'IndieHackers', patterns: ['indiehackers.com', 'indie hackers'] },
      { name: 'BetaList', patterns: ['betalist.com', 'betalist'] },
      { name: 'StartupStash', patterns: ['startupstash.com'] },
      { name: 'Crunchbase', patterns: ['crunchbase.com'] },
      { name: 'AngelList', patterns: ['angel.co', 'angellist'] }
    ]

    for (const platform of platforms) {
      if (platform.patterns.some(pattern => 
        url.includes(pattern) || title.includes(pattern) || bodyClass.includes(pattern)
      )) {
        return platform.name
      }
    }

    return undefined
  }

  // 计算DOM哈希值用于缓存
  static calculateDOMHash(document: Document): string {
    const forms = Array.from(document.querySelectorAll('form'))
    const formData = forms.map(form => ({
      action: form.action || '',
      method: form.method || 'get',
      fieldCount: form.querySelectorAll('input, textarea, select').length,
      id: form.id || '',
      className: form.className || ''
    }))

    const hashSource = JSON.stringify({
      url: document.location?.href || '',
      title: document.title,
      forms: formData,
      timestamp: Math.floor(Date.now() / (1000 * 60 * 10)) // 10分钟精度
    })

    return this.simpleHash(hashSource)
  }

  private static simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  // 检测页面是否是单页应用
  static isSinglePageApp(window: Window): boolean {
    return !!(
      window.history?.pushState &&
      (this.hasReact(window) || this.hasVue(window) || this.hasAngular(window, document))
    )
  }

  // 估算表单完成时间
  static estimateFormCompletionTime(fieldCount: number, hasValidation: boolean): number {
    // 基础时间：每个字段30秒
    let baseTime = fieldCount * 30

    // 验证增加时间
    if (hasValidation) {
      baseTime *= 1.2
    }

    // 复杂度调整
    if (fieldCount > 10) {
      baseTime *= 1.3 // 复杂表单需要更多思考时间
    }

    return Math.round(baseTime)
  }
}