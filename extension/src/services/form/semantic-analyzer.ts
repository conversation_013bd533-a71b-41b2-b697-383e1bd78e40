import type { FormField, SemanticFieldType, FormFieldType } from '~/types'

interface SemanticPattern {
  type: SemanticFieldType
  patterns: RegExp[]
  weight: number
  fieldTypes: FormFieldType[]
}

export class SemanticFieldAnalyzer {
  private static readonly SEMANTIC_PATTERNS: SemanticPattern[] = [
    // 项目名称
    {
      type: 'project_name',
      patterns: [
        /\b(project|app|product|software|tool|service|startup)\s*(name|title)\b/i,
        /\b(name|title)\s*(of\s*)?(project|app|product|software|tool|service)\b/i,
        /\bapp[_-]?name\b/i,
        /\bproduct[_-]?name\b/i,
        /\bproject[_-]?title\b/i
      ],
      weight: 10,
      fieldTypes: ['text']
    },

    // 项目描述
    {
      type: 'project_description',
      patterns: [
        /\b(project|app|product|software|tool|service)\s*(description|desc|about|details?|summary|info)\b/i,
        /\b(description|desc|about|details?|summary|info)\s*(of\s*)?(project|app|product)\b/i,
        /\bwhat\s*(is|does)\s*(your|the)\s*(project|app|product)\b/i,
        /\btell\s*us\s*about\s*(your|the)\s*(project|app|product)\b/i,
        /\bproject[_-]?(description|desc|about|details|summary)\b/i
      ],
      weight: 10,
      fieldTypes: ['textarea', 'text']
    },

    // 项目URL
    {
      type: 'project_url',
      patterns: [
        /\b(project|app|product|software|tool|service|website)\s*(url|link|address)\b/i,
        /\b(url|link|address)\s*(of\s*)?(project|app|product|website)\b/i,
        /\bwebsite[_-]?url\b/i,
        /\bproject[_-]?url\b/i,
        /\bapp[_-]?url\b/i,
        /\bhomepage\b/i
      ],
      weight: 10,
      fieldTypes: ['url', 'text']
    },

    // 演示URL
    {
      type: 'demo_url',
      patterns: [
        /\bdemo[_-]?(url|link)\b/i,
        /\blive[_-]?(demo|url|link)\b/i,
        /\btry[_-]?(it|demo)\b/i,
        /\bpreview[_-]?(url|link)\b/i
      ],
      weight: 9,
      fieldTypes: ['url', 'text']
    },

    // 联系邮箱
    {
      type: 'contact_email',
      patterns: [
        /\bemail\b/i,
        /\bcontact[_-]?(email|mail)\b/i,
        /\byour[_-]?email\b/i,
        /\bemail[_-]?address\b/i
      ],
      weight: 10,
      fieldTypes: ['email', 'text']
    },

    // 公司名称
    {
      type: 'company_name',
      patterns: [
        /\bcompany[_-]?(name|title)\b/i,
        /\borganization\b/i,
        /\bstartup[_-]?name\b/i,
        /\bbusiness[_-]?name\b/i
      ],
      weight: 8,
      fieldTypes: ['text']
    },

    // 分类
    {
      type: 'category',
      patterns: [
        /\bcategor(y|ies)\b/i,
        /\btype\b/i,
        /\btag\b/i,
        /\bgenre\b/i,
        /\bclassification\b/i,
        /\bsector\b/i,
        /\bindustry\b/i
      ],
      weight: 7,
      fieldTypes: ['select', 'text']
    },

    // 标签
    {
      type: 'tags',
      patterns: [
        /\btags?\b/i,
        /\bkeywords?\b/i,
        /\blabels?\b/i,
        /\btech[_-]?stack\b/i,
        /\btechnolog(y|ies)\b/i
      ],
      weight: 8,
      fieldTypes: ['text', 'textarea']
    },

    // 简短描述
    {
      type: 'short_description',
      patterns: [
        /\bshort[_-]?(description|desc)\b/i,
        /\bbrief[_-]?(description|desc)\b/i,
        /\btagline\b/i,
        /\bsubtitle\b/i,
        /\belevator[_-]?pitch\b/i,
        /\bone[_-]?liner\b/i,
        /\bsummary\b/i
      ],
      weight: 8,
      fieldTypes: ['text', 'textarea']
    },

    // 社交媒体
    {
      type: 'social_media',
      patterns: [
        /\btwitter\b/i,
        /\bfacebook\b/i,
        /\blinkedin\b/i,
        /\binstagram\b/i,
        /\bgithub\b/i,
        /\bsocial[_-]?media\b/i,
        /\bsocial[_-]?link\b/i
      ],
      weight: 6,
      fieldTypes: ['url', 'text']
    },

    // 定价
    {
      type: 'pricing',
      patterns: [
        /\bpric(e|ing)\b/i,
        /\bcost\b/i,
        /\bfee\b/i,
        /\bsubscription\b/i,
        /\bplan\b/i
      ],
      weight: 6,
      fieldTypes: ['text', 'select', 'number']
    },

    // 团队规模
    {
      type: 'team_size',
      patterns: [
        /\bteam[_-]?size\b/i,
        /\bemployees?\b/i,
        /\bmembers?\b/i,
        /\bstaff\b/i,
        /\bpeople\b/i
      ],
      weight: 5,
      fieldTypes: ['number', 'select', 'text']
    }
  ]

  static analyzeField(field: FormField): {
    semanticType: SemanticFieldType
    confidence: number
    reasoning: string[]
  } {
    const searchText = this.buildSearchText(field)
    const matches: Array<{
      type: SemanticFieldType
      score: number
      reason: string
    }> = []

    // 遍历所有语义模式
    for (const pattern of this.SEMANTIC_PATTERNS) {
      // 检查字段类型是否匹配
      if (!pattern.fieldTypes.includes(field.type)) {
        continue
      }

      // 检查模式匹配
      for (const regex of pattern.patterns) {
        if (regex.test(searchText)) {
          const score = pattern.weight + this.calculateContextBonus(field, pattern.type)
          matches.push({
            type: pattern.type,
            score,
            reason: `匹配模式: ${regex.source}`
          })
          break // 一个模式匹配后跳出内循环
        }
      }
    }

    // 如果没有匹配，尝试基于字段特征的启发式分析
    if (matches.length === 0) {
      const heuristicResult = this.heuristicAnalysis(field)
      if (heuristicResult) {
        matches.push(heuristicResult)
      }
    }

    // 如果仍然没有匹配，返回unknown
    if (matches.length === 0) {
      return {
        semanticType: 'unknown',
        confidence: 0,
        reasoning: ['无法识别字段类型']
      }
    }

    // 选择得分最高的匹配
    matches.sort((a, b) => b.score - a.score)
    const bestMatch = matches[0]

    // 计算置信度 (0-1)
    const maxScore = 15 // 理论最高分
    const confidence = Math.min(bestMatch.score / maxScore, 1)

    return {
      semanticType: bestMatch.type,
      confidence,
      reasoning: matches.slice(0, 3).map(m => m.reason)
    }
  }

  private static buildSearchText(field: FormField): string {
    const parts = [
      field.name || '',
      field.label || '',
      field.placeholder || '',
      field.element.id || '',
      field.element.className || ''
    ]

    return parts.join(' ').toLowerCase()
  }

  private static calculateContextBonus(field: FormField, semanticType: SemanticFieldType): number {
    let bonus = 0

    // 字段类型匹配度加成
    if (semanticType === 'project_url' && field.type === 'url') bonus += 2
    if (semanticType === 'contact_email' && field.type === 'email') bonus += 2
    if (semanticType === 'project_description' && field.type === 'textarea') bonus += 2

    // 必填字段加成
    if (field.required) {
      if (['project_name', 'project_description', 'contact_email'].includes(semanticType)) {
        bonus += 1
      }
    }

    // 字段长度限制加成
    if (field.maxLength) {
      if (semanticType === 'short_description' && field.maxLength <= 200) bonus += 1
      if (semanticType === 'project_name' && field.maxLength <= 100) bonus += 1
    }

    return bonus
  }

  private static heuristicAnalysis(field: FormField): {
    type: SemanticFieldType
    score: number
    reason: string
  } | null {
    const searchText = this.buildSearchText(field)

    // 基于字段类型的启发式推断
    if (field.type === 'url') {
      if (searchText.includes('demo') || searchText.includes('live')) {
        return { type: 'demo_url', score: 4, reason: 'URL字段包含demo/live关键词' }
      }
      return { type: 'project_url', score: 3, reason: 'URL类型字段' }
    }

    if (field.type === 'email') {
      return { type: 'contact_email', score: 5, reason: 'Email类型字段' }
    }

    if (field.type === 'textarea') {
      if (field.maxLength && field.maxLength <= 300) {
        return { type: 'short_description', score: 4, reason: '短文本区域字段' }
      }
      return { type: 'project_description', score: 4, reason: '文本区域字段' }
    }

    if (field.type === 'select') {
      return { type: 'category', score: 3, reason: '选择框字段' }
    }

    // 基于字段名称的简单推断
    if (searchText.includes('name') || searchText.includes('title')) {
      return { type: 'project_name', score: 2, reason: '字段名包含name/title' }
    }

    if (searchText.includes('desc') || searchText.includes('about')) {
      return { type: 'project_description', score: 2, reason: '字段名包含desc/about' }
    }

    return null
  }

  // 批量分析表单字段
  static analyzeFormFields(fields: FormField[]): FormField[] {
    const analyzedFields = fields.map(field => {
      const analysis = this.analyzeField(field)
      
      return {
        ...field,
        semanticType: analysis.semanticType,
        confidence: analysis.confidence
      }
    })

    // 解决冲突（多个字段映射到同一语义类型）
    return this.resolveConflicts(analyzedFields)
  }

  private static resolveConflicts(fields: FormField[]): FormField[] {
    const typeGroups = new Map<SemanticFieldType, FormField[]>()

    // 按语义类型分组
    for (const field of fields) {
      if (field.semanticType && field.semanticType !== 'unknown') {
        if (!typeGroups.has(field.semanticType)) {
          typeGroups.set(field.semanticType, [])
        }
        typeGroups.get(field.semanticType)!.push(field)
      }
    }

    // 解决每个类型的冲突
    for (const [semanticType, conflictingFields] of typeGroups) {
      if (conflictingFields.length > 1) {
        // 按置信度排序，只保留置信度最高的
        conflictingFields.sort((a, b) => (b.confidence || 0) - (a.confidence || 0))
        
        // 将其他字段标记为unknown
        for (let i = 1; i < conflictingFields.length; i++) {
          conflictingFields[i].semanticType = 'unknown'
          conflictingFields[i].confidence = 0
        }
      }
    }

    return fields
  }

  // 获取字段类型的中文描述
  static getSemanticTypeLabel(type: SemanticFieldType): string {
    const labels: Record<SemanticFieldType, string> = {
      project_name: '项目名称',
      project_description: '项目描述',
      project_url: '项目URL',
      contact_email: '联系邮箱',
      company_name: '公司名称',
      category: '项目分类',
      tags: '项目标签',
      short_description: '简短描述',
      demo_url: '演示链接',
      social_media: '社交媒体',
      pricing: '定价信息',
      team_size: '团队规模',
      unknown: '未知类型'
    }

    return labels[type] || type
  }

  // 获取语义类型的重要性评分
  static getSemanticTypeImportance(type: SemanticFieldType): number {
    const importance: Record<SemanticFieldType, number> = {
      project_name: 10,
      project_description: 9,
      project_url: 8,
      contact_email: 7,
      short_description: 6,
      category: 5,
      tags: 5,
      demo_url: 4,
      company_name: 4,
      social_media: 3,
      pricing: 3,
      team_size: 2,
      unknown: 0
    }

    return importance[type] || 0
  }
}