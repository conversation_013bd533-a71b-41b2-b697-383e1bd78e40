import type { 
  FormElement, 
  FormField, 
  BaseFormDetector,
  DetectionEnvironment,
  DetectionOptions,
  FormDetectionResult,
  FormMetadata,
  DetectionContext,
  FieldContext,
  VisualProperties,
  FormCategory,
  ValidationRule,
  PerformanceMetrics,
  DetectionSummary
} from '~/types'
import { PageAnalyzer } from './page-analyzer'
import { SemanticFieldAnalyzer } from './semantic-analyzer'
import { FormDetectionCache } from './cache'

export class AdvancedFormDetector implements BaseFormDetector {
  readonly name = 'AdvancedFormDetector'
  readonly priority = 10
  readonly strategy = 'hybrid' as const

  private cache: FormDetectionCache
  private readonly SUBMISSION_KEYWORDS = [
    'submit', 'send', 'post', 'create', 'add', 'register', 'signup', 'apply',
    'contact', 'feedback', 'message', 'inquiry', 'request', 'upload', 'launch',
    'publish', 'share', 'announce', 'promote', 'showcase'
  ]

  private readonly MIN_CONFIDENCE_SCORE = 0.4
  private readonly MAX_PROCESSING_TIME = 10000 // 10秒超时

  constructor() {
    this.cache = new FormDetectionCache()
  }

  async detectForms(env: DetectionEnvironment): Promise<FormElement[]> {
    const startTime = performance.now()
    const options = env.options || {}
    
    try {
      // 检查缓存
      if (options.enableCache !== false) {
        const cacheKey = FormDetectionCache.getPageKey(env.pageContext.url)
        const cached = this.cache.get<FormElement[]>(cacheKey)
        if (cached) {
          console.log('[AdvancedFormDetector] 使用缓存结果')
          return cached
        }
      }

      // 执行检测
      const forms = await this.performDetection(env)
      
      // 缓存结果
      if (options.enableCache !== false && forms.length > 0) {
        const cacheKey = FormDetectionCache.getPageKey(env.pageContext.url)
        const ttl = 5 * 60 * 1000 // 5分钟缓存
        this.cache.set(cacheKey, forms, ttl)
      }

      const endTime = performance.now()
      console.log(`[AdvancedFormDetector] 检测完成，耗时: ${endTime - startTime}ms，发现 ${forms.length} 个表单`)

      return forms
    } catch (error) {
      console.error('[AdvancedFormDetector] 检测失败:', error)
      throw error
    }
  }

  async validateForm(form: FormElement): Promise<boolean> {
    try {
      // 基础验证
      if (!form.element || !form.element.isConnected) {
        return false
      }

      // 字段验证
      if (form.fields.length === 0) {
        return false
      }

      // 置信度验证
      if (form.confidence < this.MIN_CONFIDENCE_SCORE) {
        return false
      }

      // 检查表单是否可见
      const rect = form.element.getBoundingClientRect()
      if (rect.width === 0 || rect.height === 0) {
        return false
      }

      return true
    } catch (error) {
      console.warn('[AdvancedFormDetector] 表单验证失败:', error)
      return false
    }
  }

  getConfidence(form: FormElement): number {
    return form.confidence
  }

  private async performDetection(env: DetectionEnvironment): Promise<FormElement[]> {
    const { document, window, pageContext, options = {} } = env
    const detectedForms: FormElement[] = []

    // 设置超时保护
    const timeout = options.timeout || this.MAX_PROCESSING_TIME
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('检测超时')), timeout)
    })

    try {
      const detectionPromise = this.doDetection(document, window, pageContext, options)
      const forms = await Promise.race([detectionPromise, timeoutPromise])
      
      return forms
    } catch (error) {
      if (error instanceof Error && error.message === '检测超时') {
        console.warn('[AdvancedFormDetector] 检测超时，返回已发现的表单')
        return detectedForms
      }
      throw error
    }
  }

  private async doDetection(
    document: Document, 
    window: Window, 
    pageContext: any, 
    options: DetectionOptions
  ): Promise<FormElement[]> {
    const detectedForms: FormElement[] = []
    const performanceMetrics: PerformanceMetrics = {
      detectionTime: 0,
      elementsScanned: 0,
      cacheHitRate: 0
    }

    // 获取所有form元素
    const formElements = Array.from(document.querySelectorAll('form'))
    performanceMetrics.elementsScanned += formElements.length

    console.log(`[AdvancedFormDetector] 开始分析 ${formElements.length} 个表单`)

    for (let i = 0; i < formElements.length; i++) {
      if (options.maxForms && detectedForms.length >= options.maxForms) {
        break
      }

      const formElement = formElements[i]
      
      try {
        const form = await this.analyzeForm(formElement, i, pageContext, performanceMetrics)
        
        if (form && form.confidence >= (options.minConfidence || this.MIN_CONFIDENCE_SCORE)) {
          detectedForms.push(form)
        }
      } catch (error) {
        console.warn(`[AdvancedFormDetector] 表单 ${i} 分析失败:`, error)
      }
    }

    // 按置信度排序
    detectedForms.sort((a, b) => b.confidence - a.confidence)

    // 如果启用了深度扫描，查找非form容器内的表单
    if (options.deepScan) {
      const additionalForms = await this.deepScanForForms(document, options)
      detectedForms.push(...additionalForms)
    }

    return detectedForms
  }

  private async analyzeForm(
    formElement: HTMLFormElement, 
    index: number, 
    pageContext: any,
    performanceMetrics: PerformanceMetrics
  ): Promise<FormElement | null> {
    const startTime = performance.now()
    
    try {
      // 生成唯一ID
      const formId = this.generateFormId(formElement, index)
      
      // 分析表单字段
      const fields = await this.analyzeFormFields(formElement)
      if (fields.length === 0) {
        return null
      }

      // 查找提交按钮
      const submitButton = this.findSubmitButton(formElement)
      
      // 分析表单元数据
      const metadata = this.analyzeFormMetadata(formElement, fields)
      
      // 计算置信度
      const confidence = this.calculateAdvancedConfidence(formElement, fields, submitButton, metadata)
      
      // 创建检测上下文
      const detectionContext: DetectionContext = {
        detectorType: 'hybrid',
        detectionStrategy: 'deep_scan',
        pageContext,
        performanceMetrics: {
          ...performanceMetrics,
          detectionTime: performance.now() - startTime,
          elementsScanned: fields.length + 1
        }
      }

      const form: FormElement = {
        id: formId,
        element: formElement,
        fields,
        submitButton,
        confidence,
        metadata,
        detectionContext,
        lastUpdated: Date.now()
      }

      return form
    } catch (error) {
      console.warn(`[AdvancedFormDetector] 表单分析失败:`, error)
      return null
    }
  }

  private async analyzeFormFields(formElement: HTMLFormElement): Promise<FormField[]> {
    const inputs = Array.from(formElement.querySelectorAll('input, textarea, select'))
    const fields: FormField[] = []

    for (let i = 0; i < inputs.length; i++) {
      const element = inputs[i]
      
      try {
        const field = await this.analyzeField(element, i)
        if (field) {
          fields.push(field)
        }
      } catch (error) {
        console.warn(`[AdvancedFormDetector] 字段分析失败:`, error)
      }
    }

    // 使用语义分析器分析字段
    const analyzedFields = SemanticFieldAnalyzer.analyzeFormFields(fields)
    
    return analyzedFields
  }

  // 向后兼容的方法
  isSubmissionForm(form: FormElement): boolean {
    return form.confidence >= this.MIN_CONFIDENCE_SCORE
  }

  private async analyzeField(
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement, 
    position: number
  ): Promise<FormField | null> {
    // 跳过隐藏字段和特殊类型
    if (this.shouldSkipField(element)) {
      return null
    }

    const fieldType = this.determineFieldType(element)
    const name = this.extractFieldName(element)
    const label = this.extractFieldLabel(element)
    const placeholder = this.extractFieldPlaceholder(element)
    const required = this.isFieldRequired(element)
    const maxLength = this.extractMaxLength(element)
    const validationRules = this.extractValidationRules(element)
    const visualProperties = this.getVisualProperties(element)

    const fieldContext: FieldContext = {
      position,
      visualProperties
    }

    return {
      id: this.generateFieldId(element, position),
      element,
      type: fieldType,
      name,
      label,
      placeholder,
      required,
      maxLength,
      confidence: 0.5, // 将由语义分析器更新
      validationRules,
      context: fieldContext
    }
  }

  private generateFormId(formElement: HTMLFormElement, index: number): string {
    const url = window.location.hostname
    const formAction = formElement.action || 'no-action'
    const timestamp = Date.now()
    return `form_${url}_${index}_${formAction.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}`
  }

  private generateFieldId(element: HTMLElement, position: number): string {
    const name = element.getAttribute('name') || element.id || 'unnamed'
    const type = element.getAttribute('type') || element.tagName.toLowerCase()
    return `field_${name}_${type}_${position}`
  }

  private analyzeFormMetadata(formElement: HTMLFormElement, fields: FormField[]): FormMetadata {
    const framework = PageAnalyzer.detectWebFramework(document, window)
    const platform = PageAnalyzer.detectPlatform(document, window)
    
    return {
      title: this.extractFormTitle(formElement),
      description: this.extractFormDescription(formElement),
      category: this.determineFormCategory(formElement, fields),
      platform,
      framework,
      submitUrl: formElement.action || undefined,
      method: formElement.method?.toLowerCase() || 'get',
      enctype: formElement.enctype || undefined,
      hasValidation: this.hasFormValidation(formElement, fields),
      isMultiStep: this.isMultiStepForm(formElement),
      estimatedCompletionTime: PageAnalyzer.estimateFormCompletionTime(
        fields.length, 
        this.hasFormValidation(formElement, fields)
      )
    }
  }

  private shouldSkipField(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): boolean {
    const type = (input as HTMLInputElement).type?.toLowerCase()
    const style = window.getComputedStyle(input)

    // 跳过隐藏字段
    if (style.display === 'none' || style.visibility === 'hidden') {
      return true
    }

    // 跳过特殊输入类型
    const skipTypes = ['hidden', 'submit', 'button', 'reset', 'image']
    if (type && skipTypes.includes(type)) {
      return true
    }

    // 跳过密码字段（但可以通过配置允许）
    if (type === 'password') {
      return true
    }

    // 跳过小尺寸元素（可能是隐藏的）
    const rect = input.getBoundingClientRect()
    if (rect.width < 10 || rect.height < 10) {
      return true
    }

    return false
  }

  private getVisualProperties(element: HTMLElement): VisualProperties {
    const style = window.getComputedStyle(element)
    const rect = element.getBoundingClientRect()
    
    return {
      visible: style.display !== 'none' && style.visibility !== 'hidden',
      position: rect,
      zIndex: parseInt(style.zIndex) || 0,
      backgroundColor: style.backgroundColor,
      fontSize: parseFloat(style.fontSize) || undefined
    }
  }

  private extractValidationRules(element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): ValidationRule[] {
    const rules: ValidationRule[] = []

    // required属性
    if (element.required || element.hasAttribute('required')) {
      rules.push({ type: 'required' })
    }

    // pattern属性
    const pattern = (element as HTMLInputElement).pattern
    if (pattern) {
      rules.push({ type: 'pattern', value: pattern })
    }

    // 长度限制
    const maxLength = (element as HTMLInputElement | HTMLTextAreaElement).maxLength
    if (maxLength > 0) {
      rules.push({ type: 'length', value: { max: maxLength } })
    }

    const minLength = (element as HTMLInputElement | HTMLTextAreaElement).minLength
    if (minLength > 0) {
      rules.push({ type: 'length', value: { min: minLength } })
    }

    return rules
  }

  private determineFieldType(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): any {
    if (input.tagName.toLowerCase() === 'textarea') {
      return 'textarea'
    }

    if (input.tagName.toLowerCase() === 'select') {
      return 'select'
    }

    const htmlInput = input as HTMLInputElement
    const type = htmlInput.type?.toLowerCase()

    // 支持更多类型
    switch (type) {
      case 'email': return 'email'
      case 'url': return 'url'
      case 'file': return 'file'
      case 'number': return 'number'
      case 'tel': return 'tel'
      case 'password': return 'password'
      case 'search': return 'search'
      case 'date': return 'date'
      case 'checkbox': return 'checkbox'
      case 'radio': return 'radio'
      case 'range': return 'range'
      case 'color': return 'color'
      default: return 'text'
    }
  }

  private extractFieldName(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string {
    return input.name || input.id || this.generateFieldName(input)
  }

  private extractFieldLabel(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string | undefined {
    // 查找关联的label标签
    let label: HTMLLabelElement | null = null

    if (input.id) {
      label = document.querySelector(`label[for="${input.id}"]`)
    }

    if (!label) {
      // 查找父级label
      let parent = input.parentElement
      while (parent && parent.tagName.toLowerCase() !== 'form') {
        if (parent.tagName.toLowerCase() === 'label') {
          label = parent as HTMLLabelElement
          break
        }
        parent = parent.parentElement
      }
    }

    if (!label) {
      // 查找前一个文本节点或元素
      label = this.findNearbyLabel(input)
    }

    return label?.textContent?.trim() || undefined
  }

  private extractFieldPlaceholder(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): string | undefined {
    const htmlInput = input as HTMLInputElement
    return htmlInput.placeholder?.trim() || undefined
  }

  private isFieldRequired(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): boolean {
    return input.required || input.hasAttribute('required')
  }

  private extractMaxLength(input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): number | undefined {
    const htmlInput = input as HTMLInputElement | HTMLTextAreaElement
    return htmlInput.maxLength > 0 ? htmlInput.maxLength : undefined
  }

  private findSubmitButton(form: HTMLFormElement): HTMLButtonElement | undefined {
    // 优先查找type="submit"的按钮
    const submitButtons = Array.from(form.querySelectorAll('button[type="submit"], input[type="submit"]'))
    if (submitButtons.length > 0) {
      return submitButtons[0] as HTMLButtonElement
    }

    // 查找默认button（没有指定type的button默认是submit）
    const defaultButtons = Array.from(form.querySelectorAll('button:not([type])'))
    if (defaultButtons.length > 0) {
      return defaultButtons[0] as HTMLButtonElement
    }

    // 基于文本内容查找可能的提交按钮
    const allButtons = Array.from(form.querySelectorAll('button, input[type="button"], div[role="button"], span[role="button"]'))
    
    for (const button of allButtons) {
      const text = (button.textContent?.toLowerCase() || (button as HTMLInputElement).value?.toLowerCase() || '').trim()
      
      if (this.SUBMISSION_KEYWORDS.some(keyword => text.includes(keyword))) {
        return button as HTMLButtonElement
      }
    }

    // 查找包含提交相关class的元素
    const classBasedButtons = Array.from(form.querySelectorAll('[class*="submit"], [class*="send"], [class*="post"]'))
    if (classBasedButtons.length > 0) {
      return classBasedButtons[0] as HTMLButtonElement
    }

    return undefined
  }

  private determineFormCategory(formElement: HTMLFormElement, fields: FormField[]): FormCategory {
    const formText = (formElement.textContent || '').toLowerCase()
    const action = (formElement.action || '').toLowerCase()
    const className = (formElement.className || '').toLowerCase()
    
    // 检查URL和内容中的关键词
    if (action.includes('contact') || formText.includes('contact us') || formText.includes('get in touch')) {
      return 'contact'
    }
    
    if (action.includes('signup') || action.includes('register') || formText.includes('sign up') || formText.includes('create account')) {
      return 'signup'
    }
    
    if (action.includes('login') || action.includes('signin') || formText.includes('log in') || formText.includes('sign in')) {
      return 'login'
    }
    
    if (action.includes('newsletter') || formText.includes('newsletter') || formText.includes('subscribe')) {
      return 'newsletter'
    }
    
    if (action.includes('survey') || formText.includes('survey') || formText.includes('feedback')) {
      return 'survey'
    }
    
    if (action.includes('order') || action.includes('purchase') || formText.includes('buy') || formText.includes('order')) {
      return 'order'
    }
    
    if (action.includes('support') || formText.includes('support') || formText.includes('help')) {
      return 'support'
    }
    
    if (action.includes('apply') || formText.includes('application') || formText.includes('submit')) {
      return 'application'
    }

    // 基于字段类型推断
    const hasProjectFields = fields.some(f => 
      f.semanticType === 'project_name' || 
      f.semanticType === 'project_description' || 
      f.semanticType === 'project_url'
    )
    
    if (hasProjectFields) {
      return 'submission'
    }

    return 'unknown'
  }

  private hasFormValidation(formElement: HTMLFormElement, fields: FormField[]): boolean {
    // 检查HTML5验证
    if (fields.some(field => field.required || field.validationRules && field.validationRules.length > 0)) {
      return true
    }

    // 检查JavaScript验证
    const scripts = document.querySelectorAll('script')
    for (const script of scripts) {
      const content = script.textContent || ''
      if (content.includes('validate') || content.includes('validation')) {
        return true
      }
    }

    // 检查验证相关的CSS类
    const validationClasses = ['validate', 'validation', 'error', 'invalid', 'required']
    const hasValidationClasses = validationClasses.some(cls => 
      formElement.querySelector(`[class*="${cls}"]`)
    )

    return hasValidationClasses
  }

  private isMultiStepForm(formElement: HTMLFormElement): boolean {
    // 检查是否有步骤指示器
    const stepIndicators = formElement.querySelectorAll(
      '[class*="step"], [class*="wizard"], [class*="progress"], [id*="step"]'
    )
    
    if (stepIndicators.length > 1) {
      return true
    }

    // 检查是否有隐藏的表单部分
    const hiddenSections = formElement.querySelectorAll('[style*="display: none"], [hidden]')
    if (hiddenSections.length > 0) {
      return true
    }

    // 检查是否有"下一步"/"上一步"按钮
    const navigationButtons = formElement.querySelectorAll('button, input[type="button"]')
    for (const button of navigationButtons) {
      const text = button.textContent?.toLowerCase() || ''
      if (text.includes('next') || text.includes('previous') || text.includes('下一步') || text.includes('上一步')) {
        return true
      }
    }

    return false
  }

  private extractFormTitle(formElement: HTMLFormElement): string | undefined {
    // 查找表单内的标题
    const headings = formElement.querySelectorAll('h1, h2, h3, h4, h5, h6')
    if (headings.length > 0) {
      const title = headings[0].textContent?.trim()
      if (title && title.length < 200) {
        return title
      }
    }

    // 查找表单前的标题
    let previousElement = formElement.previousElementSibling
    while (previousElement) {
      if (previousElement.matches('h1, h2, h3, h4, h5, h6')) {
        const title = previousElement.textContent?.trim()
        if (title && title.length < 200) {
          return title
        }
        break
      }
      previousElement = previousElement.previousElementSibling
    }

    // 查找legend元素
    const legend = formElement.querySelector('legend')
    if (legend) {
      const title = legend.textContent?.trim()
      if (title && title.length < 200) {
        return title
      }
    }

    return undefined
  }

  private extractFormDescription(formElement: HTMLFormElement): string | undefined {
    // 查找描述性段落
    const descriptions = formElement.querySelectorAll('p, div.description, div.help-text')
    
    for (const desc of descriptions) {
      const text = desc.textContent?.trim()
      if (text && text.length > 20 && text.length < 500) {
        return text
      }
    }

    return undefined
  }

  private calculateAdvancedConfidence(
    formElement: HTMLFormElement,
    fields: FormField[],
    submitButton?: HTMLButtonElement,
    metadata?: FormMetadata
  ): number {
    let totalScore = 0
    let maxScore = 0

    // 1. 基础结构分数 (20%)
    const structureScore = this.calculateStructureScore(formElement, fields, submitButton)
    totalScore += structureScore * 0.2
    maxScore += 0.2

    // 2. 语义相关性分数 (30%)
    const semanticScore = this.calculateSemanticScore(fields)
    totalScore += semanticScore * 0.3
    maxScore += 0.3

    // 3. 表单质量分数 (20%)
    const qualityScore = this.calculateQualityScore(formElement, fields)
    totalScore += qualityScore * 0.2
    maxScore += 0.2

    // 4. 上下文分数 (20%)
    const contextScore = this.calculateContextScore(formElement, metadata)
    totalScore += contextScore * 0.2
    maxScore += 0.2

    // 5. 可访问性分数 (10%)
    const accessibilityBonus = this.calculateAccessibilityBonus(formElement, fields)
    totalScore += accessibilityBonus * 0.1
    maxScore += 0.1

    return maxScore > 0 ? Math.min(totalScore / maxScore, 1) : 0
  }

  private calculateStructureScore(
    formElement: HTMLFormElement,
    fields: FormField[],
    submitButton?: HTMLButtonElement
  ): number {
    let score = 0

    // 有字段 (必须)
    if (fields.length > 0) score += 0.3

    // 有提交按钮
    if (submitButton) score += 0.3

    // 字段数量合理 (3-20个字段)
    if (fields.length >= 3 && fields.length <= 20) {
      score += 0.2
    } else if (fields.length >= 1 && fields.length < 3) {
      score += 0.1 // 简单表单也给一些分数
    }

    // 有必填字段
    if (fields.some(field => field.required)) {
      score += 0.1
    }

    // 表单有action
    if (formElement.action && formElement.action.trim() !== '') {
      score += 0.1
    }

    return Math.min(score, 1)
  }

  private calculateSemanticScore(fields: FormField[]): number {
    if (fields.length === 0) return 0

    let semanticScore = 0
    let totalImportanceWeight = 0

    for (const field of fields) {
      if (field.semanticType && field.semanticType !== 'unknown') {
        const importance = SemanticFieldAnalyzer.getSemanticTypeImportance(field.semanticType)
        const confidence = field.confidence || 0.5
        
        semanticScore += importance * confidence
        totalImportanceWeight += importance
      } else {
        totalImportanceWeight += 1 // 未知类型给最低权重
      }
    }

    // 如果有项目相关的高价值字段，额外加分
    const hasHighValueFields = fields.some(f => 
      ['project_name', 'project_description', 'project_url'].includes(f.semanticType || '')
    )
    
    if (hasHighValueFields) {
      semanticScore *= 1.2 // 20% 加成
    }

    return totalImportanceWeight > 0 ? Math.min(semanticScore / (totalImportanceWeight * 10), 1) : 0
  }

  private calculateQualityScore(formElement: HTMLFormElement, fields: FormField[]): number {
    let score = 0

    // 有标签的字段比例
    const fieldsWithLabels = fields.filter(f => f.label && f.label.trim() !== '')
    if (fields.length > 0) {
      score += (fieldsWithLabels.length / fields.length) * 0.3
    }

    // 有placeholder的字段比例
    const fieldsWithPlaceholders = fields.filter(f => f.placeholder && f.placeholder.trim() !== '')
    if (fields.length > 0) {
      score += (fieldsWithPlaceholders.length / fields.length) * 0.2
    }

    // 表单有标题
    const hasTitle = !!formElement.querySelector('h1, h2, h3, h4, h5, h6, legend')
    if (hasTitle) score += 0.2

    // 使用语义化HTML
    const hasSemanticElements = formElement.querySelectorAll('fieldset, legend, label').length > 0
    if (hasSemanticElements) score += 0.2

    // 表单可见性
    const isVisible = formElement.offsetWidth > 0 && formElement.offsetHeight > 0
    if (isVisible) score += 0.1

    return Math.min(score, 1)
  }

  private calculateContextScore(formElement: HTMLFormElement, metadata?: FormMetadata): number {
    let score = 0

    // 在已知的提交平台上
    if (metadata?.platform) {
      score += 0.4
    }

    // 表单分类明确
    if (metadata?.category && metadata.category !== 'unknown') {
      score += 0.3
    }

    // URL包含提交相关词汇
    const url = window.location.href.toLowerCase()
    const submissionUrls = ['submit', 'post', 'create', 'add', 'new', 'form', 'apply']
    if (submissionUrls.some(keyword => url.includes(keyword))) {
      score += 0.2
    }

    // 页面标题包含相关词汇
    const title = document.title.toLowerCase()
    if (submissionUrls.some(keyword => title.includes(keyword))) {
      score += 0.1
    }

    return Math.min(score, 1)
  }

  private calculateAccessibilityBonus(formElement: HTMLFormElement, fields: FormField[]): number {
    let bonus = 0

    // ARIA标签使用
    const hasAriaLabels = fields.some(f => 
      f.element.hasAttribute('aria-label') || 
      f.element.hasAttribute('aria-labelledby') ||
      f.element.hasAttribute('aria-describedby')
    )
    if (hasAriaLabels) bonus += 0.3

    // 键盘导航支持
    const hasTabIndex = fields.some(f => f.element.hasAttribute('tabindex'))
    if (hasTabIndex) bonus += 0.2

    // 表单有fieldset和legend结构
    const hasFieldset = formElement.querySelectorAll('fieldset').length > 0
    if (hasFieldset) bonus += 0.3

    // 错误处理和提示
    const hasErrorHandling = formElement.querySelectorAll('[role="alert"], .error, .invalid').length > 0
    if (hasErrorHandling) bonus += 0.2

    return Math.min(bonus, 1)
  }

  // 向后兼容的方法
  private calculateConfidence(
    form: HTMLFormElement, 
    fields: FormField[], 
    submitButton?: HTMLButtonElement
  ): number {
    // 使用新的高级置信度计算
    return this.calculateAdvancedConfidence(form, fields, submitButton)
  }

  private async deepScanForForms(document: Document, options: DetectionOptions): Promise<FormElement[]> {
    const additionalForms: FormElement[] = []
    
    try {
      console.log('[AdvancedFormDetector] 开始深度扫描非form容器内的表单')

      // 查找可能的表单容器（没有form标签但包含表单元素）
      const potentialContainers = document.querySelectorAll(
        'div[class*="form"], div[id*="form"], section[class*="form"], ' +
        'div[class*="submit"], div[class*="contact"], div[class*="signup"], ' +
        'div[role="form"], [data-form], [data-component*="form"]'
      )

      for (const container of potentialContainers) {
        if (container instanceof HTMLElement && !(container instanceof HTMLFormElement)) {
          const inputs = container.querySelectorAll('input, textarea, select')
          
          if (inputs.length >= 2) { // 至少2个输入字段才考虑
            try {
              const virtualForm = await this.createVirtualForm(container as HTMLElement, inputs)
              if (virtualForm && virtualForm.confidence >= (options.minConfidence || this.MIN_CONFIDENCE_SCORE)) {
                additionalForms.push(virtualForm)
              }
            } catch (error) {
              console.warn('[AdvancedFormDetector] 虚拟表单创建失败:', error)
            }
          }
        }
      }

      console.log(`[AdvancedFormDetector] 深度扫描完成，发现 ${additionalForms.length} 个额外表单`)
      return additionalForms

    } catch (error) {
      console.error('[AdvancedFormDetector] 深度扫描失败:', error)
      return []
    }
  }

  private async createVirtualForm(
    container: HTMLElement, 
    inputs: NodeListOf<Element>
  ): Promise<FormElement | null> {
    try {
      // 创建虚拟form元素
      const virtualFormElement = document.createElement('form')
      virtualFormElement.style.display = 'none'
      
      // 复制container的属性
      virtualFormElement.className = container.className
      virtualFormElement.id = `virtual-form-${Date.now()}`

      // 查找提交按钮
      const submitButton = container.querySelector(
        'button[type="submit"], input[type="submit"], ' +
        'button:not([type]), [role="button"]'
      ) as HTMLButtonElement

      // 分析字段
      const fields: FormField[] = []
      for (let i = 0; i < inputs.length; i++) {
        const input = inputs[i] as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        
        if (!this.shouldSkipField(input)) {
          const field = await this.analyzeField(input, i)
          if (field) {
            fields.push(field)
          }
        }
      }

      if (fields.length === 0) {
        return null
      }

      // 使用语义分析器
      const analyzedFields = SemanticFieldAnalyzer.analyzeFormFields(fields)

      // 创建虚拟表单的元数据
      const metadata: FormMetadata = {
        title: this.extractContainerTitle(container),
        description: this.extractContainerDescription(container),
        category: this.determineFormCategory(virtualFormElement, analyzedFields),
        platform: PageAnalyzer.detectPlatform(document, window),
        framework: PageAnalyzer.detectWebFramework(document, window),
        method: 'post',
        hasValidation: false,
        isMultiStep: false,
        estimatedCompletionTime: PageAnalyzer.estimateFormCompletionTime(fields.length, false)
      }

      // 计算置信度
      const confidence = this.calculateAdvancedConfidence(virtualFormElement, analyzedFields, submitButton, metadata)

      // 创建虚拟表单对象
      const virtualForm: FormElement = {
        id: this.generateFormId(virtualFormElement, -1),
        element: container as any, // 使用容器作为表单元素
        fields: analyzedFields,
        submitButton,
        confidence,
        metadata,
        detectionContext: {
          detectorType: 'hybrid',
          detectionStrategy: 'deep_scan',
          pageContext: PageAnalyzer.analyzePageContext(document, window),
          performanceMetrics: {
            detectionTime: 0,
            elementsScanned: inputs.length
          }
        },
        lastUpdated: Date.now()
      }

      return virtualForm

    } catch (error) {
      console.warn('[AdvancedFormDetector] 虚拟表单创建失败:', error)
      return null
    }
  }

  private extractContainerTitle(container: HTMLElement): string | undefined {
    // 查找容器内的标题
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
    if (headings.length > 0) {
      const title = headings[0].textContent?.trim()
      if (title && title.length < 200) {
        return title
      }
    }

    // 查找容器前的标题
    let previousElement = container.previousElementSibling
    while (previousElement) {
      if (previousElement.matches('h1, h2, h3, h4, h5, h6')) {
        const title = previousElement.textContent?.trim()
        if (title && title.length < 200) {
          return title
        }
        break
      }
      previousElement = previousElement.previousElementSibling
    }

    return undefined
  }

  private extractContainerDescription(container: HTMLElement): string | undefined {
    const descriptions = container.querySelectorAll('p, div.description, div.help-text, .subtitle')
    
    for (const desc of descriptions) {
      const text = desc.textContent?.trim()
      if (text && text.length > 20 && text.length < 500) {
        return text
      }
    }

    return undefined
  }

  // 向后兼容的旧方法（已弃用，但保留以防其他代码依赖）
  private calculateFieldRelevance(fields: FormField[]): number {
    console.warn('[AdvancedFormDetector] calculateFieldRelevance is deprecated, use calculateSemanticScore instead')
    return this.calculateSemanticScore(fields)
  }

  private isRelevantField(field: FormField): boolean {
    console.warn('[AdvancedFormDetector] isRelevantField is deprecated')
    return field.semanticType !== 'unknown'
  }

  private analyzeFormStructure(form: HTMLFormElement, fields: FormField[]): number {
    console.warn('[AdvancedFormDetector] analyzeFormStructure is deprecated, use calculateStructureScore instead')
    return this.calculateStructureScore(form, fields, undefined)
  }

  // 向后兼容的方法
  getFormContext(form: HTMLFormElement): any {
    const title = this.extractFormTitle(form)
    const description = this.extractFormDescription(form)
    
    return {
      action: form.action || undefined,
      method: form.method?.toLowerCase() || 'get',
      enctype: form.enctype || undefined,
      title,
      description
    }
  }

  // 清理缓存的方法
  clearCache(): void {
    this.cache.clear()
    console.log('[AdvancedFormDetector] 缓存已清理')
  }

  // 获取检测器统计信息
  getStats(): {
    cacheStats: any
  } {
    return {
      cacheStats: this.cache.getStats()
    }
  }
}