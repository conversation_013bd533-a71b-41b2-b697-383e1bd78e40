import type { 
  FormMonitor, 
  FormElement, 
  FormChange, 
  MonitoringOptions,
  DetectionEnvironment
} from '~/types'
import { AdvancedFormDetector } from './detector'
import { PageAnalyzer } from './page-analyzer'
import { FormDetectionCache } from './cache'

export class FormMonitorService implements FormMonitor {
  private detector: AdvancedFormDetector
  private cache: FormDetectionCache
  private mutationObserver?: MutationObserver
  private intersectionObserver?: IntersectionObserver
  private isMonitoring = false
  private debounceTimer?: number
  private currentForms = new Map<string, FormElement>()
  private callbacks = {
    formAdded: new Set<(form: FormElement) => void>(),
    formRemoved: new Set<(formId: string) => void>(),
    formChanged: new Set<(form: FormElement, changes: FormChange[]) => void>()
  }

  constructor() {
    this.detector = new AdvancedFormDetector()
    this.cache = new FormDetectionCache()
  }

  startMonitoring(options?: MonitoringOptions): void {
    if (this.isMonitoring) {
      console.warn('[FormMonitor] 监控已在运行')
      return
    }

    // 检查环境是否可用
    if (typeof document === 'undefined' || typeof window === 'undefined') {
      console.error('[FormMonitor] Document或Window不可用，无法启动监控')
      return
    }

    const config = {
      debounceMs: 1000,
      enableMutationObserver: true,
      enableIntersectionObserver: true,
      trackVisibilityChanges: true,
      ...options
    }

    console.log('[FormMonitor] 开始表单监控', config)

    try {
      // 初始扫描
      this.performInitialScan().catch(error => {
        console.error('[FormMonitor] 初始扫描失败:', error)
      })

      // 设置变异观察器
      if (config.enableMutationObserver) {
        this.setupMutationObserver(config.debounceMs)
      }

      // 设置交集观察器
      if (config.enableIntersectionObserver && config.trackVisibilityChanges) {
        this.setupIntersectionObserver()
      }

      this.isMonitoring = true
    } catch (error) {
      console.error('[FormMonitor] 启动监控失败:', error)
    }
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    console.log('[FormMonitor] 停止表单监控')

    // 清理观察器
    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
      this.mutationObserver = undefined
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
      this.intersectionObserver = undefined
    }

    // 清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = undefined
    }

    this.isMonitoring = false
    this.currentForms.clear()
  }

  onFormAdded(callback: (form: FormElement) => void): void {
    this.callbacks.formAdded.add(callback)
  }

  onFormRemoved(callback: (formId: string) => void): void {
    this.callbacks.formRemoved.add(callback)
  }

  onFormChanged(callback: (form: FormElement, changes: FormChange[]) => void): void {
    this.callbacks.formChanged.add(callback)
  }

  // 移除回调
  removeCallback(type: 'formAdded' | 'formRemoved' | 'formChanged', callback: Function): void {
    this.callbacks[type].delete(callback as any)
  }

  // 获取当前监控的表单
  getCurrentForms(): FormElement[] {
    return Array.from(this.currentForms.values())
  }

  private async performInitialScan(): Promise<void> {
    try {
      const env: DetectionEnvironment = {
        document,
        window,
        pageContext: PageAnalyzer.analyzePageContext(document, window),
        cache: this.cache,
        options: {
          enableCache: true,
          deepScan: true,
          minConfidence: 0.3
        }
      }

      const forms = await this.detector.detectForms(env)
      
      for (const form of forms) {
        this.currentForms.set(form.id, form)
        this.notifyFormAdded(form)
      }

      console.log(`[FormMonitor] 初始扫描完成，发现 ${forms.length} 个表单`)
    } catch (error) {
      console.error('[FormMonitor] 初始扫描失败:', error)
    }
  }

  private setupMutationObserver(debounceMs: number): void {
    try {
      if (typeof MutationObserver === 'undefined' || !document.body) {
        console.warn('[FormMonitor] MutationObserver不可用或document.body不存在')
        return
      }

      this.mutationObserver = new MutationObserver((mutations) => {
        // 防抖处理
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer)
        }

        this.debounceTimer = window.setTimeout(() => {
          this.handleMutations(mutations).catch(error => {
            console.error('[FormMonitor] 处理变异失败:', error)
          })
        }, debounceMs)
      })

      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'id', 'style', 'hidden'],
        characterData: true
      })
    } catch (error) {
      console.error('[FormMonitor] 设置MutationObserver失败:', error)
    }
  }

  private setupIntersectionObserver(): void {
    try {
      if (typeof IntersectionObserver === 'undefined') {
        console.warn('[FormMonitor] IntersectionObserver不可用')
        return
      }

      const formElements = Array.from(this.currentForms.values()).map(f => f.element)
      
      if (formElements.length === 0) {
        console.log('[FormMonitor] 没有表单需要观察，跳过IntersectionObserver设置')
        return
      }

      this.intersectionObserver = new IntersectionObserver((entries) => {
        try {
          for (const entry of entries) {
            const formElement = entry.target as HTMLFormElement
            const formId = this.findFormIdByElement(formElement)
            
            if (formId) {
              const form = this.currentForms.get(formId)
              if (form) {
                const isNowVisible = entry.isIntersecting
                const wasVisible = form.metadata.platform !== undefined // 简化的可见性检查
                
                if (isNowVisible !== wasVisible) {
                  const changes: FormChange[] = [{
                    type: 'form_moved',
                    oldValue: wasVisible,
                    newValue: isNowVisible,
                    timestamp: Date.now()
                  }]
                  
                  this.notifyFormChanged(form, changes)
                }
              }
            }
          }
        } catch (error) {
          console.error('[FormMonitor] IntersectionObserver回调错误:', error)
        }
      }, {
        threshold: [0, 0.5, 1.0]
      })

      // 观察现有表单
      formElements.forEach(element => {
        try {
          if (element && element.isConnected) {
            this.intersectionObserver!.observe(element)
          }
        } catch (error) {
          console.warn('[FormMonitor] 观察表单元素失败:', error)
        }
      })
    } catch (error) {
      console.error('[FormMonitor] 设置IntersectionObserver失败:', error)
    }
  }

  private async handleMutations(mutations: MutationRecord[]): Promise<void> {
    let hasStructuralChanges = false
    const changedForms = new Set<string>()

    // 分析变异
    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        // 检查添加的节点
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            
            // 检查是否是表单或包含表单
            if (element.tagName === 'FORM' || element.querySelector('form')) {
              hasStructuralChanges = true
            }
            
            // 检查是否是表单字段
            if (this.isFormField(element)) {
              const containingForm = this.findContainingForm(element)
              if (containingForm) {
                const formId = this.findFormIdByElement(containingForm)
                if (formId) {
                  changedForms.add(formId)
                }
              }
            }
          }
        }

        // 检查移除的节点
        for (const node of Array.from(mutation.removedNodes)) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            
            if (element.tagName === 'FORM') {
              hasStructuralChanges = true
              
              // 查找并移除对应的表单
              const formId = this.findFormIdByElement(element as HTMLFormElement)
              if (formId && this.currentForms.has(formId)) {
                this.currentForms.delete(formId)
                this.notifyFormRemoved(formId)
              }
            }
          }
        }
      } else if (mutation.type === 'attributes') {
        // 属性变化
        const element = mutation.target as Element
        if (this.isFormField(element)) {
          const containingForm = this.findContainingForm(element)
          if (containingForm) {
            const formId = this.findFormIdByElement(containingForm)
            if (formId) {
              changedForms.add(formId)
            }
          }
        }
      }
    }

    // 处理结构性变化
    if (hasStructuralChanges) {
      await this.handleStructuralChanges()
    }

    // 处理表单字段变化
    for (const formId of changedForms) {
      await this.handleFormFieldChanges(formId)
    }
  }

  private async handleStructuralChanges(): Promise<void> {
    try {
      // 重新扫描所有表单
      const env: DetectionEnvironment = {
        document,
        window,
        pageContext: PageAnalyzer.analyzePageContext(document, window),
        cache: this.cache,
        options: {
          enableCache: false, // 强制重新检测
          deepScan: true,
          minConfidence: 0.3
        }
      }

      const newForms = await this.detector.detectForms(env)
      const newFormIds = new Set(newForms.map(f => f.id))
      const currentFormIds = new Set(this.currentForms.keys())

      // 找出新增的表单
      for (const form of newForms) {
        if (!currentFormIds.has(form.id)) {
          this.currentForms.set(form.id, form)
          this.notifyFormAdded(form)
          
          // 为新表单设置交集观察器
          if (this.intersectionObserver) {
            this.intersectionObserver.observe(form.element)
          }
        }
      }

      // 找出移除的表单
      for (const formId of currentFormIds) {
        if (!newFormIds.has(formId)) {
          this.currentForms.delete(formId)
          this.notifyFormRemoved(formId)
        }
      }

    } catch (error) {
      console.error('[FormMonitor] 处理结构性变化失败:', error)
    }
  }

  private async handleFormFieldChanges(formId: string): Promise<void> {
    const currentForm = this.currentForms.get(formId)
    if (!currentForm) {
      return
    }

    try {
      // 重新分析表单
      const env: DetectionEnvironment = {
        document,
        window,
        pageContext: PageAnalyzer.analyzePageContext(document, window),
        cache: this.cache,
        options: { enableCache: false }
      }

      const newForms = await this.detector.detectForms(env)
      const updatedForm = newForms.find(f => f.element === currentForm.element)

      if (updatedForm) {
        // 比较字段变化
        const changes = this.compareFormFields(currentForm, updatedForm)
        
        if (changes.length > 0) {
          this.currentForms.set(formId, updatedForm)
          this.notifyFormChanged(updatedForm, changes)
        }
      }

    } catch (error) {
      console.error('[FormMonitor] 处理表单字段变化失败:', error)
    }
  }

  private compareFormFields(oldForm: FormElement, newForm: FormElement): FormChange[] {
    const changes: FormChange[] = []
    const oldFieldIds = new Set(oldForm.fields.map(f => f.id))
    const newFieldIds = new Set(newForm.fields.map(f => f.id))

    // 检查新增字段
    for (const field of newForm.fields) {
      if (!oldFieldIds.has(field.id)) {
        changes.push({
          type: 'field_added',
          fieldId: field.id,
          newValue: field,
          timestamp: Date.now()
        })
      }
    }

    // 检查移除字段
    for (const field of oldForm.fields) {
      if (!newFieldIds.has(field.id)) {
        changes.push({
          type: 'field_removed',
          fieldId: field.id,
          oldValue: field,
          timestamp: Date.now()
        })
      }
    }

    // 检查修改字段
    for (const newField of newForm.fields) {
      const oldField = oldForm.fields.find(f => f.id === newField.id)
      if (oldField && this.hasFieldChanged(oldField, newField)) {
        changes.push({
          type: 'field_modified',
          fieldId: newField.id,
          oldValue: oldField,
          newValue: newField,
          timestamp: Date.now()
        })
      }
    }

    return changes
  }

  private hasFieldChanged(oldField: any, newField: any): boolean {
    return (
      oldField.name !== newField.name ||
      oldField.type !== newField.type ||
      oldField.required !== newField.required ||
      oldField.label !== newField.label ||
      oldField.placeholder !== newField.placeholder
    )
  }

  private isFormField(element: Element): boolean {
    return element.matches('input, textarea, select, button')
  }

  private findContainingForm(element: Element): HTMLFormElement | null {
    return element.closest('form')
  }

  private findFormIdByElement(element: HTMLFormElement): string | null {
    for (const [id, form] of this.currentForms.entries()) {
      if (form.element === element) {
        return id
      }
    }
    return null
  }

  private notifyFormAdded(form: FormElement): void {
    console.log('[FormMonitor] 发现新表单:', form.id)
    this.callbacks.formAdded.forEach(callback => {
      try {
        callback(form)
      } catch (error) {
        console.error('[FormMonitor] 表单添加回调失败:', error)
      }
    })
  }

  private notifyFormRemoved(formId: string): void {
    console.log('[FormMonitor] 表单被移除:', formId)
    this.callbacks.formRemoved.forEach(callback => {
      try {
        callback(formId)
      } catch (error) {
        console.error('[FormMonitor] 表单移除回调失败:', error)
      }
    })
  }

  private notifyFormChanged(form: FormElement, changes: FormChange[]): void {
    console.log('[FormMonitor] 表单发生变化:', form.id, changes)
    this.callbacks.formChanged.forEach(callback => {
      try {
        callback(form, changes)
      } catch (error) {
        console.error('[FormMonitor] 表单变化回调失败:', error)
      }
    })
  }

  // 清理资源
  dispose(): void {
    this.stopMonitoring()
    this.callbacks.formAdded.clear()
    this.callbacks.formRemoved.clear()
    this.callbacks.formChanged.clear()
  }
}