import type { DetectionCache } from '~/types'

interface CacheEntry<T> {
  value: T
  timestamp: number
  ttl: number
}

export class FormDetectionCache implements DetectionCache {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5分钟默认TTL

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.value as T
  }

  set<T>(key: string, value: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    }
    
    this.cache.set(key, entry)
  }

  clear(): void {
    this.cache.clear()
  }

  invalidate(pattern?: string): void {
    if (!pattern) {
      this.clear()
      return
    }

    // 使用正则表达式匹配key
    const regex = new RegExp(pattern)
    const keysToDelete: string[] = []

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  // 额外的缓存管理方法
  size(): number {
    return this.cache.size
  }

  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  // 清理过期的缓存项
  cleanup(): number {
    const now = Date.now()
    let cleaned = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }

    return cleaned
  }

  // 获取缓存统计信息
  getStats(): {
    size: number
    memoryUsage: number
    hitRate?: number
    expiredEntries: number
  } {
    const now = Date.now()
    let expiredEntries = 0
    let memoryUsage = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredEntries++
      }
      
      // 估算内存使用量
      memoryUsage += key.length * 2 // key string
      memoryUsage += JSON.stringify(entry.value).length * 2 // value estimate
      memoryUsage += 16 // metadata
    }

    return {
      size: this.cache.size,
      memoryUsage,
      expiredEntries
    }
  }

  // 生成缓存key的工具方法
  static generateKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`
  }

  // 页面级别的缓存key
  static getPageKey(url: string): string {
    return this.generateKey('page', url)
  }

  // 表单级别的缓存key
  static getFormKey(url: string, formIndex: number): string {
    return this.generateKey('form', url, formIndex)
  }

  // DOM结构的缓存key
  static getDOMKey(url: string, domHash?: string): string {
    return this.generateKey('dom', url, domHash || 'default')
  }
}