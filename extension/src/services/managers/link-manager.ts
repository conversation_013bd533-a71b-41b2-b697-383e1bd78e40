import type { 
  ExternalLink, 
  CreateLinkDto, 
  LinkManager as ILinkManager,
  StorageService 
} from '~/types'
import { StorageFactory } from '../storage'

export class LinkManager implements ILinkManager {
  private storageService: StorageService | null = null
  private storageFactory: StorageFactory

  constructor() {
    this.storageFactory = StorageFactory.getInstance()
  }

  private async getStorageService(): Promise<StorageService> {
    if (!this.storageService) {
      this.storageService = await this.storageFactory.getStorageService()
    }
    return this.storageService
  }

  async getLinks(): Promise<ExternalLink[]> {
    try {
      const storage = await this.getStorageService()
      const links = await storage.getLinks()
      
      // 按更新时间降序排列
      return links.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    } catch (error) {
      console.error('获取外链列表失败:', error)
      throw new Error('获取外链列表失败，请稍后重试')
    }
  }

  async addLink(link: CreateLinkDto): Promise<ExternalLink> {
    try {
      this.validateLinkData(link)
      
      const storage = await this.getStorageService()
      
      // 检查URL是否重复
      const existingLinks = await storage.getLinks()
      const normalizedUrl = this.normalizeUrl(link.url)
      
      const isDuplicate = existingLinks.some(l => 
        this.normalizeUrl(l.url) === normalizedUrl
      )
      
      if (isDuplicate) {
        throw new Error('该外链平台已存在')
      }

      // 处理外链数据
      const processedLink = {
        ...link,
        name: link.name.trim(),
        url: normalizedUrl,
        submitUrl: link.submitUrl ? this.normalizeUrl(link.submitUrl) : undefined,
        isPaid: link.isPaid ?? false,
        category: link.category?.trim(),
        requirements: this.processRequirements(link.requirements || []),
        notes: link.notes?.trim()
      }

      const newLink = await storage.addLink(processedLink)
      console.log('外链平台添加成功:', newLink.name)
      
      return newLink
    } catch (error) {
      console.error('添加外链平台失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('添加外链平台失败，请稍后重试')
    }
  }

  async updateLink(id: string, updates: Partial<ExternalLink>): Promise<ExternalLink> {
    try {
      if (!id) {
        throw new Error('外链平台ID不能为空')
      }

      const storage = await this.getStorageService()
      
      // 检查外链是否存在
      const existingLinks = await storage.getLinks()
      const link = existingLinks.find(l => l.id === id)
      
      if (!link) {
        throw new Error('外链平台不存在')
      }

      // 如果更新了URL，检查是否重复
      if (updates.url && this.normalizeUrl(updates.url) !== this.normalizeUrl(link.url)) {
        const normalizedUrl = this.normalizeUrl(updates.url)
        const isDuplicate = existingLinks.some(l => 
          l.id !== id && this.normalizeUrl(l.url) === normalizedUrl
        )
        
        if (isDuplicate) {
          throw new Error('该外链平台已存在')
        }
      }

      // 处理更新数据
      const processedUpdates: Partial<ExternalLink> = { ...updates }
      
      if (processedUpdates.name) {
        processedUpdates.name = processedUpdates.name.trim()
      }
      
      if (processedUpdates.url) {
        processedUpdates.url = this.normalizeUrl(processedUpdates.url)
      }
      
      if (processedUpdates.submitUrl !== undefined) {
        processedUpdates.submitUrl = processedUpdates.submitUrl ? 
          this.normalizeUrl(processedUpdates.submitUrl) : undefined
      }
      
      if (processedUpdates.category !== undefined) {
        processedUpdates.category = processedUpdates.category?.trim()
      }
      
      if (processedUpdates.requirements) {
        processedUpdates.requirements = this.processRequirements(processedUpdates.requirements)
      }
      
      if (processedUpdates.notes !== undefined) {
        processedUpdates.notes = processedUpdates.notes?.trim()
      }

      const updatedLink = await storage.updateLink(id, processedUpdates)
      console.log('外链平台更新成功:', updatedLink.name)
      
      return updatedLink
    } catch (error) {
      console.error('更新外链平台失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('更新外链平台失败，请稍后重试')
    }
  }

  async deleteLink(id: string): Promise<void> {
    try {
      if (!id) {
        throw new Error('外链平台ID不能为空')
      }

      const storage = await this.getStorageService()
      
      // 检查外链是否存在
      const existingLinks = await storage.getLinks()
      const link = existingLinks.find(l => l.id === id)
      
      if (!link) {
        throw new Error('外链平台不存在')
      }

      await storage.deleteLink(id)
      console.log('外链平台删除成功:', link.name)
    } catch (error) {
      console.error('删除外链平台失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('删除外链平台失败，请稍后重试')
    }
  }

  async saveCurrentUrl(url: string): Promise<ExternalLink> {
    try {
      if (!url) {
        throw new Error('URL不能为空')
      }

      // 获取页面标题作为平台名称
      const title = await this.extractPageTitle(url)
      const platformName = title || this.extractDomainName(url)

      // 创建外链数据
      const linkData: CreateLinkDto = {
        name: platformName,
        url: url,
        isPaid: false, // 默认为免费
        category: '未分类',
        notes: `从浏览器保存于 ${new Date().toLocaleString()}`
      }

      return this.addLink(linkData)
    } catch (error) {
      console.error('保存当前网址失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('保存当前网址失败，请稍后重试')
    }
  }

  normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      
      // 移除www前缀
      let hostname = urlObj.hostname
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }
      
      // 统一使用https协议
      urlObj.protocol = 'https:'
      urlObj.hostname = hostname
      
      // 移除尾部斜杠
      if (urlObj.pathname.endsWith('/') && urlObj.pathname.length > 1) {
        urlObj.pathname = urlObj.pathname.slice(0, -1)
      }
      
      // 移除默认端口
      if (urlObj.port === '443' && urlObj.protocol === 'https:') {
        urlObj.port = ''
      }
      if (urlObj.port === '80' && urlObj.protocol === 'http:') {
        urlObj.port = ''
      }

      return urlObj.toString()
    } catch {
      throw new Error('URL格式不正确')
    }
  }

  async syncWithAPI(): Promise<void> {
    try {
      const storage = await this.getStorageService()
      await storage.syncWithAPI()
      console.log('外链数据同步成功')
    } catch (error) {
      console.error('外链数据同步失败:', error)
      throw new Error('数据同步失败，请检查网络连接')
    }
  }

  // 搜索和筛选
  async searchLinks(query: string): Promise<ExternalLink[]> {
    const links = await this.getLinks()
    
    if (!query.trim()) {
      return links
    }

    const searchTerm = query.toLowerCase().trim()
    
    return links.filter(link => 
      link.name.toLowerCase().includes(searchTerm) ||
      link.url.toLowerCase().includes(searchTerm) ||
      link.category?.toLowerCase().includes(searchTerm) ||
      link.notes?.toLowerCase().includes(searchTerm) ||
      link.requirements?.some(req => req.toLowerCase().includes(searchTerm))
    )
  }

  async getLinksByCategory(category: string): Promise<ExternalLink[]> {
    const links = await this.getLinks()
    return links.filter(link => link.category === category)
  }

  async getPaidLinks(): Promise<ExternalLink[]> {
    const links = await this.getLinks()
    return links.filter(link => link.isPaid)
  }

  async getFreeLinks(): Promise<ExternalLink[]> {
    const links = await this.getLinks()
    return links.filter(link => !link.isPaid)
  }

  // 统计信息
  async getLinkStats(): Promise<{
    total: number
    paid: number
    free: number
    byCategory: Record<string, number>
    recentlyAdded: ExternalLink[]
  }> {
    const links = await this.getLinks()
    
    const paid = links.filter(link => link.isPaid).length
    const free = links.length - paid
    
    const byCategory: Record<string, number> = {}
    links.forEach(link => {
      if (link.category) {
        byCategory[link.category] = (byCategory[link.category] || 0) + 1
      }
    })

    // 最近添加的外链（7天内）
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const recentlyAdded = links.filter(link => link.createdAt > sevenDaysAgo)

    return {
      total: links.length,
      paid,
      free,
      byCategory,
      recentlyAdded
    }
  }

  // 批量操作
  async addLinksFromUrls(urls: string[]): Promise<ExternalLink[]> {
    const results: ExternalLink[] = []
    const errors: string[] = []

    for (const url of urls) {
      try {
        const link = await this.saveCurrentUrl(url)
        results.push(link)
      } catch (error) {
        errors.push(`${url}: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }

    if (errors.length > 0) {
      console.warn('部分URL添加失败:', errors)
    }

    return results
  }

  // 数据验证
  private validateLinkData(link: CreateLinkDto): void {
    if (!link.name || link.name.trim().length === 0) {
      throw new Error('外链平台名称不能为空')
    }

    if (link.name.trim().length > 100) {
      throw new Error('外链平台名称不能超过100个字符')
    }

    if (!link.url || !this.isValidUrl(link.url)) {
      throw new Error('请提供有效的平台URL')
    }

    if (link.submitUrl && !this.isValidUrl(link.submitUrl)) {
      throw new Error('提交URL格式不正确')
    }

    if (link.notes && link.notes.trim().length > 1000) {
      throw new Error('备注不能超过1000个字符')
    }

    if (link.requirements && link.requirements.length > 50) {
      throw new Error('提交要求不能超过50项')
    }
  }

  // 工具方法
  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  private processRequirements(requirements: string[]): string[] {
    return [...new Set(
      requirements
        .map(req => req.trim())
        .filter(req => req.length > 0)
    )]
  }

  private async extractPageTitle(url: string): Promise<string | null> {
    // 在扩展环境中，我们可以通过content script获取页面标题
    // 这里先返回基于域名的默认名称
    return this.extractDomainName(url)
  }

  private extractDomainName(url: string): string {
    try {
      const urlObj = new URL(url)
      let hostname = urlObj.hostname

      // 移除www前缀
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }

      // 提取主域名
      const parts = hostname.split('.')
      if (parts.length >= 2) {
        return parts[parts.length - 2]
      }

      return hostname
    } catch {
      return '未知平台'
    }
  }
}