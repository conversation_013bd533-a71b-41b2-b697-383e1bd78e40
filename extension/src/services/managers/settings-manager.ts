import type { UserSettings } from '~/types'
import { Storage } from '@plasmohq/storage'
import { StorageFactory } from '../storage/factory'
import { getDefaultApiUrl } from '~/lib/config'

interface APIKeyConfig {
  apiKey: string
  apiUrl?: string
}

export class SettingsManager {
  private storage: Storage
  private storageFactory: StorageFactory

  constructor() {
    this.storage = new Storage({ area: 'local' })
    this.storageFactory = StorageFactory.getInstance()
  }

  // 获取设置 - 始终从本地存储获取
  async getSettings(): Promise<UserSettings> {
    try {
      const settings = await this.storage.get('settings') as Partial<UserSettings> | null
      return {
        ...this.getDefaultSettings(),
        ...(settings || {})
      }
    } catch (error) {
      console.error('获取设置失败:', error)
      return this.getDefaultSettings()
    }
  }

  // 更新设置 - 始终保存到本地存储
  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const currentSettings = await this.getSettings()
      const newSettings = { ...currentSettings, ...updates }
      
      await this.storage.set('settings', newSettings)
      return newSettings
    } catch (error) {
      console.error('更新设置失败:', error)
      throw error
    }
  }

  // 获取默认设置
  private getDefaultSettings(): UserSettings {
    return {
      isPaidUser: false,
      theme: 'system',
      language: 'zh-CN',
      autoOpenSidebar: false,
      aiOptimizationEnabled: false
    }
  }

  // API Key 配置
  async setApiKey(config: APIKeyConfig): Promise<boolean> {
    try {
      const apiUrl = config.apiUrl || getDefaultApiUrl()
      
      // 创建新的存储服务
      await this.storageFactory.switchToAPIStorage({
        apiKey: config.apiKey,
        apiUrl
      })

      // 更新用户类型
      await this.updateSettings({ isPaidUser: true, aiOptimizationEnabled: true })
      
      return true
    } catch (error) {
      console.error('设置API Key失败:', error)
      return false
    }
  }

  // 移除API Key配置
  async removeApiKey(): Promise<void> {
    try {
      // 切换到本地存储
      await this.storageFactory.switchToLocalStorage()
      
      // 更新用户类型
      await this.updateSettings({ isPaidUser: false, aiOptimizationEnabled: false })
    } catch (error) {
      console.error('移除API Key失败:', error)
      throw error
    }
  }

  // 获取API配置
  async getApiConfig(): Promise<{ apiKey?: string; apiUrl?: string } | null> {
    try {
      return await this.storage.get('apiConfig')
    } catch (error) {
      console.error('获取API配置失败:', error)
      return null
    }
  }

  // 验证API Key
  async validateApiKey(apiKey: string, apiUrl?: string): Promise<boolean> {
    const url = apiUrl || getDefaultApiUrl()
    const validateUrl = `${url}/extension/auth`
    
    console.log('[LinkTrackPro] 开始验证API Key...')
    console.log('[LinkTrackPro] 验证URL:', validateUrl)
    console.log('[LinkTrackPro] API Key长度:', apiKey.length)
    console.log('[LinkTrackPro] API Key前缀:', apiKey.substring(0, 8) + '...')
    
    // 验证API Key格式
    if (!this.validateApiKeyFormat(apiKey)) {
      console.error('[LinkTrackPro] API Key格式无效，包含非ASCII字符或格式错误')
      return false
    }
    
    try {
      const cleanApiKey = this.sanitizeApiKey(apiKey)
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${cleanApiKey}`
      }
      
      const requestBody = {
        action: 'validate'
      }
      
      console.log('[LinkTrackPro] 发送验证请求，Headers:', { ...headers, Authorization: 'Bearer ***' })
      console.log('[LinkTrackPro] 请求Body:', requestBody)
      
      const response = await fetch(validateUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      })

      console.log('[LinkTrackPro] 验证响应状态:', response.status)
      console.log('[LinkTrackPro] 验证响应状态文本:', response.statusText)
      
      if (!response.ok) {
        const responseText = await response.text()
        console.log('[LinkTrackPro] 验证失败响应内容:', responseText)
        
        // 尝试解析错误信息
        try {
          const errorData = JSON.parse(responseText)
          console.error('[LinkTrackPro] API验证错误详情:', errorData)
        } catch (parseError) {
          console.error('[LinkTrackPro] 无法解析错误响应，原始内容:', responseText)
        }
        
        return false
      }
      
      const responseData = await response.json()
      console.log('[LinkTrackPro] 验证成功响应:', responseData)
      
      // 检查响应中的success字段
      return !!(responseData && responseData.success)
    } catch (error) {
      console.error('[LinkTrackPro] 验证API Key时发生网络错误:', error)
      
      // 提供更详细的错误信息
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error('[LinkTrackPro] 网络连接错误，请检查:', {
          url: validateUrl,
          possibleIssues: [
            '1. API服务器未运行',
            '2. CORS配置问题',
            '3. 网络连接问题',
            '4. URL格式错误',
            '5. 扩展权限不足'
          ]
        })
      }
      
      return false
    }
  }

  // 数据迁移：从本地到API
  async migrateToAPI(config: APIKeyConfig): Promise<void> {
    try {
      const apiUrl = config.apiUrl || getDefaultApiUrl()
      await this.storageFactory.migrateFromLocalToAPI({
        apiKey: config.apiKey,
        apiUrl
      })
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  // 数据迁移：从API到本地
  async migrateToLocal(): Promise<void> {
    try {
      await this.storageFactory.migrateFromAPIToLocal()
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  // 重置所有配置
  async resetSettings(): Promise<void> {
    try {
      await this.storageFactory.resetStorage()
      await this.storage.clear()
    } catch (error) {
      console.error('重置设置失败:', error)
      throw error
    }
  }

  // 导出数据
  async exportData(): Promise<any> {
    try {
      const storageService = await this.storageFactory.getStorageService()
      
      const [projects, links, settings] = await Promise.all([
        storageService.getProjects(),
        storageService.getLinks(),
        this.getSettings() // 直接使用本地存储获取设置
      ])

      return {
        projects,
        links,
        settings,
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  // 导入数据
  async importData(data: any): Promise<void> {
    try {
      // 验证数据格式
      if (!data.projects || !data.links || !data.settings) {
        throw new Error('数据格式不正确')
      }

      const storageService = await this.storageFactory.getStorageService()

      // 清空现有数据（可选）
      // await this.resetSettings()

      // 导入项目
      for (const project of data.projects) {
        const { id, createdAt, updatedAt, ...projectData } = project
        await storageService.addProject(projectData)
      }

      // 导入外链
      for (const link of data.links) {
        const { id, createdAt, updatedAt, ...linkData } = link
        await storageService.addLink(linkData)
      }

      // 导入设置 - 直接使用本地存储
      await this.updateSettings(data.settings)

      console.log('数据导入完成')
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }

  // 清理API Key，确保只包含ASCII字符
  private sanitizeApiKey(apiKey: string): string {
    return apiKey.replace(/[^\x00-\x7F]/g, "").trim()
  }

  // 验证API Key格式
  private validateApiKeyFormat(apiKey: string): boolean {
    // 检查是否包含非ASCII字符
    if (!/^[\x00-\x7F]*$/.test(apiKey)) {
      return false
    }
    // 检查长度和基本格式
    return apiKey.length > 0 && apiKey.trim() === apiKey
  }
}