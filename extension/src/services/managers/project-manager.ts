import type { 
  Project, 
  CreateProjectDto, 
  ProjectManager as IProjectManager,
  StorageService 
} from '~/types'
import { StorageFactory } from '../storage'

export class ProjectManager implements IProjectManager {
  private storageService: StorageService | null = null
  private storageFactory: StorageFactory

  constructor() {
    this.storageFactory = StorageFactory.getInstance()
  }

  private async getStorageService(): Promise<StorageService> {
    if (!this.storageService) {
      this.storageService = await this.storageFactory.getStorageService()
    }
    return this.storageService
  }

  async getProjects(): Promise<Project[]> {
    try {
      const storage = await this.getStorageService()
      const projects = await storage.getProjects()
      
      // 按更新时间降序排列
      return projects.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    } catch (error) {
      console.error('获取项目列表失败:', error)
      throw new Error('获取项目列表失败，请稍后重试')
    }
  }

  async addProject(project: CreateProjectDto): Promise<Project> {
    try {
      this.validateProjectData(project)
      
      const storage = await this.getStorageService()
      
      // 检查项目名称是否重复
      const existingProjects = await storage.getProjects()
      const isDuplicate = existingProjects.some(p => 
        p.name.toLowerCase().trim() === project.name.toLowerCase().trim()
      )
      
      if (isDuplicate) {
        throw new Error('项目名称已存在，请使用不同的名称')
      }

      // 处理项目数据
      const processedProject = {
        ...project,
        name: project.name.trim(),
        domain: this.normalizeDomain(project.domain),
        info: project.info ? {
          introduction: project.info.introduction?.trim()
        } : undefined,
        screenshots: await this.processScreenshots(project.screenshots || [])
      }

      const newProject = await storage.addProject(processedProject)
      console.log('项目添加成功:', newProject.name)
      
      return newProject
    } catch (error) {
      console.error('添加项目失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('添加项目失败，请稍后重试')
    }
  }

  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {
    try {
      if (!id) {
        throw new Error('项目ID不能为空')
      }

      const storage = await this.getStorageService()
      
      // 检查项目是否存在
      const existingProjects = await storage.getProjects()
      const project = existingProjects.find(p => p.id === id)
      
      if (!project) {
        throw new Error('项目不存在')
      }

      // 如果更新了名称，检查是否重复
      if (updates.name && updates.name.trim() !== project.name) {
        const isDuplicate = existingProjects.some(p => 
          p.id !== id && p.name.toLowerCase().trim() === updates.name!.toLowerCase().trim()
        )
        
        if (isDuplicate) {
          throw new Error('项目名称已存在，请使用不同的名称')
        }
      }

      // 处理更新数据
      const processedUpdates: Partial<Project> = { ...updates }
      
      if (processedUpdates.name) {
        processedUpdates.name = processedUpdates.name.trim()
      }
      
      if (processedUpdates.domain) {
        processedUpdates.domain = this.normalizeDomain(processedUpdates.domain)
      }
      
      if (processedUpdates.info) {
        processedUpdates.info = {
          introduction: processedUpdates.info.introduction?.trim()
        }
      }
      
      if (processedUpdates.screenshots) {
        processedUpdates.screenshots = await this.processScreenshots(processedUpdates.screenshots)
      }

      const updatedProject = await storage.updateProject(id, processedUpdates)
      console.log('项目更新成功:', updatedProject.name)
      
      return updatedProject
    } catch (error) {
      console.error('更新项目失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('更新项目失败，请稍后重试')
    }
  }

  async deleteProject(id: string): Promise<void> {
    try {
      if (!id) {
        throw new Error('项目ID不能为空')
      }

      const storage = await this.getStorageService()
      
      // 检查项目是否存在
      const existingProjects = await storage.getProjects()
      const project = existingProjects.find(p => p.id === id)
      
      if (!project) {
        throw new Error('项目不存在')
      }

      await storage.deleteProject(id)
      console.log('项目删除成功:', project.name)
    } catch (error) {
      console.error('删除项目失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('删除项目失败，请稍后重试')
    }
  }

  async syncWithAPI(): Promise<void> {
    try {
      const storage = await this.getStorageService()
      await storage.syncWithAPI()
      console.log('项目数据同步成功')
    } catch (error) {
      console.error('项目数据同步失败:', error)
      throw new Error('数据同步失败，请检查网络连接')
    }
  }

  // 搜索和筛选
  async searchProjects(query: string): Promise<Project[]> {
    const projects = await this.getProjects()
    
    if (!query.trim()) {
      return projects
    }

    const searchTerm = query.toLowerCase().trim()
    
    return projects.filter(project => 
      project.name.toLowerCase().includes(searchTerm) ||
      project.info?.introduction?.toLowerCase().includes(searchTerm) ||
      project.domain.toLowerCase().includes(searchTerm) ||
      project.category?.toLowerCase().includes(searchTerm)
    )
  }

  async getProjectsByCategory(category: string): Promise<Project[]> {
    const projects = await this.getProjects()
    return projects.filter(project => project.category === category)
  }



  // 统计信息
  async getProjectStats(): Promise<{
    total: number
    byCategory: Record<string, number>
    recentlyUpdated: Project[]
  }> {
    const projects = await this.getProjects()
    
    const byCategory: Record<string, number> = {}
    
    projects.forEach(project => {
      // 分类统计
      if (project.category) {
        byCategory[project.category] = (byCategory[project.category] || 0) + 1
      }
    })

    // 最近更新的项目（7天内）
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const recentlyUpdated = projects.filter(project => project.updatedAt > sevenDaysAgo)

    return {
      total: projects.length,
      byCategory,
      recentlyUpdated
    }
  }

  // 数据验证
  private validateProjectData(project: CreateProjectDto): void {
    if (!project.name || project.name.trim().length === 0) {
      throw new Error('项目名称不能为空')
    }

    if (project.name.trim().length > 100) {
      throw new Error('项目名称不能超过100个字符')
    }

    if (!project.domain || !this.isValidDomain(project.domain)) {
      throw new Error('请提供有效的项目域名')
    }

    if (project.info?.introduction && project.info.introduction.trim().length > 5000) {
      throw new Error('项目简介不能超过5000个字符')
    }

    if (project.screenshots && project.screenshots.length > 10) {
      throw new Error('项目截图不能超过10张')
    }
  }

  // 工具方法
  private normalizeDomain(domain: string): string {
    // 移除协议前缀和路径
    let normalized = domain.toLowerCase().trim()
    normalized = normalized.replace(/^https?:\/\//, '')
    normalized = normalized.replace(/^www\./, '')
    normalized = normalized.split('/')[0]
    
    if (!this.isValidDomain(normalized)) {
      throw new Error('域名格式不正确')
    }
    
    return normalized
  }

  private isValidDomain(domain: string): boolean {
    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
    return domainRegex.test(domain)
  }

  private async processScreenshots(screenshots: string[]): Promise<string[]> {
    // 验证截图URL或base64数据
    const validScreenshots: string[] = []
    
    for (const screenshot of screenshots) {
      if (this.isValidScreenshotUrl(screenshot) || this.isValidBase64Image(screenshot)) {
        validScreenshots.push(screenshot)
      } else {
        console.warn('无效的截图数据，已跳过:', screenshot.substring(0, 50))
      }
    }
    
    return validScreenshots
  }

  private isValidScreenshotUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  private isValidBase64Image(data: string): boolean {
    return /^data:image\/(jpeg|jpg|png|gif|webp);base64,/.test(data)
  }


}