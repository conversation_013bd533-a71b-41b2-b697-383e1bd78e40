// 配置管理模块
export interface AppConfig {
  apiUrl: string
  timeout: number
  retryAttempts: number
  retryDelay: number
}

/**
 * 获取应用配置
 * 优先级：环境变量 > 默认值
 */
export function getAppConfig(): AppConfig {
  const defaultConfig: AppConfig = {
    apiUrl: 'https://api.linktrackpro.com',
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000
  }

  return {
    apiUrl: process.env.PLASMO_PUBLIC_API_URL || defaultConfig.apiUrl,
    timeout: parseInt(process.env.PLASMO_PUBLIC_API_TIMEOUT || String(defaultConfig.timeout)),
    retryAttempts: parseInt(process.env.PLASMO_PUBLIC_RETRY_ATTEMPTS || String(defaultConfig.retryAttempts)),
    retryDelay: parseInt(process.env.PLASMO_PUBLIC_RETRY_DELAY || String(defaultConfig.retryDelay))
  }
}

/**
 * 获取默认的 API URL
 */
export function getDefaultApiUrl(): string {
  return getAppConfig().apiUrl
}

/**
 * 验证 API URL 格式
 */
export function validateApiUrl(url: string): boolean {
  try {
    const parsed = new URL(url)
    return parsed.protocol === 'https:' || parsed.protocol === 'http:'
  } catch {
    return false
  }
}

/**
 * 格式化 API URL（确保没有尾部斜杠）
 */
export function formatApiUrl(url: string): string {
  return url.replace(/\/$/, '')
}