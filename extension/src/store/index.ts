import { create } from 'zustand'
import type { 
  Project, 
  ExternalLink, 
  UserSettings, 
  FormElement 
} from '~/types'
import { ProjectManager, LinkManager } from '~/services/managers'
import { SettingsManager } from '~/services/managers/settings-manager'
import { AIService } from '~/services/ai'
import { StorageFactory } from '~/services/storage'
import { errorHandler, showToast, type Toast, type AppError } from '~/lib/error-handler'

interface AppState {
  // 用户设置
  settings: UserSettings
  isLoading: boolean
  error: string | null

  // 项目管理
  projects: Project[]
  selectedProject: Project | null
  projectsLoading: boolean

  // 外链管理
  links: ExternalLink[]
  selectedLink: ExternalLink | null
  linksLoading: boolean

  // 表单检测
  detectedForms: any[]
  selectedForm: any | null
  formDetectionLoading: boolean

  // AI优化
  aiService: AIService | null
  aiOptimizing: boolean

  // 错误和通知管理
  toasts: Toast[]
  errors: AppError[]

  // UI状态
  currentView: 'dashboard' | 'projects' | 'links' | 'forms' | 'settings'
  sidebarVisible: boolean

  // Actions
  initializeApp: () => Promise<void>
  
  // 设置相关
  updateSettings: (updates: Partial<UserSettings>) => Promise<void>
  
  // 项目相关
  loadProjects: () => Promise<void>
  addProject: (project: any) => Promise<void>
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  selectProject: (project: Project | null) => void
  
  // 外链相关
  loadLinks: () => Promise<void>
  addLink: (link: any) => Promise<void>
  updateLink: (id: string, updates: Partial<ExternalLink>) => Promise<void>
  deleteLink: (id: string) => Promise<void>
  selectLink: (link: ExternalLink | null) => void
  saveCurrentUrl: () => Promise<void>
  
  // 表单相关
  detectForms: () => Promise<void>
  selectForm: (form: any | null) => void
  fillForm: (formIndex: number, project: Project, mapping?: any) => Promise<any>
  ensureContentScriptLoaded: (tabId: number) => Promise<void>
  
  // AI相关
  optimizeContent: (content: string, context: any) => Promise<string>
  optimizeWithSeparatedContent: (content: string, instruction: string) => Promise<string>
  
  // UI相关
  setCurrentView: (view: AppState['currentView']) => void
  setSidebarVisible: (visible: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  
  // 错误和通知管理
  addToast: (toast: Omit<Toast, 'id'>) => string
  removeToast: (id: string) => void
  clearAllToasts: () => void
  handleError: (error: any, context?: string) => AppError
  retryError: (errorId: string, retryFn: () => Promise<any>) => Promise<boolean>
  clearAllErrors: () => void
}

export const useAppStore = create<AppState>((set, get) => {
  const projectManager = new ProjectManager()
  const linkManager = new LinkManager()
  const settingsManager = new SettingsManager()
  let aiService: AIService | null = null

  // 初始化错误处理监听器
  const errorUnsubscribe = errorHandler.onError((error: AppError) => {
    set(state => ({ errors: [...state.errors, error] }))
  })

  const toastUnsubscribe = errorHandler.onToast((toast: Toast) => {
    set(state => ({ toasts: [...state.toasts, toast] }))
  })

  return {
    // 初始状态
    settings: {
      isPaidUser: false,
      theme: 'system',
      language: 'zh-CN',
      autoOpenSidebar: false,
      aiOptimizationEnabled: true
    },
    isLoading: false,
    error: null,

    projects: [],
    selectedProject: null,
    projectsLoading: false,

    links: [],
    selectedLink: null,
    linksLoading: false,

    detectedForms: [],
    selectedForm: null,
    formDetectionLoading: false,

    aiService: null,
    aiOptimizing: false,

    toasts: [],
    errors: [],

    currentView: 'dashboard',
    sidebarVisible: true,

    // Actions
    initializeApp: async () => {
      set({ isLoading: true, error: null })
      
      try {
        // 初始化存储服务
        const storageFactory = StorageFactory.getInstance()
        const storage = await storageFactory.getStorageService()
        
        // 加载用户设置
        const settings = await storage.getSettings()
        set({ settings })

        // 初始化AI服务
        aiService = new AIService()
        set({ aiService })

        // 并行加载项目和外链数据
        await Promise.all([
          get().loadProjects(),
          get().loadLinks()
        ])

        console.log('应用初始化完成')
      } catch (error) {
        console.error('应用初始化失败:', error)
        set({ error: error instanceof Error ? error.message : '初始化失败' })
      } finally {
        set({ isLoading: false })
      }
    },

    // 设置相关
    updateSettings: async (updates) => {
      try {
        const newSettings = await settingsManager.updateSettings(updates)
        set({ settings: newSettings })
        
        // 如果更新了用户类型，重新初始化服务
        if (updates.isPaidUser !== undefined) {
          aiService = new AIService()
          set({ aiService })
        }
        
        showToast.success('设置已保存')
      } catch (error) {
        get().handleError(error, 'settings')
        throw error
      }
    },

    // 项目相关
    loadProjects: async () => {
      set({ projectsLoading: true })
      
      try {
        const projects = await projectManager.getProjects()
        set({ projects })
      } catch (error) {
        console.error('加载项目失败:', error)
        set({ error: error instanceof Error ? error.message : '加载项目失败' })
      } finally {
        set({ projectsLoading: false })
      }
    },

    addProject: async (projectData) => {
      try {
        const newProject = await projectManager.addProject(projectData)
        set(state => ({ 
          projects: [newProject, ...state.projects],
          selectedProject: newProject
        }))
      } catch (error) {
        console.error('添加项目失败:', error)
        set({ error: error instanceof Error ? error.message : '添加项目失败' })
        throw error
      }
    },

    updateProject: async (id, updates) => {
      try {
        const updatedProject = await projectManager.updateProject(id, updates)
        set(state => ({
          projects: state.projects.map(p => p.id === id ? updatedProject : p),
          selectedProject: state.selectedProject?.id === id ? updatedProject : state.selectedProject
        }))
      } catch (error) {
        console.error('更新项目失败:', error)
        set({ error: error instanceof Error ? error.message : '更新项目失败' })
        throw error
      }
    },

    deleteProject: async (id) => {
      try {
        await projectManager.deleteProject(id)
        set(state => ({
          projects: state.projects.filter(p => p.id !== id),
          selectedProject: state.selectedProject?.id === id ? null : state.selectedProject
        }))
      } catch (error) {
        console.error('删除项目失败:', error)
        set({ error: error instanceof Error ? error.message : '删除项目失败' })
        throw error
      }
    },

    selectProject: (project) => {
      set({ selectedProject: project })
    },

    // 外链相关
    loadLinks: async () => {
      set({ linksLoading: true })
      
      try {
        const links = await linkManager.getLinks()
        set({ links })
      } catch (error) {
        console.error('加载外链失败:', error)
        set({ error: error instanceof Error ? error.message : '加载外链失败' })
      } finally {
        set({ linksLoading: false })
      }
    },

    addLink: async (linkData) => {
      try {
        const newLink = await linkManager.addLink(linkData)
        set(state => ({ 
          links: [newLink, ...state.links],
          selectedLink: newLink
        }))
      } catch (error) {
        console.error('添加外链失败:', error)
        set({ error: error instanceof Error ? error.message : '添加外链失败' })
        throw error
      }
    },

    updateLink: async (id, updates) => {
      try {
        const updatedLink = await linkManager.updateLink(id, updates)
        set(state => ({
          links: state.links.map(l => l.id === id ? updatedLink : l),
          selectedLink: state.selectedLink?.id === id ? updatedLink : state.selectedLink
        }))
      } catch (error) {
        console.error('更新外链失败:', error)
        set({ error: error instanceof Error ? error.message : '更新外链失败' })
        throw error
      }
    },

    deleteLink: async (id) => {
      try {
        await linkManager.deleteLink(id)
        set(state => ({
          links: state.links.filter(l => l.id !== id),
          selectedLink: state.selectedLink?.id === id ? null : state.selectedLink
        }))
      } catch (error) {
        console.error('删除外链失败:', error)
        set({ error: error instanceof Error ? error.message : '删除外链失败' })
        throw error
      }
    },

    selectLink: (link) => {
      set({ selectedLink: link })
    },

    saveCurrentUrl: async () => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tab.url) {
          const newLink = await linkManager.saveCurrentUrl(tab.url)
          set(state => ({ 
            links: [newLink, ...state.links],
            selectedLink: newLink
          }))
        }
      } catch (error) {
        console.error('保存当前网址失败:', error)
        set({ error: error instanceof Error ? error.message : '保存当前网址失败' })
        throw error
      }
    },

    // 表单相关
    detectForms: async () => {
      set({ formDetectionLoading: true, error: null })
      
      try {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (!tab.id) throw new Error('无法获取当前标签页')

        console.log('[Store] 开始检测表单，标签页ID:', tab.id)
        console.log('[Store] 标签页URL:', tab.url)

        // 检查页面是否支持表单检测
        if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
          throw new Error('当前页面不支持表单检测')
        }

        // 确保content script已加载
        try {
          await get().ensureContentScriptLoaded(tab.id)
        } catch (error) {
          console.error('[Store] Content script加载失败:', error)
          throw new Error('内容脚本加载失败，请刷新页面后重试')
        }

        // 发送表单检测消息
        console.log('[Store] 发送表单检测消息')
        const response = await new Promise<any>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('表单检测超时，请刷新页面后重试'))
          }, 15000) // 增加超时时间到15秒

          chrome.tabs.sendMessage(tab.id!, { type: 'DETECT_FORMS' }, (response) => {
            clearTimeout(timeout)
            if (chrome.runtime.lastError) {
              const error = chrome.runtime.lastError.message
              console.error('[Store] 消息发送失败:', error)
              
              if (error.includes('Could not establish connection')) {
                reject(new Error('无法连接到页面内容脚本，请刷新页面后重试'))
              } else if (error.includes('Receiving end does not exist')) {
                reject(new Error('页面内容脚本未响应，请刷新页面后重试'))
              } else if (error.includes('Extension context invalidated')) {
                reject(new Error('扩展上下文已失效，请重新加载扩展'))
              } else {
                reject(new Error(`消息传递失败: ${error}`))
              }
            } else {
              resolve(response)
            }
          })
        })

        if (response && response.success) {
          console.log('[Store] 表单检测成功，发现', response.forms?.length || 0, '个表单')
          set({ detectedForms: response.forms || [], error: null })
        } else {
          const errorMsg = response?.error || '表单检测返回失败状态'
          console.error('[Store] 表单检测失败:', errorMsg)
          throw new Error(errorMsg)
        }
      } catch (error) {
        console.error('[Store] 检测表单失败:', error)
        const errorMessage = error instanceof Error ? error.message : '检测表单失败'
        set({ error: errorMessage, detectedForms: [] })
        throw error
      } finally {
        set({ formDetectionLoading: false })
      }
    },

    selectForm: (form) => {
      set({ selectedForm: form })
    },

    fillForm: async (formIndex, project, mapping) => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (!tab.id) throw new Error('无法获取当前标签页')

        const response = await chrome.tabs.sendMessage(tab.id, {
          type: 'FILL_FORM',
          formIndex,
          project,
          mapping
        })

        if (response.success) {
          return response.result
        } else {
          throw new Error(response.error)
        }
      } catch (error) {
        console.error('填充表单失败:', error)
        set({ error: error instanceof Error ? error.message : '填充表单失败' })
        throw error
      }
    },

    ensureContentScriptLoaded: async (tabId: number) => {
      try {
        console.log('[Store] 检查content script状态，tabId:', tabId)
        
        // 尝试ping content script
        const pingResult = await new Promise<boolean>((resolve) => {
          const timeout = setTimeout(() => {
            console.log('[Store] Content script ping超时')
            resolve(false)
          }, 3000)

          chrome.tabs.sendMessage(tabId, { type: 'PING' }, (response) => {
            clearTimeout(timeout)
            if (chrome.runtime.lastError) {
              console.log('[Store] Content script未响应:', chrome.runtime.lastError.message)
              resolve(false)
            } else if (response && response.success) {
              console.log('[Store] Content script已响应')
              resolve(true)
            } else {
              console.log('[Store] Content script响应无效')
              resolve(false)
            }
          })
        })

        if (!pingResult) {
          console.log('[Store] Content script需要注入')
          
          // 动态获取content script文件名
          const contentScriptFile = await this.findContentScriptFile()
          
          if (!contentScriptFile) {
            throw new Error('无法找到content script文件')
          }

          console.log(`[Store] 尝试注入content script: ${contentScriptFile}`)
          
          await chrome.scripting.executeScript({
            target: { tabId },
            files: [contentScriptFile]
          })
          
          // 等待content script初始化
          await new Promise(resolve => setTimeout(resolve, 2000))
          
          // 验证注入是否成功
          const verifyResult = await new Promise<boolean>((resolve) => {
            const timeout = setTimeout(() => resolve(false), 3000)
            
            chrome.tabs.sendMessage(tabId, { type: 'PING' }, (response) => {
              clearTimeout(timeout)
              resolve(!chrome.runtime.lastError && response && response.success)
            })
          })
          
          if (!verifyResult) {
            throw new Error('Content script注入后验证失败')
          }
          
          console.log('[Store] Content script注入并验证成功')
        }
      } catch (error) {
        console.warn('[Store] 确保content script加载失败:', error)
        throw error
      }
    },

    // 动态查找content script文件
    findContentScriptFile: async () => {
      try {
        // 获取扩展根URL
        const extensionUrl = chrome.runtime.getURL('')
        console.log('[Store] 扩展根URL:', extensionUrl)
        
        // 尝试不同的可能文件名模式
        const possiblePatterns = [
          'form-detector.*.js', // 带哈希的文件名
          'form-detector.js',   // 不带哈希的文件名
        ]
        
        // 通过fetch测试文件是否存在
        for (const pattern of possiblePatterns) {
          if (pattern.includes('*')) {
            // 对于包含通配符的模式，我们需要列出目录内容
            // 但由于安全限制，我们无法直接列目录
            // 所以我们尝试常见的哈希模式
            const commonHashes = [
              '9a4bfd0c', // 当前构建的哈希
              'latest',
              'bundle',
            ]
            
            for (const hash of commonHashes) {
              const fileName = pattern.replace('*', hash)
              try {
                const response = await fetch(chrome.runtime.getURL(fileName))
                if (response.ok) {
                  console.log(`[Store] 找到content script文件: ${fileName}`)
                  return fileName
                }
              } catch (error) {
                // 继续尝试下一个
              }
            }
          } else {
            try {
              const response = await fetch(chrome.runtime.getURL(pattern))
              if (response.ok) {
                console.log(`[Store] 找到content script文件: ${pattern}`)
                return pattern
              }
            } catch (error) {
              // 继续尝试下一个
            }
          }
        }
        
        console.error('[Store] 无法找到有效的content script文件')
        return null
      } catch (error) {
        console.error('[Store] 查找content script文件失败:', error)
        return null
      }
    },

    // AI相关
    optimizeContent: async (content, context) => {
      if (!aiService) {
        throw new Error('AI服务未初始化')
      }

      set({ aiOptimizing: true })
      
      try {
        const optimized = await aiService.optimizeContent(content, context)
        return optimized
      } catch (error) {
        console.error('内容优化失败:', error)
        set({ error: error instanceof Error ? error.message : '内容优化失败' })
        throw error
      } finally {
        set({ aiOptimizing: false })
      }
    },

    optimizeWithSeparatedContent: async (content, instruction) => {
      if (!aiService) {
        throw new Error('AI服务未初始化')
      }

      set({ aiOptimizing: true })
      
      try {
        const optimized = await aiService.optimizeWithSeparatedContent(content, instruction)
        return optimized
      } catch (error) {
        console.error('AI分离内容优化失败:', error)
        set({ error: error instanceof Error ? error.message : 'AI分离内容优化失败' })
        throw error
      } finally {
        set({ aiOptimizing: false })
      }
    },

    // UI相关
    setCurrentView: (view) => {
      set({ currentView: view })
    },

    setSidebarVisible: (visible) => {
      set({ sidebarVisible: visible })
    },

    setError: (error) => {
      set({ error })
    },

    clearError: () => {
      set({ error: null })
    },

    // 错误和通知管理
    addToast: (toast) => {
      return errorHandler.showToast(toast)
    },

    removeToast: (id) => {
      set(state => ({ toasts: state.toasts.filter(t => t.id !== id) }))
    },

    clearAllToasts: () => {
      set({ toasts: [] })
    },

    handleError: (error, context) => {
      return errorHandler.handleError(error, context)
    },

    retryError: async (errorId, retryFn) => {
      return await errorHandler.retryError(errorId, retryFn)
    },

    clearAllErrors: () => {
      errorHandler.clearAllErrors()
      set({ errors: [] })
    }
  }
})