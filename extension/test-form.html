<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkTrackPro 表单检测测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .required {
            color: red;
        }
        h1, h2 {
            color: #333;
        }
        .test-info {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🧪 LinkTrackPro 表单检测测试页面</h1>
        <p>这个页面包含多种表单，用于测试 LinkTrackPro 扩展的表单检测功能。</p>
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>确保 LinkTrackPro 扩展已加载</li>
            <li>打开扩展的侧边栏或弹窗</li>
            <li>点击"检测表单"按钮</li>
            <li>查看是否正确识别了下面的表单</li>
        </ol>
    </div>

    <!-- 项目提交表单 -->
    <div class="form-container">
        <h2>🚀 项目提交表单</h2>
        <p>这是一个典型的项目提交表单，应该被扩展正确识别为高置信度表单。</p>
        
        <form action="/submit-project" method="post" id="project-form">
            <div class="form-group">
                <label for="project-name">项目名称 <span class="required">*</span></label>
                <input type="text" id="project-name" name="project_name" required placeholder="请输入您的项目名称">
            </div>
            
            <div class="form-group">
                <label for="project-url">项目链接 <span class="required">*</span></label>
                <input type="url" id="project-url" name="project_url" required placeholder="https://your-project.com">
            </div>
            
            <div class="form-group">
                <label for="project-description">项目描述 <span class="required">*</span></label>
                <textarea id="project-description" name="project_description" required placeholder="请详细描述您的项目..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="project-category">项目类别</label>
                <select id="project-category" name="project_category">
                    <option value="">请选择项目类别</option>
                    <option value="web-app">网页应用</option>
                    <option value="mobile-app">移动应用</option>
                    <option value="desktop-app">桌面应用</option>
                    <option value="tool">工具/插件</option>
                    <option value="game">游戏</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="submitter-email">联系邮箱 <span class="required">*</span></label>
                <input type="email" id="submitter-email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="submitter-name">提交者姓名</label>
                <input type="text" id="submitter-name" name="name" placeholder="您的姓名">
            </div>
            
            <button type="submit">🚀 提交项目</button>
        </form>
    </div>

    <!-- 联系表单 -->
    <div class="form-container">
        <h2>📧 联系我们</h2>
        <p>这是一个联系表单，应该被识别为联系类别。</p>
        
        <form action="/contact" method="post" id="contact-form">
            <div class="form-group">
                <label for="contact-name">姓名 <span class="required">*</span></label>
                <input type="text" id="contact-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="contact-email">邮箱 <span class="required">*</span></label>
                <input type="email" id="contact-email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="contact-subject">主题</label>
                <input type="text" id="contact-subject" name="subject">
            </div>
            
            <div class="form-group">
                <label for="contact-message">消息 <span class="required">*</span></label>
                <textarea id="contact-message" name="message" required placeholder="请输入您的消息..."></textarea>
            </div>
            
            <button type="submit">📧 发送消息</button>
        </form>
    </div>

    <!-- 简单搜索表单 -->
    <div class="form-container">
        <h2>🔍 搜索</h2>
        <p>这是一个简单的搜索表单，应该被识别为较低置信度。</p>
        
        <form action="/search" method="get" id="search-form">
            <div class="form-group">
                <label for="search-query">搜索关键词</label>
                <input type="search" id="search-query" name="q" placeholder="输入搜索关键词...">
            </div>
            
            <button type="submit">🔍 搜索</button>
        </form>
    </div>

    <!-- 没有form标签的"虚拟表单" -->
    <div class="form-container">
        <h2>💡 虚拟表单 (无 &lt;form&gt; 标签)</h2>
        <p>这些输入字段没有包装在form标签中，测试深度扫描功能。</p>
        
        <div id="virtual-form">
            <div class="form-group">
                <label for="virtual-name">产品名称</label>
                <input type="text" id="virtual-name" name="product_name" placeholder="输入产品名称">
            </div>
            
            <div class="form-group">
                <label for="virtual-price">价格</label>
                <input type="number" id="virtual-price" name="price" placeholder="0.00">
            </div>
            
            <div class="form-group">
                <label for="virtual-description">产品描述</label>
                <textarea id="virtual-description" name="description" placeholder="描述您的产品..."></textarea>
            </div>
            
            <button type="button" onclick="alert('这是一个虚拟提交按钮')">💫 提交产品</button>
        </div>
    </div>

    <div class="test-info">
        <h3>🎯 预期检测结果</h3>
        <ul>
            <li><strong>项目提交表单</strong>：高置信度，应识别为 "submission" 类别</li>
            <li><strong>联系表单</strong>：中等置信度，应识别为 "contact" 类别</li>
            <li><strong>搜索表单</strong>：低置信度，但仍应被检测到</li>
            <li><strong>虚拟表单</strong>：如果启用深度扫描，应该被检测到</li>
        </ul>
        
        <p><strong>测试成功指标：</strong></p>
        <ul>
            <li>✅ 控制台没有 "Extension context invalidated" 错误</li>
            <li>✅ 能够成功检测到至少3个表单</li>
            <li>✅ 项目提交表单的置信度 > 0.8</li>
            <li>✅ 表单字段语义识别正确</li>
            <li>✅ 高亮功能正常工作</li>
        </ul>
    </div>

    <script>
        // 添加一些动态行为来测试监控功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成')
            
            // 模拟动态添加表单字段
            setTimeout(() => {
                const dynamicGroup = document.createElement('div')
                dynamicGroup.className = 'form-group'
                dynamicGroup.innerHTML = `
                    <label for="dynamic-field">动态添加的字段</label>
                    <input type="text" id="dynamic-field" name="dynamic" placeholder="这是动态添加的字段">
                `
                document.getElementById('project-form').insertBefore(dynamicGroup, document.querySelector('#project-form button'))
                console.log('动态添加了一个表单字段')
            }, 3000)
        })
    </script>
</body>
</html>